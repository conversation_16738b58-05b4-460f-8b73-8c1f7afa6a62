package com.mes.common.core.constant;

/**
 * 服务名称
 *
 * <AUTHOR>
 */
public class ServiceNameConstants {
    /**
     * 认证服务的serviceid
     */
    public static final String AUTH_SERVICE = "mes-auth";

    /**
     * 系统模块的serviceid
     */
    public static final String SYSTEM_SERVICE = "mes-system";

    /**
     * 文件服务的serviceid
     */
    public static final String FILE_SERVICE = "mes-file";

    /**
     * flowable的serviceid
     */
    public static final String FLOWABLE = "mes-flowable";

    /**
     * monitorwarn的serviceid
     */
    public static final String MONITORWARN = "mes-monitorwarn";

    /**
     * 资源管理的serviceid
     */
    public static final String RESOURCE = "mes-resourcemanage";

    /**
     * 智能调度的serviceid
     */
    public static final String SMARTDISPATCH = "mes-smartdispatch";

    /**
     * 活动监测的serviceid
     */
    public static final String ACTIVITY_MONITORING = "mes-activity-monitoring";

    /**
     * 数据接入的serviceid
     */
    public static final String DATA_ACCESS = "mes-dataaccess";
}
