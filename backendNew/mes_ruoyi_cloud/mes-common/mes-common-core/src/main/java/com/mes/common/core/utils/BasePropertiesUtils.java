package com.mes.common.core.utils;

import java.util.Properties;

/**
 * <AUTHOR>
 * @since 2024/10/24 9:50
 */
public class BasePropertiesUtils {

    public static Properties properties = null;

    static {
        try {
            properties = new Properties();
            //这里用的jdbc.properties配置文件
            properties.load(BasePropertiesUtils.class.getClassLoader().getResourceAsStream("base.properties"));

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("未找到配置文件，使用 base.properties！！");
        }
    }

    /**
     * 获取配置
     *
     * @param key
     * @return
     */
    public static String getString(String key) {
        return properties.getProperty(key);
    }

    /**
     * 获取配置
     *
     * @param key
     * @return
     */
    public static String getString(String key, String defaultValue) {
        String value = defaultValue;
        try {
            value = properties.getProperty(key);
        } catch (Exception e) {
            System.out.println("未找到配置项:" + key);
        }
        return value;
    }

}
