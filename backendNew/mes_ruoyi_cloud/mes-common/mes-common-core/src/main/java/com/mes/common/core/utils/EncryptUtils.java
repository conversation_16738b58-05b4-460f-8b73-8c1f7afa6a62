package com.mes.common.core.utils;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.util.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Base64;

public class EncryptUtils {


    /**
     * 指定加密算法
     */
    private static final String ALGORITHM_NAME = "SM4";

    /**
     * BC中SM4默认使用ECB模式和PKCS5Padding填充方式
     */
    private static final String ALGORITHM_ECB_PKCS5PADDING = "SM4/ECB/PKCS5Padding";

    /**
     * 默认国密sm4 key值，128bit=32位16进制字符串
     */
    public static final String SM4_HEX_KEY_ID_CARD = "A7C9D1E8D93E6CJD7A175D1605598B1E";

    /**
     * SM4算法目前只支持128位（即密钥16字节）
     */
    private static final int DEFAULT_KEY_SIZE = 128;

    static {
        // 防止内存中出现多次BouncyCastleProvider的实例
        if (null == Security.getProvider(BouncyCastleProvider.PROVIDER_NAME)) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    public static String md5(String str) {
        return DigestUtils.md5DigestAsHex(str.getBytes());
    }

    /**
     * SM4对称加解密  加密
     *
     * @param plainString 待加密的字符串
     * @param key         密钥
     * @return 密文
     */
    public static String sm4Encrypt(String plainString, String key) {
        String cipherString = null;
        try {
            // 创建密钥规范
            SecretKeySpec secretKeySpec = new SecretKeySpec(hexStringToBytes(key), ALGORITHM_NAME);
            // 获取Cipher对象实例
            Cipher cipher = Cipher.getInstance(ALGORITHM_ECB_PKCS5PADDING, BouncyCastleProvider.PROVIDER_NAME);
            // 初始化Cipher为加密模式
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
            // 获取加密byte数组
            byte[] cipherBytes = cipher.doFinal(plainString.getBytes(StandardCharsets.UTF_8));
            // 输出为Base64编码
            cipherString = Base64.getEncoder().encodeToString(cipherBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cipherString;
    }

    /**
     * SM4对称加解密 解密
     *
     * @param cipherString 密文
     * @param key          密钥
     * @return 明文
     */
    public static String sm4Decrypt(String cipherString, String key) {
        String plainString = null;
        try {
            // 创建密钥规范
            SecretKeySpec secretKeySpec = new SecretKeySpec(hexStringToBytes(key), ALGORITHM_NAME);
            // 获取Cipher对象实例
            Cipher cipher = Cipher.getInstance(ALGORITHM_ECB_PKCS5PADDING, BouncyCastleProvider.PROVIDER_NAME);
            // 初始化Cipher为解密模式
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            // 获取加密byte数组
            byte[] cipherBytes = cipher.doFinal(Base64.getDecoder().decode(cipherString));
            // 输出为字符串
            plainString = new String(cipherBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return plainString;
    }


    public static byte[] hexStringToBytes(String hexString) {
        if (hexString != null && !hexString.equals("")) {
            hexString = hexString.toUpperCase();
            if (hexString.length() % 2 != 0) {
                hexString = "0" + hexString;
            }

            int length = hexString.length() / 2;
            char[] hexChars = hexString.toCharArray();
            byte[] d = new byte[length];

            for (int i = 0; i < length; ++i) {
                int pos = i * 2;
                d[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
            }

            return d;
        } else {
            return null;
        }
    }

    /**
     * 身份证加密
     *
     * @param idCard 身份证号
     * @return 只显示前3位和后三位
     */
    public static String showIdCard(String idCard) {
        if (!StringUtils.isEmpty(idCard)) {
            return idCard;
        }
        return idCard.substring(0, 3) + "************" + idCard.substring(idCard.length() - 3);
    }

    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

//    public static void main(String[] args) throws Exception {
//
//        String data = "3214124";
//        String encrypt = EncryptUtils.sm4Encrypt(data, SM4_HEX_KEY_ID_CARD);
//        System.out.println("使用SM4开源包加密后：" + encrypt);
//        String decrypt = EncryptUtils.sm4Decrypt(encrypt, SM4_HEX_KEY_ID_CARD);
//        System.out.println("使用SM4开源包解密后：" + decrypt);
//        System.out.println("使用SM4开源包解密后：" + "410************814");
//
//    }
}
