package com.mes.common.core.constant;

/**
 * 缓存常量信息
 *
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源管理字典 cache key
     */
    public static final String RESOURCE_DICT_KEY = "resource_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";

    /**
     * 点位表缓存key
     */
    public final static String TB_RES_SITE_DATA = "tb_res_site_data";

    /**
     * 设备表缓存key
     */
    public final static String TB_RES_DEVICE_DATA = "tb_res_device_data";

    /**
     * 监控基础参数缓存key
     */
    public final static String TB_MON_BASE_PARAM_DATA = "tb_mon_base_param_data";

    /**
     * 监控基础参数缓存key
     */
    public final static String TB_MON_MONITOR_DATA = "tb_mon_monitor_data";


}
