package com.mes.flowable.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mes.common.core.web.domain.BaseEntity;

import java.util.Date;

/**
 * <p>工作流任务<p>
 * 工作流任务相关-返回参数
 *
 * <AUTHOR>
 */
public class FlowTaskDto extends BaseEntity {

    private static final long serialVersionUID = 7223678899590929865L;

    //租户标识
    private String tenantId;

    //任务编号
    private String taskId;

    //任务执行编号
    private String executionId;

    //任务名称
    private String taskName;

    //任务Key
    private String taskDefKey;

    //任务执行人Id
    private Long assigneeId;

    //部门名称
    private String deptName;

    //流程发起人部门名称
    private String startDeptName;

    //任务执行人名称
    private String assigneeName;

    //流程发起人Id
    private String startUserId;

    //流程发起人名称
    private String startUserName;

    //流程类型
    private String category;

    //流程变量信息
    private Object procVars;

    //局部变量信息
    private Object taskLocalVars;

    //流程部署编号
    private String deployId;

    //流程ID
    private String procDefId;

    //流程key
    private String procDefKey;

    //流程定义名称
    private String procDefName;

    //流程标题
    private String processTitle;

    //流程定义内置使用版本
    private int procDefVersion;

    //流程实例ID
    private String procInsId;

    //历史流程实例ID
    private String hisProcInsId;

    //任务耗时
    private String duration;

    //任务意见
    private FlowCommentDto comment;

    //候选执行人
    private String candidate;

    //任务完成时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh_CN", timezone = "GMT+8")
    private Date finishTime;

    //表单ID
    private Long formId;

    //是否完成
    private Integer markCompleted;

    public Integer getMarkCompleted() {
        return markCompleted;
    }

    public void setMarkCompleted(Integer markCompleted) {
        this.markCompleted = markCompleted;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getProcessTitle() {
        return processTitle;
    }

    public void setProcessTitle(String processTitle) {
        this.processTitle = processTitle;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getExecutionId() {
        return executionId;
    }

    public void setExecutionId(String executionId) {
        this.executionId = executionId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskDefKey() {
        return taskDefKey;
    }

    public void setTaskDefKey(String taskDefKey) {
        this.taskDefKey = taskDefKey;
    }

    public Long getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(Long assigneeId) {
        this.assigneeId = assigneeId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getStartDeptName() {
        return startDeptName;
    }

    public void setStartDeptName(String startDeptName) {
        this.startDeptName = startDeptName;
    }

    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public String getStartUserId() {
        return startUserId;
    }

    public void setStartUserId(String startUserId) {
        this.startUserId = startUserId;
    }

    public String getStartUserName() {
        return startUserName;
    }

    public void setStartUserName(String startUserName) {
        this.startUserName = startUserName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Object getProcVars() {
        return procVars;
    }

    public void setProcVars(Object procVars) {
        this.procVars = procVars;
    }

    public Object getTaskLocalVars() {
        return taskLocalVars;
    }

    public void setTaskLocalVars(Object taskLocalVars) {
        this.taskLocalVars = taskLocalVars;
    }

    public String getDeployId() {
        return deployId;
    }

    public void setDeployId(String deployId) {
        this.deployId = deployId;
    }

    public String getProcDefId() {
        return procDefId;
    }

    public void setProcDefId(String procDefId) {
        this.procDefId = procDefId;
    }

    public String getProcDefKey() {
        return procDefKey;
    }

    public void setProcDefKey(String procDefKey) {
        this.procDefKey = procDefKey;
    }

    public String getProcDefName() {
        return procDefName;
    }

    public void setProcDefName(String procDefName) {
        this.procDefName = procDefName;
    }

    public int getProcDefVersion() {
        return procDefVersion;
    }

    public void setProcDefVersion(int procDefVersion) {
        this.procDefVersion = procDefVersion;
    }

    public String getProcInsId() {
        return procInsId;
    }

    public void setProcInsId(String procInsId) {
        this.procInsId = procInsId;
    }

    public String getHisProcInsId() {
        return hisProcInsId;
    }

    public void setHisProcInsId(String hisProcInsId) {
        this.hisProcInsId = hisProcInsId;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public FlowCommentDto getComment() {
        return comment;
    }

    public void setComment(FlowCommentDto comment) {
        this.comment = comment;
    }

    public String getCandidate() {
        return candidate;
    }

    public void setCandidate(String candidate) {
        this.candidate = candidate;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }

}
