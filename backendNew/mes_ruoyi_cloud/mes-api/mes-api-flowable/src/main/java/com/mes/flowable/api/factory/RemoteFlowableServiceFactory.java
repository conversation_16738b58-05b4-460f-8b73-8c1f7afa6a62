package com.mes.flowable.api.factory;

import com.mes.common.core.web.domain.AjaxResult;
import com.mes.flowable.api.domain.FlowTaskDto;
import com.mes.flowable.api.model.FlowTaskVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import com.mes.flowable.api.RemoteFlowableService;

import java.util.Collections;
import java.util.Map;

public class RemoteFlowableServiceFactory implements FallbackFactory<RemoteFlowableService> {
    @Override
    public RemoteFlowableService create(Throwable cause) {
        return new RemoteFlowableService() {

            @Override
            public AjaxResult startAndGet(String procDefId, Map<String, Object> variables, String source) {
                return AjaxResult.error("启动流程失败");
            }

            @Override
            public AjaxResult startByKey(String procDefKey, Map<String, Object> variables, String source) {
                return AjaxResult.error("启动流程失败");
            }

            @Override
            public AjaxResult completeInner(FlowTaskVo params, String source) {
                return AjaxResult.error("提交流程失败");
            }

            @Override
            public Map<String, Object> todoList(Integer pageNum, Integer pageSize, FlowTaskDto params, String source) {
                return Collections.emptyMap();
            }

            @Override
            public AjaxResult getFormById(String procDefId, String source) {
                return AjaxResult.error("表单信息获取失败");
            }
        };
    }
}
