package com.mes.flowable.api.model;


import java.util.List;
import java.util.Map;

/**
 * <p>流程任务<p>
 * 工作流任务相关--请求参数
 *
 * <AUTHOR>
 */
public class FlowTaskVo {

    //任务Id
    private String taskId;
    //用户Id
    private String userId;

    //任务意见
    private String comment;

    //"流程实例Id")
    private String instanceId;

    //"节点")
    private String targetKey;

    //"流程变量信息")
    private Map<String, Object> values;

    //"审批人")
    private String assignee;

    //"候选人")
    private List<String> candidateUsers;

    //"审批组")
    private List<String> candidateGroups;

    private String deploymentId;
    //"流程环节定义ID")
    private String defId;

    //"子执行流ID")
    private String currentChildExecutionId;

    //"子执行流是否已执行")
    private Boolean flag;

    //"流程变量信息")
    private Map<String, Object> variables;

    //"表单ID")
    private Long formId;

    public String getDeploymentId() {
        return deploymentId;
    }

    public void setDeploymentId(String deploymentId) {
        this.deploymentId = deploymentId;
    }

    public String getDefId() {
        return defId;
    }

    public void setDefId(String defId) {
        this.defId = defId;
    }

    public String getCurrentChildExecutionId() {
        return currentChildExecutionId;
    }

    public void setCurrentChildExecutionId(String currentChildExecutionId) {
        this.currentChildExecutionId = currentChildExecutionId;
    }

    public Boolean getFlag() {
        return flag;
    }

    public void setFlag(Boolean flag) {
        this.flag = flag;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }

    public String getTargetKey() {
        return targetKey;
    }

    public void setTargetKey(String targetKey) {
        this.targetKey = targetKey;
    }

    public Map<String, Object> getValues() {
        return values;
    }

    public void setValues(Map<String, Object> values) {
        this.values = values;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public List<String> getCandidateUsers() {
        return candidateUsers;
    }

    public void setCandidateUsers(List<String> candidateUsers) {
        this.candidateUsers = candidateUsers;
    }

    public List<String> getCandidateGroups() {
        return candidateGroups;
    }

    public void setCandidateGroups(List<String> candidateGroups) {
        this.candidateGroups = candidateGroups;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }
}
