package com.mes.flowable.api;

import com.mes.common.core.constant.SecurityConstants;
import com.mes.common.core.constant.ServiceNameConstants;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.flowable.api.domain.FlowTaskDto;
import com.mes.flowable.api.factory.RemoteFlowableServiceFactory;
import com.mes.flowable.api.model.FlowTaskVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@FeignClient(contextId = "remoteFlowableService", value = ServiceNameConstants.FLOWABLE, fallbackFactory = RemoteFlowableServiceFactory.class)
public interface RemoteFlowableService {

    /**
     * 根据流程定义id启动流程实例并返回id
     * <p>
     * procDefId 流程定义id
     *
     * @param variables 变量集合,json对象
     * @return
     */
    @PostMapping("/flowable/instance/startAndGet/{procDefId}")
    AjaxResult startAndGet(@PathVariable("procDefId") String procDefId, @RequestBody Map<String, Object> variables, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据流程定义key启动流程实例并返回id
     * <p>
     * procDefKey 流程定义key
     *
     * @param variables 变量集合,json对象
     * @return
     */
    @PostMapping("/flowable/definition/startByKey/{procDefKey}")
    AjaxResult startByKey(@PathVariable("procDefKey") String procDefKey, @RequestBody Map<String, Object> variables, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 审批任务
     *
     * @param params
     * @return
     */
    @PostMapping("/flowable/task/completeInner")
    AjaxResult completeInner(@RequestBody FlowTaskVo params, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取待办列表
     *
     * @param pageNum  当前页码
     * @param pageSize 每页条数
     * @param params
     * @return
     */
    @GetMapping("/flowable/task/todoListInner")
    Map<String, Object> todoList(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize, @RequestBody FlowTaskDto params, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据表单id获取表单内容
     *
     * @param procDefId 表单id
     * @return
     */
    @GetMapping("/flowable/form/inner/{formId}")
    AjaxResult getFormById(@PathVariable("procDefId") String procDefId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
