# 压测环境配置示例
# 将此配置添加到 application-test.yml 或 application-prod.yml

# 幂等性配置 - 压测环境
idempotent:
  # 启用幂等性功能
  enabled: true
  # IP限制过期时间（可以设置更短，比如1秒）
  ip-limit-expire-time: 1
  # 启用IP白名单
  enable-whitelist: true
  # 白名单IP完全跳过幂等性检查
  whitelist-skip-check: true
  # 压测机器IP白名单
  whitelist-ips:
    # 压测机器IP（请根据实际情况修改）
    - "*************"           # 压测机器1
    - "*************"           # 压测机器2
    - "***********/24"          # 整个压测网段
    - "********-********00"     # 压测IP段
    - "127.0.0.1"               # 本地测试

---

# 生产环境配置示例
# 将此配置添加到 application-prod.yml

# 幂等性配置 - 生产环境
idempotent:
  # 启用幂等性功能
  enabled: true
  # IP限制过期时间（生产环境建议3-5秒）
  ip-limit-expire-time: 3
  # 启用IP白名单
  enable-whitelist: true
  # 白名单IP完全跳过幂等性检查
  whitelist-skip-check: true
  # 生产环境白名单（谨慎配置）
  whitelist-ips:
    # 内部系统IP
    - "**************"          # 内部API调用
    - "*************/24"        # 内部网段
    # 可信第三方系统IP
    - "************"            # 第三方系统1
    - "************"            # 第三方系统2

---

# 开发环境配置示例
# 将此配置添加到 application-dev.yml

# 幂等性配置 - 开发环境
idempotent:
  # 开发环境可以关闭或开启
  enabled: false
  # 开发环境自动禁用
  disable-in-dev: true
  # 如果需要测试，可以配置开发机器IP
  whitelist-ips:
    - "127.0.0.1"               # 本地开发
    - "192.168.1.*"             # 开发网段

---

# 不同时间配置建议

# 1. 高频操作接口（如点赞、收藏）
idempotent:
  ip-limit-expire-time: 1       # 1秒防重复

# 2. 一般业务接口（如查询、更新）
idempotent:
  ip-limit-expire-time: 3       # 3秒防重复

# 3. 重要操作接口（如支付、下单）
idempotent:
  ip-limit-expire-time: 5       # 5秒防重复

# 4. 批量操作接口
idempotent:
  ip-limit-expire-time: 10      # 10秒防重复

---

# IP白名单格式说明

whitelist-ips:
  # 1. 单个IP地址
  - "*************"

  # 2. IP段格式
  - "***********-*************"

  # 3. CIDR格式
  - "***********/24"            # *********** 到 *************
  - "10.0.0.0/8"                # 10.0.0.0 到 **************

  # 4. 通配符格式
  - "192.168.1.*"               # *********** 到 *************
  - "10.0.*.*"                  # 10.0.0.0 到 ************

  # 5. 本地地址
  - "127.0.0.1"                 # 本地回环
  - "localhost"                 # 本地主机

---

# 压测场景配置

# 场景1：单机压测
idempotent:
  enabled: true
  whitelist-ips:
    - "127.0.0.1"               # 本机压测

# 场景2：多机压测
idempotent:
  enabled: true
  whitelist-ips:
    - "***********/24"          # 压测网段

# 场景3：云端压测
idempotent:
  enabled: true
  whitelist-ips:
    - "***********/24"          # 云端压测IP段

# 场景4：完全关闭（不推荐生产环境）
idempotent:
  enabled: false
