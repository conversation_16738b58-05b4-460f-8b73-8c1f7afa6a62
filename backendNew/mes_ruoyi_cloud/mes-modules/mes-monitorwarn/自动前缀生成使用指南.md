# 幂等性自动前缀生成使用指南

## 🎯 解决的问题

您提出的问题非常重要：**如何确保多人开发时keyPrefix的唯一性？**

我们的解决方案：**自动前缀生成器**

## 🚀 使用方式

### 1. 推荐用法：完全自动（零配置）

```java
@PostMapping("/saveUser")
@Idempotent(message = "用户保存中，请勿重复提交")
public LcResult saveUser(@RequestBody UserDto user) {
    // 业务逻辑
    return LcResult.success("保存成功");
}
```

**自动生成的前缀**: `user_controller_saveUser`  
**完整Key**: `idempotent:user_controller_saveUser:ip:*************:interface:/user/saveUser`

### 2. 手动指定前缀（特殊业务）

```java
@PostMapping("/payment")
@Idempotent(
    keyPrefix = "critical_payment",  // 手动指定
    message = "支付处理中，请勿重复操作"
)
public LcResult processPayment(@RequestBody PaymentDto payment) {
    // 支付逻辑
    return LcResult.success("支付成功");
}
```

### 3. 禁用自动前缀（特殊场景）

```java
@PostMapping("/simple")
@Idempotent(
    autoPrefix = false,  // 禁用自动前缀
    message = "处理中..."
)
public LcResult simpleOperation() {
    // 简单操作
    return LcResult.success("操作完成");
}
```

## 📋 自动前缀生成规则

### 生成格式
**类名_方法名** (驼峰转下划线)

| 原始 | 生成前缀 |
|------|----------|
| `UserController.saveUser` | `user_controller_saveUser` |
| `OrderService.createOrder` | `order_service_createOrder` |
| `MonAlarmHandleController.saveMonAlarmHandle` | `mon_alarm_handle_controller_saveMonAlarmHandle` |

### 自动清理规则
- 移除 `Controller`、`Service`、`Impl` 后缀
- 驼峰命名转下划线
- 确保前缀唯一性

## 🔧 多人开发场景

### 场景1：不同开发者，不同Controller
```java
// 开发者A：用户模块
@RestController
public class UserController {
    @PostMapping("/save")
    @Idempotent(message = "用户保存中")
    public LcResult saveUser() {
        // 自动前缀: user_saveUser
    }
}

// 开发者B：订单模块  
@RestController
public class OrderController {
    @PostMapping("/save")
    @Idempotent(message = "订单保存中")
    public LcResult saveOrder() {
        // 自动前缀: order_saveOrder
    }
}
```
**结果**: 前缀自动唯一，无冲突

### 场景2：相同Controller，不同方法
```java
@RestController
public class ProductController {
    @PostMapping("/create")
    @Idempotent(message = "创建中")
    public LcResult createProduct() {
        // 自动前缀: product_createProduct
    }
    
    @PostMapping("/update")
    @Idempotent(message = "更新中")
    public LcResult updateProduct() {
        // 自动前缀: product_updateProduct
    }
}
```
**结果**: 方法名不同，前缀自动唯一

### 场景3：故意冲突的手动前缀
```java
// 开发者A
@Idempotent(keyPrefix = "user_save", message = "A处理中")
public LcResult methodA() { }

// 开发者B  
@Idempotent(keyPrefix = "user_save", message = "B处理中")
public LcResult methodB() { }
```
**结果**: 会产生冲突！建议使用自动前缀

## 📊 前缀管理工具

### 查看前缀统计
```bash
GET /idempotent/prefix/stats
```

### 验证前缀格式
```bash
GET /idempotent/prefix/validate?prefix=user_save
```

### 清空前缀缓存（测试用）
```bash
POST /idempotent/prefix/clear-cache
```

## 🎯 最佳实践

### ✅ 推荐做法

1. **使用自动前缀**（默认行为）
```java
@Idempotent(message = "处理中，请稍后")
public LcResult businessMethod() { }
```

2. **重要业务手动指定**
```java
@Idempotent(
    keyPrefix = "critical_payment_process",
    message = "支付处理中"
)
public LcResult processPayment() { }
```

3. **团队约定命名规范**
```java
// 格式：模块_业务_操作
@Idempotent(keyPrefix = "user_account_login")
@Idempotent(keyPrefix = "order_payment_process")  
@Idempotent(keyPrefix = "alarm_handle_approve")
```

### ❌ 避免的做法

1. **随意的手动前缀**
```java
@Idempotent(keyPrefix = "abc")  // 太简单，容易冲突
@Idempotent(keyPrefix = "test") // 不明确
```

2. **相同的手动前缀**
```java
// 多个方法使用相同前缀
@Idempotent(keyPrefix = "user")  // 会冲突！
```

## 🔍 测试验证

### 测试接口
- `POST /idempotent/demo/quick-test` - 自动前缀
- `POST /idempotent/demo/manual-prefix-test` - 手动前缀
- `POST /idempotent/demo/conflict-test-1` - 冲突测试1
- `POST /idempotent/demo/conflict-test-2` - 冲突测试2

### 验证步骤
1. 调用自动前缀接口，查看生成的Key
2. 调用手动前缀接口，验证自定义前缀
3. 调用冲突测试接口，观察相同前缀的行为

## 📈 优势总结

✅ **自动唯一**: 基于类名+方法名，天然唯一  
✅ **零配置**: 默认行为，无需额外配置  
✅ **可读性好**: 前缀包含业务含义  
✅ **灵活性**: 支持手动指定特殊前缀  
✅ **团队友好**: 避免多人开发冲突  
✅ **可维护**: 前缀与代码结构对应  

## 🎉 解决方案总结

现在您不用再担心多人开发时的前缀冲突问题了：

1. **默认使用自动前缀** - 系统自动保证唯一性
2. **特殊业务手动指定** - 重要接口可以自定义
3. **团队规范约定** - 手动前缀遵循命名规范
4. **工具辅助管理** - 提供前缀查看和验证工具

这样既保证了唯一性，又保持了灵活性！🎉
