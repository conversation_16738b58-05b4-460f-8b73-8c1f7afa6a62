# 幂等性功能优化说明

## 🎯 优化内容

根据您的建议，我们对幂等性功能进行了重大优化：

### 1. 添加配置开关
- **默认关闭**: 避免影响压测和开发
- **环境感知**: 开发环境自动禁用
- **灵活配置**: 支持多种配置选项

### 2. 新增IP+接口模式（推荐）
- **简化逻辑**: 相同IP访问相同接口短时间内只允许一次
- **性能优化**: 避免复杂的参数序列化
- **压测友好**: 不同IP可以正常并发测试

### 3. 智能排除机制
- **路径排除**: 自动排除监控、文档等接口
- **通配符支持**: 灵活的路径匹配规则

## 📋 配置说明

### application-dev.yml 配置
```yaml
# 幂等性配置
idempotent:
  # 是否启用幂等性功能，默认关闭
  enabled: false
  # 默认过期时间（秒）
  default-expire-time: 30
  # 是否启用IP限制模式（推荐）
  ip-limit-mode: true
  # IP限制模式下的过期时间（秒）
  ip-limit-expire-time: 10
  # 是否在开发环境下禁用
  disable-in-dev: true
  # 排除的接口路径
  exclude-paths:
    - /idempotent/**
    - /actuator/**
    - /swagger-ui/**
    - /v3/api-docs/**
```

### 生产环境配置建议
```yaml
# application-prod.yml
idempotent:
  enabled: true                    # 生产环境启用
  default-expire-time: 60         # 生产环境可以设置更长时间
  ip-limit-mode: true             # 推荐使用IP限制模式
  ip-limit-expire-time: 30        # 生产环境30秒防重复
  disable-in-dev: false           # 生产环境不禁用
```

## 🚀 使用方式

### 1. 最简单的使用（推荐）
```java
@PostMapping("/save")
@Idempotent(message = "请勿重复提交")
public LcResult save(@RequestBody DataDto data) {
    // 业务逻辑
    return LcResult.success("保存成功");
}
```

**特点**:
- 默认使用 `IP_INTERFACE` 模式
- 默认10秒过期时间
- 相同IP访问相同接口10秒内只允许一次

### 2. 自定义配置
```java
@PostMapping("/payment")
@Idempotent(
    keyPrefix = "payment",
    expireTime = 30,
    message = "支付请求处理中，请勿重复操作"
)
public LcResult payment(@RequestBody PaymentDto payment) {
    // 支付逻辑
    return LcResult.success("支付成功");
}
```

### 3. 复杂场景（如需要）
```java
@PostMapping("/complex")
@Idempotent(
    type = IdempotentTypeEnum.PARAM,  // 使用参数模式
    includeKeys = {"orderId", "amount"},
    expireTime = 5,
    timeUnit = TimeUnit.MINUTES
)
public LcResult complexOperation(@RequestBody OrderDto order) {
    // 复杂业务逻辑
    return LcResult.success("操作成功");
}
```

## 🔧 开发和测试

### 开发环境
```yaml
idempotent:
  enabled: false          # 开发时关闭，不影响调试
  disable-in-dev: true    # 即使enabled=true，开发环境也会禁用
```

### 压测环境
```yaml
idempotent:
  enabled: false          # 压测时关闭，避免影响并发测试
```

### 生产环境
```yaml
idempotent:
  enabled: true           # 生产环境开启
  ip-limit-expire-time: 30 # 30秒防重复提交
```

## 📊 性能对比

| 模式 | 性能 | 复杂度 | 适用场景 |
|------|------|--------|----------|
| IP_INTERFACE | ⭐⭐⭐⭐⭐ | ⭐ | 防重复提交（推荐） |
| PARAM | ⭐⭐⭐ | ⭐⭐⭐ | 严格参数幂等 |
| USER_PARAM | ⭐⭐ | ⭐⭐⭐⭐ | 用户级幂等 |
| TOKEN | ⭐⭐⭐⭐ | ⭐⭐ | 表单防重复 |

## 🎯 推荐配置

### 1. 一般业务接口
```java
@Idempotent(message = "操作过于频繁，请稍后再试")
```

### 2. 重要操作接口
```java
@Idempotent(
    expireTime = 30,
    message = "重要操作正在处理中，请勿重复操作"
)
```

### 3. 高频接口
```java
@Idempotent(
    expireTime = 5,
    message = "请求过于频繁，请稍后再试"
)
```

## 🔍 监控和日志

启用后会看到以下日志：
```
INFO  - === 幂等性功能已启用 ===
INFO  - 默认类型: IP_INTERFACE (推荐)
INFO  - IP限制模式: 启用
INFO  - 默认过期时间: 30秒
```

重复请求时：
```
WARN  - 幂等性校验失败，请求地址: /api/save, 错误信息: 操作过于频繁，请稍后再试
DEBUG - 生成幂等性key: idempotent:api_limit:ip:*************:interface:/api/save
```

## 🚦 启用步骤

### 1. 开发阶段
保持默认配置（关闭状态），正常开发和调试

### 2. 测试阶段
```yaml
idempotent:
  enabled: true
  ip-limit-expire-time: 5  # 测试时可以设置短一点
```

### 3. 生产部署
```yaml
idempotent:
  enabled: true
  ip-limit-expire-time: 30
```

## ✅ 优化效果

1. **压测友好**: 默认关闭，不影响性能测试
2. **开发友好**: 开发环境自动禁用，不影响调试
3. **性能优化**: IP+接口模式性能更好
4. **配置灵活**: 支持多种场景配置
5. **智能排除**: 自动排除系统接口

现在您可以放心地在生产环境中使用，而不用担心影响开发和压测！
