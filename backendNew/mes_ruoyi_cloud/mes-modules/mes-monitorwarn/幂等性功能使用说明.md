# Spring Boot 接口幂等性功能使用说明

## 功能概述

本项目实现了一套完整的接口幂等性解决方案，通过自定义注解 `@Idempotent` 可以轻松为任何接口添加幂等性保护，防止重复提交和重复处理。

## 核心组件

### 1. @Idempotent 注解
- **位置**: `com.mes.moniwarn.annotation.Idempotent`
- **作用**: 标记需要幂等性处理的方法
- **支持的幂等性类型**:
  - `TOKEN`: 基于Token的幂等性
  - `PARAM`: 基于参数的幂等性
  - `USER_PARAM`: 基于用户和参数的幂等性

### 2. 幂等性服务
- **接口**: `com.mes.moniwarn.service.IdempotentService`
- **实现**: `com.mes.moniwarn.service.impl.IdempotentServiceImpl`
- **功能**: 提供幂等性锁管理和Token生成验证

### 3. AOP切面
- **类**: `com.mes.moniwarn.config.aspect.IdempotentAspect`
- **作用**: 拦截带有@Idempotent注解的方法，执行幂等性检查

## 使用方式

### 1. 基于参数的幂等性（推荐）

```java
@PostMapping("/create-order")
@Idempotent(
    type = IdempotentTypeEnum.PARAM,
    keyPrefix = "order",
    expireTime = 10,
    timeUnit = TimeUnit.MINUTES,
    message = "订单正在处理中，请勿重复提交"
)
public LcResult createOrder(@RequestBody OrderDto orderDto) {
    // 业务逻辑
    return LcResult.success("订单创建成功");
}
```

### 2. 基于用户和参数的幂等性

```java
@PostMapping("/user-action")
@Idempotent(
    type = IdempotentTypeEnum.USER_PARAM,
    keyPrefix = "user_action",
    expireTime = 5,
    timeUnit = TimeUnit.MINUTES,
    message = "您已执行过相同操作，请勿重复",
    delKey = true // 业务完成后立即删除key
)
public LcResult userAction(@RequestBody ActionDto actionDto) {
    // 业务逻辑
    return LcResult.success("操作成功");
}
```

### 3. 基于Token的幂等性

#### 步骤1: 获取Token
```http
GET /idempotent/token
```

响应:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "token": "abc123def456",
        "header": "Idempotent-Token",
        "expireTime": "30分钟"
    }
}
```

#### 步骤2: 使用Token
```java
@PostMapping("/submit-form")
@Idempotent(
    type = IdempotentTypeEnum.TOKEN,
    message = "请勿重复提交，请重新获取Token"
)
public LcResult submitForm(@RequestBody FormDto formDto) {
    // 业务逻辑
    return LcResult.success("提交成功");
}
```

请求时需要在Header中携带Token:
```http
POST /submit-form
Idempotent-Token: abc123def456
Content-Type: application/json

{
    "field1": "value1",
    "field2": "value2"
}
```

## 注解参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| type | IdempotentTypeEnum | PARAM | 幂等性类型 |
| keyPrefix | String | "" | 幂等性key的前缀 |
| expireTime | long | 5L | 过期时间 |
| timeUnit | TimeUnit | MINUTES | 时间单位 |
| message | String | "请勿重复提交" | 重复请求时的错误信息 |
| delKey | boolean | false | 是否在业务完成后删除key |
| includeKeys | String[] | {} | 指定参与幂等性校验的参数名称 |
| excludeKeys | String[] | {} | 排除参与幂等性校验的参数名称 |

## 高级用法

### 1. 指定参数参与幂等性校验

```java
@PostMapping("/payment")
@Idempotent(
    type = IdempotentTypeEnum.PARAM,
    includeKeys = {"paymentId", "amount"}, // 只根据这两个参数判断
    excludeKeys = {"timestamp"}, // 排除时间戳参数
    expireTime = 15,
    timeUnit = TimeUnit.MINUTES
)
public LcResult processPayment(
    @RequestParam String paymentId,
    @RequestParam BigDecimal amount,
    @RequestParam String timestamp,
    @RequestParam String remark
) {
    // 支付逻辑
    return LcResult.success("支付成功");
}
```

### 2. 业务完成后立即释放锁

```java
@PostMapping("/quick-action")
@Idempotent(
    type = IdempotentTypeEnum.PARAM,
    delKey = true, // 业务完成后立即删除key，允许立即重新执行
    expireTime = 1,
    timeUnit = TimeUnit.HOURS
)
public LcResult quickAction(@RequestBody ActionDto actionDto) {
    // 快速业务逻辑
    return LcResult.success("操作完成");
}
```

## 测试接口

项目提供了完整的测试接口，位于 `IdempotentDemoController`:

1. **Token生成**: `GET /idempotent/token`
2. **基于Token的示例**: `POST /idempotent/demo/token-example`
3. **基于参数的示例**: `POST /idempotent/demo/param-example`
4. **基于用户和参数的示例**: `POST /idempotent/demo/user-param-example`
5. **指定参数的示例**: `POST /idempotent/demo/include-keys-example`

## 异常处理

当检测到重复请求时，系统会抛出 `IdempotentException` 异常，该异常会被 `IdempotentExceptionHandler` 捕获并返回统一的错误响应。

## 注意事项

1. **Redis依赖**: 幂等性功能依赖Redis存储，确保Redis服务正常运行
2. **性能考虑**: 幂等性检查会增加少量性能开销，建议只在必要的接口上使用
3. **过期时间**: 合理设置过期时间，避免key长期占用内存
4. **Token安全**: Token具有时效性，客户端应妥善保管，避免泄露
5. **并发处理**: 系统已处理高并发场景下的竞态条件

## 扩展说明

如需自定义幂等性逻辑，可以：

1. 实现 `IdempotentService` 接口
2. 继承 `IdempotentKeyGenerator` 类
3. 添加新的 `IdempotentTypeEnum` 枚举值

## 技术实现

- **AOP**: 使用Spring AOP实现方法拦截
- **Redis**: 使用Redis实现分布式锁和Token存储
- **注解**: 使用自定义注解简化使用方式
- **异常处理**: 统一的异常处理机制
- **日志**: 完整的日志记录，便于问题排查
