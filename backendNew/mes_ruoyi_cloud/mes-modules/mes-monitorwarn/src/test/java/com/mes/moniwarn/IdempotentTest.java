//package com.mes.moniwarn;
//
//import com.mes.moniwarn.service.IdempotentService;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.concurrent.TimeUnit;
//
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * 幂等性功能测试
// *
// * <AUTHOR>
// * @date 2025-07-31
// */
//@SpringBootTest
//public class IdempotentTest {
//
//    @Autowired
//    private IdempotentService idempotentService;
//
//    @Test
//    public void testTryLock() {
//        String key = "test:lock:001";
//
//        // 第一次获取锁应该成功
//        boolean firstLock = idempotentService.tryLock(key, 1, TimeUnit.MINUTES);
//        assertTrue(firstLock, "第一次获取锁应该成功");
//
//        // 第二次获取相同的锁应该失败
//        boolean secondLock = idempotentService.tryLock(key, 1, TimeUnit.MINUTES);
//        assertFalse(secondLock, "第二次获取相同的锁应该失败");
//
//        // 释放锁
//        idempotentService.releaseLock(key);
//
//        // 释放后再次获取应该成功
//        boolean thirdLock = idempotentService.tryLock(key, 1, TimeUnit.MINUTES);
//        assertTrue(thirdLock, "释放锁后再次获取应该成功");
//
//        // 清理
//        idempotentService.releaseLock(key);
//    }
//
//    @Test
//    public void testGenerateAndValidateToken() {
//        // 生成Token
//        String token = idempotentService.generateToken();
//        assertNotNull(token, "生成的Token不应为空");
//        assertFalse(token.isEmpty(), "生成的Token不应为空字符串");
//
//        // 验证并消费Token
//        boolean valid = idempotentService.validateAndConsumeToken(token);
//        assertTrue(valid, "Token验证应该成功");
//
//        // 再次验证相同Token应该失败（已被消费）
//        boolean validAgain = idempotentService.validateAndConsumeToken(token);
//        assertFalse(validAgain, "已消费的Token再次验证应该失败");
//    }
//
//    @Test
//    public void testExists() {
//        String key = "test:exists:001";
//
//        // 初始状态key不存在
//        assertFalse(idempotentService.exists(key), "初始状态key不应存在");
//
//        // 设置锁后key应该存在
//        idempotentService.tryLock(key, 1, TimeUnit.MINUTES);
//        assertTrue(idempotentService.exists(key), "设置锁后key应该存在");
//
//        // 释放锁后key不存在
//        idempotentService.releaseLock(key);
//        assertFalse(idempotentService.exists(key), "释放锁后key不应存在");
//    }
//}
