package com.mes.moniwarn.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;

/**
 * IP白名单工具类
 * 支持多种IP格式匹配：
 * 1. 单个IP: *************
 * 2. IP段: ***********-*************
 * 3. CIDR格式: ***********/24
 * 4. 通配符: 192.168.1.*
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
public class IpWhitelistUtil {

    /**
     * 检查IP是否在白名单中
     *
     * @param clientIp 客户端IP
     * @param whitelist 白名单数组
     * @return true-在白名单中，false-不在白名单中
     */
    public static boolean isInWhitelist(String clientIp, String[] whitelist) {
        if (StrUtil.isBlank(clientIp) || whitelist == null || whitelist.length == 0) {
            return false;
        }

        // 处理本地回环地址
        if ("0:0:0:0:0:0:0:1".equals(clientIp) || "::1".equals(clientIp)) {
            clientIp = "127.0.0.1";
        }

        for (String whiteIp : whitelist) {
            if (StrUtil.isBlank(whiteIp)) {
                continue;
            }

            try {
                if (matchIp(clientIp, whiteIp.trim())) {
                    log.debug("IP {} 匹配白名单规则: {}", clientIp, whiteIp);
                    return true;
                }
            } catch (Exception e) {
                log.warn("IP白名单匹配异常: clientIp={}, whiteIp={}", clientIp, whiteIp, e);
            }
        }

        return false;
    }

    /**
     * 匹配单个IP规则
     */
    private static boolean matchIp(String clientIp, String whiteIp) {
        // 1. 精确匹配
        if (clientIp.equals(whiteIp)) {
            return true;
        }

        // 2. 通配符匹配 (192.168.1.*)
        if (whiteIp.contains("*")) {
            return matchWildcard(clientIp, whiteIp);
        }

        // 3. IP段匹配 (***********-*************)
        if (whiteIp.contains("-")) {
            return matchIpRange(clientIp, whiteIp);
        }

        // 4. CIDR匹配 (***********/24)
        if (whiteIp.contains("/")) {
            return matchCidr(clientIp, whiteIp);
        }

        return false;
    }

    /**
     * 通配符匹配
     */
    private static boolean matchWildcard(String clientIp, String pattern) {
        String regex = pattern.replace(".", "\\.")
                             .replace("*", "\\d+");
        return clientIp.matches(regex);
    }

    /**
     * IP段匹配
     */
    private static boolean matchIpRange(String clientIp, String range) {
        try {
            String[] parts = range.split("-");
            if (parts.length != 2) {
                return false;
            }

            long clientIpLong = ipToLong(clientIp);
            long startIpLong = ipToLong(parts[0].trim());
            long endIpLong = ipToLong(parts[1].trim());

            return clientIpLong >= startIpLong && clientIpLong <= endIpLong;
        } catch (Exception e) {
            log.warn("IP段匹配失败: clientIp={}, range={}", clientIp, range, e);
            return false;
        }
    }

    /**
     * CIDR匹配
     */
    private static boolean matchCidr(String clientIp, String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return false;
            }

            String networkIp = parts[0].trim();
            int prefixLength = Integer.parseInt(parts[1].trim());

            long clientIpLong = ipToLong(clientIp);
            long networkIpLong = ipToLong(networkIp);

            // 计算子网掩码
            long mask = (0xFFFFFFFFL << (32 - prefixLength)) & 0xFFFFFFFFL;

            return (clientIpLong & mask) == (networkIpLong & mask);
        } catch (Exception e) {
            log.warn("CIDR匹配失败: clientIp={}, cidr={}", clientIp, cidr, e);
            return false;
        }
    }

    /**
     * IP地址转换为长整型
     */
    private static long ipToLong(String ip) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            byte[] bytes = inetAddress.getAddress();

            long result = 0;
            for (int i = 0; i < bytes.length; i++) {
                result = (result << 8) | (bytes[i] & 0xFF);
            }
            return result;
        } catch (UnknownHostException e) {
            throw new IllegalArgumentException("无效的IP地址: " + ip, e);
        }
    }

    /**
     * 验证IP格式是否正确
     */
    public static boolean isValidIp(String ip) {
        if (StrUtil.isBlank(ip)) {
            return false;
        }

        try {
            InetAddress.getByName(ip);
            return true;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 验证白名单配置格式
     */
    public static void validateWhitelist(String[] whitelist) {
        if (whitelist == null || whitelist.length == 0) {
            return;
        }

        for (String whiteIp : whitelist) {
            if (StrUtil.isBlank(whiteIp)) {
                continue;
            }

            String ip = whiteIp.trim();

            // 检查各种格式
            if (ip.contains("*")) {
                // 通配符格式
                continue;
            } else if (ip.contains("-")) {
                // IP段格式
                String[] parts = ip.split("-");
                if (parts.length == 2) {
                    if (!isValidIp(parts[0].trim()) || !isValidIp(parts[1].trim())) {
                        log.warn("无效的IP段格式: {}", ip);
                    }
                }
            } else if (ip.contains("/")) {
                // CIDR格式
                String[] parts = ip.split("/");
                if (parts.length == 2) {
                    if (!isValidIp(parts[0].trim())) {
                        log.warn("无效的CIDR格式: {}", ip);
                    }
                    try {
                        int prefix = Integer.parseInt(parts[1].trim());
                        if (prefix < 0 || prefix > 32) {
                            log.warn("无效的CIDR前缀长度: {}", ip);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("无效的CIDR前缀: {}", ip);
                    }
                }
            } else {
                // 单个IP
                if (!isValidIp(ip)) {
                    log.warn("无效的IP地址: {}", ip);
                }
            }
        }
    }
}
