package com.mes.moniwarn.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.mes.moniwarn.annotation.Idempotent;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 幂等性前缀自动生成器
 * 确保多人开发时前缀的唯一性
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
public class IdempotentPrefixGenerator {

    /**
     * 前缀缓存，避免重复计算
     */
    private static final ConcurrentHashMap<String, String> PREFIX_CACHE = new ConcurrentHashMap<>();

    /**
     * 生成唯一的幂等性前缀
     *
     * @param joinPoint 切点
     * @param idempotent 幂等性注解
     * @return 唯一前缀
     */
    public static String generatePrefix(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        // 如果手动指定了前缀，直接使用
        String manualPrefix = idempotent.keyPrefix();
        if (StrUtil.isNotBlank(manualPrefix)) {
            return manualPrefix;
        }

        // 如果禁用了自动前缀生成，返回空
        if (!idempotent.autoPrefix()) {
            return "";
        }

        // 自动生成唯一前缀
        return generateAutoPrefix(joinPoint);
    }

    /**
     * 自动生成前缀
     */
    private static String generateAutoPrefix(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 生成缓存key
        String cacheKey = method.getDeclaringClass().getName() + "#" + method.getName();

        // 从缓存中获取
        return PREFIX_CACHE.computeIfAbsent(cacheKey, key -> {
            String prefix = doGenerateAutoPrefix(method);
            log.debug("自动生成幂等性前缀: {} -> {}", key, prefix);
            return prefix;
        });
    }

    /**
     * 执行自动前缀生成逻辑
     */
    private static String doGenerateAutoPrefix(Method method) {
        Class<?> clazz = method.getDeclaringClass();
        String className = clazz.getSimpleName();
        String methodName = method.getName();

        // 策略1: 类名_方法名 (推荐，可读性好)
        String strategy1 = generateClassMethodPrefix(className, methodName);

        // 策略2: 包名_类名_方法名 (更唯一，但较长)
        String strategy2 = generatePackageClassMethodPrefix(clazz, methodName);

        // 策略3: MD5哈希 (最短，但不可读)
        String strategy3 = generateHashPrefix(clazz, methodName);

        // 根据配置选择策略，这里默认使用策略1
        return strategy1;
    }

    /**
     * 策略1: 类名_方法名
     * 例: UserController_saveUser
     */
    private static String generateClassMethodPrefix(String className, String methodName) {
        // 移除Controller后缀
        String cleanClassName = className.replace("Controller", "")
                                         .replace("Service", "")
                                         .replace("Impl", "");

        // 转换为下划线格式
        String classPrefix = camelToUnderscore(cleanClassName);
        String methodPrefix = camelToUnderscore(methodName);

        return classPrefix + "_" + methodPrefix;
    }

    /**
     * 策略2: 包名_类名_方法名
     * 例: moniwarn_UserController_saveUser
     */
    private static String generatePackageClassMethodPrefix(Class<?> clazz, String methodName) {
        String packageName = clazz.getPackage().getName();
        String[] packageParts = packageName.split("\\.");

        // 取包名的最后一部分
        String lastPackage = packageParts[packageParts.length - 1];

        String className = clazz.getSimpleName();
        String cleanClassName = className.replace("Controller", "")
                                         .replace("Service", "")
                                         .replace("Impl", "");

        String packagePrefix = camelToUnderscore(lastPackage);
        String classPrefix = camelToUnderscore(cleanClassName);
        String methodPrefix = camelToUnderscore(methodName);

        return packagePrefix + "_" + classPrefix + "_" + methodPrefix;
    }

    /**
     * 策略3: MD5哈希前缀
     * 例: a1b2c3d4
     */
    private static String generateHashPrefix(Class<?> clazz, String methodName) {
        String fullName = clazz.getName() + "#" + methodName;
        String hash = MD5.create().digestHex(fullName);

        // 取前8位作为前缀
        return hash.substring(0, 8);
    }

    /**
     * 驼峰转下划线
     */
    private static String camelToUnderscore(String camelCase) {
        if (StrUtil.isBlank(camelCase)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append("_");
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 验证前缀是否合法
     */
    public static boolean isValidPrefix(String prefix) {
        if (StrUtil.isBlank(prefix)) {
            return true; // 空前缀是合法的
        }

        // 检查前缀格式：只允许字母、数字、下划线
        return prefix.matches("^[a-zA-Z0-9_]+$");
    }

    /**
     * 获取前缀统计信息（用于调试）
     */
    public static void printPrefixStats() {
        log.info("=== 幂等性前缀统计 ===");
        log.info("已缓存前缀数量: {}", PREFIX_CACHE.size());
        PREFIX_CACHE.forEach((key, prefix) -> {
            log.info("  {} -> {}", key, prefix);
        });
    }

    /**
     * 清空前缀缓存（用于测试）
     */
    public static void clearCache() {
        PREFIX_CACHE.clear();
        log.debug("前缀缓存已清空");
    }

    /**
     * 手动注册前缀（用于特殊情况）
     */
    public static void registerPrefix(Class<?> clazz, String methodName, String prefix) {
        String cacheKey = clazz.getName() + "#" + methodName;
        PREFIX_CACHE.put(cacheKey, prefix);
        log.info("手动注册前缀: {} -> {}", cacheKey, prefix);
    }
}
