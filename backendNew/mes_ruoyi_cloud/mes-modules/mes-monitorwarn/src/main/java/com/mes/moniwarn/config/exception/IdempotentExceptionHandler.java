package com.mes.moniwarn.config.exception;

import com.mes.moniwarn.domain.vo.LcResult;
import com.mes.moniwarn.exception.IdempotentException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 幂等性异常处理器
 * 专门处理幂等性相关的异常
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestControllerAdvice
@Slf4j
@Order(1) // 设置较高优先级，确保在全局异常处理器之前处理
public class IdempotentExceptionHandler {

    /**
     * 处理幂等性异常
     */
    @ExceptionHandler(IdempotentException.class)
    public LcResult handleIdempotentException(IdempotentException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("幂等性校验失败，请求地址: {}, 错误信息: {}", requestURI, e.getMessage());

        // 返回统一的错误响应
        return LcResult.error(e.getMessage());
    }
}
