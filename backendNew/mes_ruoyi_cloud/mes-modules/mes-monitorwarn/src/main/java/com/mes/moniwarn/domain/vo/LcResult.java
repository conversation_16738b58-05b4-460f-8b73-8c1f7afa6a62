package com.mes.moniwarn.domain.vo;

import com.mes.common.core.constant.HttpStatus;
import com.mes.common.core.web.page.TableDataInfo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class LcResult implements Serializable {
    private static final long serialVersionUID = 1L;
    private int code = 200;
    private String action = "success";
    private String msg = "succeed";
    private Object data;

    public LcResult() {
    }

    public int getCode() {
        return this.code;
    }

    public String getAction() {
        return this.action;
    }

    public String getMsg() {
        return this.msg;
    }

    public Object getData() {
        return this.data;
    }

    public void setCode(final int code) {
        this.code = code;
    }

    public void setAction(final String action) {
        this.action = action;
    }

    public void setMsg(final String msg) {
        this.msg = msg;
    }

    public void setData(final Object data) {
        this.data = data;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof LcResult)) {
            return false;
        } else {
            LcResult other = (LcResult)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label59: {
                    Object this$code = this.getCode();
                    Object other$code = other.getCode();
                    if (this$code == null) {
                        if (other$code == null) {
                            break label59;
                        }
                    } else if (this$code.equals(other$code)) {
                        break label59;
                    }

                    return false;
                }

                Object this$action = this.getAction();
                Object other$action = other.getAction();
                if (this$action == null) {
                    if (other$action != null) {
                        return false;
                    }
                } else if (!this$action.equals(other$action)) {
                    return false;
                }

                Object this$msg = this.getMsg();
                Object other$msg = other.getMsg();
                if (this$msg == null) {
                    if (other$msg != null) {
                        return false;
                    }
                } else if (!this$msg.equals(other$msg)) {
                    return false;
                }

                Object this$data = this.getData();
                Object other$data = other.getData();
                if (this$data == null) {
                    if (other$data != null) {
                        return false;
                    }
                } else if (!this$data.equals(other$data)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof LcResult;
    }

    public int hashCode() {
        int result = 1;
        Object $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        Object $action = this.getAction();
        result = result * 59 + ($action == null ? 43 : $action.hashCode());
        Object $msg = this.getMsg();
        result = result * 59 + ($msg == null ? 43 : $msg.hashCode());
        Object $data = this.getData();
        result = result * 59 + ($data == null ? 43 : $data.hashCode());
        return result;
    }

    public String toString() {
        return "ResultWrapper(code=" + this.getCode() + ", action=" + this.getAction() + ", msg=" + this.getMsg() + ", data=" + this.getData() + ")";
    }

    public LcResult(final int code, final String msg, final Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public LcResult(final int code, final String action, final String msg, final Object data) {
        this.code = code;
        this.action = action;
        this.msg = msg;
        this.data = data;
    }

    public LcResult(final String msg, final Object data) {
        this.msg = msg;
        this.data = data;
    }

    public LcResult(final int code, final Object data) {
        this.code = code;
        this.data = data;
    }

    public LcResult(final Object data) {
        this.data = data;
    }

    public static LcResult success(Object data)
    {
        return new LcResult(HttpStatus.SUCCESS, data);
    }

    public static LcResult success(String msg, Object data)
    {
        return new LcResult(HttpStatus.SUCCESS, msg, data);
    }

    public static LcResult success(int code, String msg, Object data)
    {
        return new LcResult(code, msg, data);
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @return 错误消息
     */
    public static LcResult error(String msg)
    {
        return new LcResult(HttpStatus.ERROR, "error", msg, null);
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 错误消息
     */
    public static LcResult error(String msg, Object data)
    {
        return new LcResult(HttpStatus.ERROR, "error", msg, data);
    }

    /**
     * 返回错误消息
     *
     * @param code 状态码
     * @param msg 返回内容
     * @return 错误消息
     */
    public static LcResult error(int code, String msg)
    {
        return new LcResult(code, "error", msg, null);
    }

    /**
     * 返回错误消息
     *
     * @param code 状态码
     * @param msg 返回内容
     * @param data 数据对象
     * @return 错误消息
     */
    public static LcResult error(int code, String msg, Object data)
    {
        return new LcResult(code, "error", msg, data);
    }

    public static LcResult pager(TableDataInfo tableDataInfo, int pageNo, int pageSize)
    {
        Map<String, Object> pager = new HashMap();
        pager.put("totalRecords", tableDataInfo.getTotal());
        pager.put("data", tableDataInfo.getRows());
        pager.put("pageNo", pageNo);
        pager.put("pageSize", pageSize);
        pager.put("totalPages", tableDataInfo.getTotal() / pageSize);
        pager.put("offset", (pageNo - 1) * pageSize);
        return new LcResult(HttpStatus.SUCCESS, pager);
    }
}
