package com.mes.moniwarn.domain.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 视频监控区域信息
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class VideoMonitoringNodesByParamsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 资源类型;详见数据字典：附录A.3 资源类型/资源权限码；
     * 查询资源的类型，传region时查询的为用户有配置权限的区域树，传资源类型如：camera、encodeDevice查询用户对该类资源有权限的区域树；
     * 注：资源iasDevice\reader\floor不进行权限校验，即不传authCodes
     */
    private String resourceType;

    /**
     * 父编号集合; String[]；父编号个数<=1000个；单个长度<=64Byte
     */
    private List<String> parentIndexCodes;

    /**
     * 是否包含下级区域，true时，搜索parentIndexCodes的所有子、孙区域；false时，只搜索parentIndexCodes的直接子区域
     */
    private Boolean isSubRegion;

    /**
     * 当前页码；pageNo>=1
     */
    private Integer pageNo;

    /**
     * 分页大小；0<pageSize<=1000
     */
    private Integer pageSize;

    /**
     * 权限码集合; String[]；详见数据字典附录A.3 资源类型/资源权限码，权限码个数<=20个；单个权限码长度<=40Byte；
     * 只有同时指定userId、authCodes时，才进行权限过滤；当指定多个权限码时，只返回同时具有这些权限码的区域
     */
    private List<String> authCodes;

    /**
     * 区域类型，10-普通区域，11-级联区域，12-楼栋单元
     */
    private Integer regionType;

    /**
     * 区域名称；根据区域名称过滤，可模糊查询；若包含中文，最大长度40，最大长度指不超过按照指定编码的字节长度，即getBytes("utf-8").length
     */
    private String regionName;

    /**
     * 本级区域向上查询; String[];场景：通过名称模糊查询资源点获取区域编号，并组成资源点的树形结构；
     * 本级区域个数<=10个；单个长度<=64Byte；
     * sonOrgIndexCodes集合中的多个区域只能为同一级目录；
     * 当parentIndexCodes、regionType、regionName都为空时，才可根据sonOrgIndexCodes查询
     */
    private List<String> sonOrgIndexCodes;

    /**
     * 级联标识，0-全部，1-本级，2-级联，默认0
     */
    private Integer cascadeFlag;

    /**
     * 排序字段,注意：排序字段必须是查询条件，否则返回参数错误
     */
    private String orderBy;

    /**
     * 降序升序,降序：desc，升序：asc
     */
    private String orderType;

    /**
     * 查询表达式
     */
    private List<VideoMonitoringExpressionDto> expressions;

    // Getters and Setters
    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public List<String> getParentIndexCodes() {
        return parentIndexCodes;
    }

    public void setParentIndexCodes(List<String> parentIndexCodes) {
        this.parentIndexCodes = parentIndexCodes;
    }

    public Boolean getIsSubRegion() {
        return isSubRegion;
    }

    public void setIsSubRegion(Boolean isSubRegion) {
        this.isSubRegion = isSubRegion;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getAuthCodes() {
        return authCodes;
    }

    public void setAuthCodes(List<String> authCodes) {
        this.authCodes = authCodes;
    }

    public Integer getRegionType() {
        return regionType;
    }

    public void setRegionType(Integer regionType) {
        this.regionType = regionType;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public List<String> getSonOrgIndexCodes() {
        return sonOrgIndexCodes;
    }

    public void setSonOrgIndexCodes(List<String> sonOrgIndexCodes) {
        this.sonOrgIndexCodes = sonOrgIndexCodes;
    }

    public Integer getCascadeFlag() {
        return cascadeFlag;
    }

    public void setCascadeFlag(Integer cascadeFlag) {
        this.cascadeFlag = cascadeFlag;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public List<VideoMonitoringExpressionDto> getExpressions() {
        return expressions;
    }

    public void setExpressions(List<VideoMonitoringExpressionDto> expressions) {
        this.expressions = expressions;
    }

    @Override
    public String toString() {
        return "VideoMonitoringNodesByParamsDto{" +
                "resourceType='" + resourceType + '\'' +
                ", parentIndexCodes=" + parentIndexCodes +
                ", isSubRegion=" + isSubRegion +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", authCodes=" + authCodes +
                ", regionType=" + regionType +
                ", regionName='" + regionName + '\'' +
                ", sonOrgIndexCodes=" + sonOrgIndexCodes +
                ", cascadeFlag=" + cascadeFlag +
                ", orderBy='" + orderBy + '\'' +
                ", orderType='" + orderType + '\'' +
                ", expressions=" + expressions +
                '}';
    }
}
