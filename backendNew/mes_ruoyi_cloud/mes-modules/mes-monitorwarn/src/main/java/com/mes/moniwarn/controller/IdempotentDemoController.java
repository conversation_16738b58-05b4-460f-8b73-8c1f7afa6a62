package com.mes.moniwarn.controller;

import com.mes.common.core.web.controller.BaseController;
import com.mes.moniwarn.annotation.Idempotent;
import com.mes.moniwarn.domain.vo.LcResult;
import com.mes.moniwarn.enums.IdempotentTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 幂等性演示控制器
 * 展示不同类型的幂等性使用方式
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("/idempotent/demo")
@Slf4j
@Tag(name = "幂等性演示", description = "展示幂等性注解的使用方式")
public class IdempotentDemoController extends BaseController {

    /**
     * 基于Token的幂等性示例
     * 客户端需要先调用 /idempotent/token 获取token
     * 然后在请求头中携带 Idempotent-Token: {token}
     */
    @PostMapping("/token-example")
    @Operation(summary = "基于Token的幂等性示例", description = "需要在请求头中携带Idempotent-Token")
    @Idempotent(
        type = IdempotentTypeEnum.TOKEN,
        message = "请勿重复提交，请重新获取Token后再试"
    )
    public LcResult tokenExample(@RequestBody Map<String, Object> data) {
        log.info("执行基于Token的幂等性业务逻辑: {}", data);

        // 模拟业务处理
        try {
            Thread.sleep(1000); // 模拟业务耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("message", "基于Token的幂等性处理成功");
        result.put("data", data);
        result.put("timestamp", System.currentTimeMillis());

        return LcResult.success(result);
    }

    /**
     * 基于参数的幂等性示例
     * 根据方法参数自动生成幂等性key
     */
    @PostMapping("/param-example")
    @Operation(summary = "基于参数的幂等性示例", description = "根据请求参数自动判断重复")
    @Idempotent(
        type = IdempotentTypeEnum.PARAM,
        keyPrefix = "order",
        expireTime = 10,
        timeUnit = TimeUnit.SECONDS,
        message = "相同参数的请求正在处理中，请稍后再试"
    )
    public LcResult paramExample(@RequestParam String orderId, @RequestParam String userId) {
        log.info("执行基于参数的幂等性业务逻辑: orderId={}, userId={}", orderId, userId);

        // 模拟业务处理
        try {
            Thread.sleep(2000); // 模拟业务耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("message", "基于参数的幂等性处理成功");
        result.put("orderId", orderId);
        result.put("userId", userId);
        result.put("timestamp", System.currentTimeMillis());

        return LcResult.success(result);
    }

    /**
     * 基于用户和参数的幂等性示例
     * 结合当前登录用户和方法参数生成幂等性key
     */
    @PostMapping("/user-param-example")
    @Operation(summary = "基于用户和参数的幂等性示例", description = "结合当前用户和请求参数判断重复")
    @Idempotent(
        type = IdempotentTypeEnum.USER_PARAM,
        keyPrefix = "user_order",
        expireTime = 5,
        timeUnit = TimeUnit.SECONDS,
        message = "您已提交过相同的请求，请勿重复操作",
        delKey = true // 业务完成后立即删除key
    )
    public LcResult userParamExample(@RequestBody Map<String, Object> orderData) {
        log.info("执行基于用户和参数的幂等性业务逻辑: {}", orderData);

        // 模拟业务处理
        try {
            Thread.sleep(1500); // 模拟业务耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("message", "基于用户和参数的幂等性处理成功");
        result.put("orderData", orderData);
        result.put("timestamp", System.currentTimeMillis());

        return LcResult.success(result);
    }

    /**
     * 基于IP和接口的幂等性示例（推荐）
     * 相同IP访问相同接口在短时间内只允许一次
     */
    @PostMapping("/ip-interface-example")
    @Operation(summary = "基于IP和接口的幂等性示例（推荐）", description = "相同IP短时间内只能访问一次")
    @Idempotent(
        message = "您的操作过于频繁，请稍后再试"
        // 自动生成前缀: idempotent_demo_ipInterfaceExample
    )
    public LcResult ipInterfaceExample(@RequestBody Map<String, Object> requestData) {
        log.info("执行基于IP和接口的幂等性业务逻辑: {}", requestData);

        // 模拟业务处理
        try {
            Thread.sleep(1000); // 模拟业务耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("message", "基于IP和接口的幂等性处理成功");
        result.put("requestData", requestData);
        result.put("timestamp", System.currentTimeMillis());
        result.put("note", "相同IP在3秒内只能访问一次此接口");

        return LcResult.success(result);
    }

    /**
     * 指定参数的幂等性示例
     * 只根据指定的参数进行幂等性判断
     */
    @PostMapping("/include-keys-example")
    @Operation(summary = "指定参数的幂等性示例", description = "只根据指定参数判断重复")
    @Idempotent(
        type = IdempotentTypeEnum.PARAM,
        keyPrefix = "payment",
        includeKeys = {"paymentId", "amount"}, // 只根据这两个参数判断幂等性
        expireTime = 15,
        timeUnit = TimeUnit.SECONDS,
        message = "相同的支付请求正在处理中"
    )
    public LcResult includeKeysExample(
        @Parameter(description = "支付ID") @RequestParam String paymentId,
        @Parameter(description = "支付金额") @RequestParam String amount,
        @Parameter(description = "备注信息") @RequestParam(required = false) String remark
    ) {
        log.info("执行指定参数的幂等性业务逻辑: paymentId={}, amount={}, remark={}",
                paymentId, amount, remark);

        // 模拟业务处理
        try {
            Thread.sleep(3000); // 模拟业务耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("message", "指定参数的幂等性处理成功");
        result.put("paymentId", paymentId);
        result.put("amount", amount);
        result.put("remark", remark);
        result.put("timestamp", System.currentTimeMillis());

        return LcResult.success(result);
    }

    /**
     * 快速测试接口（3秒限制）
     * 用于快速验证幂等性功能
     */
    @PostMapping("/quick-test")
    @Operation(summary = "快速测试接口", description = "3秒内只能访问一次，用于快速测试")
    @Idempotent(
        expireTime = 3,
        message = "操作过于频繁，请3秒后再试"
        // 自动生成前缀: idempotent_demo_quickTest
    )
    public LcResult quickTest(@RequestBody(required = false) Map<String, Object> data) {
        log.info("执行快速测试接口: {}", data);

        Map<String, Object> result = new HashMap<>();
        result.put("message", "快速测试成功");
        result.put("data", data);
        result.put("timestamp", System.currentTimeMillis());
        result.put("note", "相同IP在3秒内只能访问一次");
        result.put("tip", "可以连续调用此接口测试幂等性效果");

        return LcResult.success(result);
    }

    /**
     * 1秒限制测试接口
     * 用于测试高频操作的幂等性
     */
    @PostMapping("/one-second-test")
    @Operation(summary = "1秒限制测试", description = "1秒内只能访问一次，模拟高频操作")
    @Idempotent(
        expireTime = 1,
        message = "操作过于频繁，请1秒后再试"
        // 自动生成前缀: idempotent_demo_oneSecondTest
    )
    public LcResult oneSecondTest(@RequestParam(required = false) String action) {
        log.info("执行1秒限制测试: action={}", action);

        Map<String, Object> result = new HashMap<>();
        result.put("message", "1秒限制测试成功");
        result.put("action", action);
        result.put("timestamp", System.currentTimeMillis());
        result.put("note", "相同IP在1秒内只能访问一次");

        return LcResult.success(result);
    }

    /**
     * 手动指定前缀示例
     */
    @PostMapping("/manual-prefix-test")
    @Operation(summary = "手动指定前缀示例", description = "演示手动指定keyPrefix的用法")
    @Idempotent(
        keyPrefix = "custom_business_logic",  // 手动指定前缀
        expireTime = 5,
        message = "自定义业务逻辑处理中，请稍后再试"
    )
    public LcResult manualPrefixTest(@RequestParam(required = false) String businessId) {
        log.info("执行手动前缀测试: businessId={}", businessId);

        Map<String, Object> result = new HashMap<>();
        result.put("message", "手动前缀测试成功");
        result.put("businessId", businessId);
        result.put("prefix", "custom_business_logic");
        result.put("note", "使用手动指定的前缀");

        return LcResult.success(result);
    }

    /**
     * 禁用自动前缀示例
     */
    @PostMapping("/no-prefix-test")
    @Operation(summary = "禁用自动前缀示例", description = "演示禁用自动前缀生成")
    @Idempotent(
        autoPrefix = false,  // 禁用自动前缀
        expireTime = 3,
        message = "无前缀测试中，请稍后再试"
    )
    public LcResult noPrefixTest(@RequestParam(required = false) String data) {
        log.info("执行无前缀测试: data={}", data);

        Map<String, Object> result = new HashMap<>();
        result.put("message", "无前缀测试成功");
        result.put("data", data);
        result.put("prefix", "无前缀");
        result.put("note", "禁用了自动前缀生成");

        return LcResult.success(result);
    }

    /**
     * 前缀冲突演示（故意使用相同前缀）
     */
    @PostMapping("/conflict-test-1")
    @Operation(summary = "前缀冲突测试1", description = "演示相同前缀的情况")
    @Idempotent(
        keyPrefix = "same_prefix",  // 故意使用相同前缀
        expireTime = 10,
        message = "冲突测试1处理中"
    )
    public LcResult conflictTest1(@RequestParam(required = false) String param) {
        log.info("执行冲突测试1: param={}", param);

        Map<String, Object> result = new HashMap<>();
        result.put("message", "冲突测试1成功");
        result.put("method", "conflictTest1");
        result.put("param", param);

        return LcResult.success(result);
    }

    /**
     * 前缀冲突演示（故意使用相同前缀）
     */
    @PostMapping("/conflict-test-2")
    @Operation(summary = "前缀冲突测试2", description = "演示相同前缀的情况")
    @Idempotent(
        keyPrefix = "same_prefix",  // 故意使用相同前缀
        expireTime = 10,
        message = "冲突测试2处理中"
    )
    public LcResult conflictTest2(@RequestParam(required = false) String param) {
        log.info("执行冲突测试2: param={}", param);

        Map<String, Object> result = new HashMap<>();
        result.put("message", "冲突测试2成功");
        result.put("method", "conflictTest2");
        result.put("param", param);

        return LcResult.success(result);
    }
}
