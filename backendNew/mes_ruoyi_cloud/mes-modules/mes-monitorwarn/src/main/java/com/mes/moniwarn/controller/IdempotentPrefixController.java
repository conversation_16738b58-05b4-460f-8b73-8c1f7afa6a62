package com.mes.moniwarn.controller;

import com.mes.common.core.web.controller.BaseController;
import com.mes.moniwarn.domain.vo.LcResult;
import com.mes.moniwarn.utils.IdempotentPrefixGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 幂等性前缀管理控制器
 * 用于查看和管理幂等性前缀
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("/idempotent/prefix")
@Slf4j
@Tag(name = "幂等性前缀管理", description = "查看和管理幂等性前缀")
public class IdempotentPrefixController extends BaseController {

    /**
     * 查看前缀统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "查看前缀统计", description = "查看当前已生成的前缀统计信息")
    public LcResult getPrefixStats() {
        try {
            // 打印到日志
            IdempotentPrefixGenerator.printPrefixStats();

            Map<String, Object> result = new HashMap<>();
            result.put("message", "前缀统计信息已输出到日志");
            result.put("tip", "请查看控制台日志获取详细信息");

            return LcResult.success(result);
        } catch (Exception e) {
            log.error("获取前缀统计失败", e);
            return LcResult.error("获取前缀统计失败: " + e.getMessage());
        }
    }

    /**
     * 清空前缀缓存
     */
    @PostMapping("/clear-cache")
    @Operation(summary = "清空前缀缓存", description = "清空前缀缓存（仅用于测试）")
    public LcResult clearPrefixCache() {
        try {
            IdempotentPrefixGenerator.clearCache();

            Map<String, Object> result = new HashMap<>();
            result.put("message", "前缀缓存已清空");
            result.put("note", "下次调用接口时会重新生成前缀");

            log.info("前缀缓存已手动清空");
            return LcResult.success(result);
        } catch (Exception e) {
            log.error("清空前缀缓存失败", e);
            return LcResult.error("清空前缀缓存失败: " + e.getMessage());
        }
    }

    /**
     * 验证前缀格式
     */
    @GetMapping("/validate")
    @Operation(summary = "验证前缀格式", description = "验证指定前缀是否符合规范")
    public LcResult validatePrefix(@RequestParam String prefix) {
        try {
            boolean isValid = IdempotentPrefixGenerator.isValidPrefix(prefix);

            Map<String, Object> result = new HashMap<>();
            result.put("prefix", prefix);
            result.put("isValid", isValid);
            result.put("rule", "只允许字母、数字、下划线");

            if (isValid) {
                result.put("message", "前缀格式正确");
            } else {
                result.put("message", "前缀格式不正确");
                result.put("suggestion", "请使用字母、数字、下划线组合");
            }

            return LcResult.success(result);
        } catch (Exception e) {
            log.error("验证前缀格式失败", e);
            return LcResult.error("验证前缀格式失败: " + e.getMessage());
        }
    }
}
