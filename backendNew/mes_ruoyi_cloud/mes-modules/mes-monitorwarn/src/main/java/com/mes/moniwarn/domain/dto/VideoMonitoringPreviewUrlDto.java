package com.mes.moniwarn.domain.dto;

import java.io.Serializable;

/**
 * 获取监控点预览取流
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class VideoMonitoringPreviewUrlDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 监控点唯一标识 */
    private String cameraIndexCode;

    /** 码流类型，0:主码流 1:子码流 2:第三码流 */
    private Integer streamType;

    /** 取流协议 */
    private String protocol;

    /** 传输协议，0:UDP 1:TCP */
    private Integer transmode;

    /** 扩展内容 */
    private String expand;

    /** 输出码流转封装格式 */
    private String streamform;

    public String getStreamform() {
        return streamform;
    }

    public void setStreamform(String streamform) {
        this.streamform = streamform;
    }

    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }

    public Integer getTransmode() {
        return transmode;
    }

    public void setTransmode(Integer transmode) {
        this.transmode = transmode;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public Integer getStreamType() {
        return streamType;
    }

    public void setStreamType(Integer streamType) {
        this.streamType = streamType;
    }

    public String getCameraIndexCode() {
        return cameraIndexCode;
    }

    public void setCameraIndexCode(String cameraIndexCode) {
        this.cameraIndexCode = cameraIndexCode;
    }

    @Override
    public String toString() {
        return "VideoMonitoringPreviewUrlDto{" +
                "cameraIndexCode='" + cameraIndexCode + '\'' +
                ", streamType=" + streamType +
                ", protocol='" + protocol + '\'' +
                ", transmode=" + transmode +
                ", expand='" + expand + '\'' +
                ", streamform='" + streamform + '\'' +
                '}';
    }
}
