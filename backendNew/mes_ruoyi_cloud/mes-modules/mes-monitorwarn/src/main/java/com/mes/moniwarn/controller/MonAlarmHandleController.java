package com.mes.moniwarn.controller;

import com.github.pagehelper.PageHelper;
import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.page.PageDomain;
import com.mes.common.core.web.page.TableSupport;
import com.mes.moniwarn.annotation.Idempotent;
import com.mes.moniwarn.config.aspect.OperationLogEndpoint;
import com.mes.moniwarn.domain.MonAlarmHandle;
import com.mes.moniwarn.domain.dto.ApproveAlarmInstDto;
import com.mes.moniwarn.domain.dto.ApproveAlarmInstListDto;
import com.mes.moniwarn.domain.vo.LcResult;
import com.mes.moniwarn.enums.IdempotentTypeEnum;
import com.mes.moniwarn.enums.OperationTypeEnum;
import com.mes.moniwarn.service.IMonAlarmHandleService;
import com.mes.tower.api.domain.dto.PersonBasicOpenDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 报警处置流程Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/monAlarmHandle")
@Slf4j
public class MonAlarmHandleController extends BaseController {
    @Autowired
    private IMonAlarmHandleService monAlarmHandleService;

    /**
     * 新增报警处置流程
     * @RequiresPermissions("moniwarn:handle:add")
     */
    //@Log(title = "报警处置流程", businessType = BusinessType.INSERT)
    @PostMapping("/saveMonAlarmHandle")
    @OperationLogEndpoint(module = "审批管理-预警处理审批", operationType = OperationTypeEnum.CREATE,
            operationContent = "新增")
    @Idempotent(
        message = "报警处置流程正在处理中，请勿重复提交"
        // keyPrefix 留空，自动生成: mon_alarm_handle_saveMonAlarmHandle
    )
    public LcResult saveMonAlarmHandle(@RequestBody MonAlarmHandle monAlarmHandle) {
        return LcResult.success(monAlarmHandleService.insertMonAlarmHandle(monAlarmHandle));
    }


    /**
     * 审批管理-预警审批-列表
     */
    @PostMapping("/findApproveAlarmInstList")
    @OperationLogEndpoint(module = "审批管理-预警处理审批", operationType = OperationTypeEnum.QUERY,
            operationContent = "审批列表")
    public LcResult findApproveAlarmInstList(@RequestBody ApproveAlarmInstDto approveAlarmInstDto) {
//        startPage();
        //POST请求参数分页参数需要重新设置
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = approveAlarmInstDto.getPageNum() == null ? 1 : approveAlarmInstDto.getPageNum();
        Integer pageSize = approveAlarmInstDto.getPageSize() == null ? 10 : approveAlarmInstDto.getPageSize();
        PageHelper.startPage(pageNum, pageSize, pageDomain.getOrderBy()).setReasonable(pageDomain.getReasonable());
        List<ApproveAlarmInstListDto> list = monAlarmHandleService.findApproveAlarmInstList(approveAlarmInstDto);
        return LcResult.pager(getDataTable(list), pageNum, pageSize);
    }

    /**
     * 查询下一步处理人列表-列表
     * @param nodeCode 流程节点编码，对应流程模板上的节点key
     * @param nodeName 流程节点名称，对应流程模板的节点name
     * @param instId 报警ID
     * @param workflowId 工作流ID
     * @param roleKey 角色权限字符
     *
     *  @return LcResult
     */
    @GetMapping("/findNextTaskUserList")
    @OperationLogEndpoint(module = "审批管理-预警处理审批", operationType = OperationTypeEnum.QUERY, operationContent = "下一步处理人列表")
    public LcResult findNextTaskUserList(@RequestParam(name = "nodeCode", required = false) String nodeCode,
                                                @RequestParam(name = "nodeName", required = false) String nodeName,
                                                @RequestParam(name = "instId", required = false) Long instId,
                                                @RequestParam(name = "workflowId", required = false) String workflowId,
                                                @RequestParam(name = "roleKey", required = false) String roleKey) {
        log.info("查询下一步处理人列表-列表 nodeCode:{}, nodeName:{}, instId:{}, workflowId:{}, roleKey:{}", nodeCode, nodeName, instId, workflowId, roleKey);

        List<PersonBasicOpenDto>  list = monAlarmHandleService.findNextTaskUserList(nodeCode, nodeName, instId, workflowId, roleKey);

        return LcResult.success(list);
    }

}
