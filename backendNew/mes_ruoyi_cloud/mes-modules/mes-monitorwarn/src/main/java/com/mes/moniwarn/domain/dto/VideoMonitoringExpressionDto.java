package com.mes.moniwarn.domain.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 监控点列表查询表达式
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class VideoMonitoringExpressionDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 资源属性名，目前支持作为查询条件的字段包括: channelNo、indexCode、parentIndexCode、installLocation、capability、updateTime、createTime、resourceType、regionIndexCode、sort、name、description、treatyType。
     *  如：key传updateTime，operator传between可以查询特定时间段更新的数据，考虑到校时和夏令时，建议值查询过去一天的数据变更 */
    private String key;
    /** 操作运算符， 0 ：=， 1 ：>=， 2 ：<=， 3 ：in， 4 ：not in， 5 ：between， 6 ：like， 7 ：pre like， 8 ：suffix like */
    private Integer operator;
    /** 资源属性值，=、>=、<=、like、values数组长度只能是1； in、not in，values数组长度大于1，最大不超时20；
     *  in_array用于查询key值有多个value的情况，例如行车监控添加的设备类型为encodeDevice、encodeDeviceMss两个类型，
     *  使用encodeDevice或者encodeDeviceMss都可以查询到； between只能用于整形、日期 ； like只能用于字符串。 */
    private List<String> values;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }

    public List<String> getValues() {
        return values;
    }

    public void setValues(List<String> values) {
        this.values = values;
    }

    @Override
    public String toString() {
        return "VideoMonitoringExpressionDto{" +
                "key='" + key + '\'' +
                ", operator=" + operator +
                ", values=" + values +
                '}';
    }
}
