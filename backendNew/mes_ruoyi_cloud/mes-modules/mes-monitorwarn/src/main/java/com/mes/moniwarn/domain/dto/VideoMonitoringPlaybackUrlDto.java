package com.mes.moniwarn.domain.dto;

import java.io.Serializable;

/**
 * 获取监控点预览取流
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class VideoMonitoringPlaybackUrlDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 监控点唯一标识 */
    private String cameraIndexCode;

    /** 存储类型,0：中心存储 1：设备存储 */
    private String recordLocation;

    /** 取流协议 */
    private String protocol;

    /** 传输协议，0:UDP 1:TCP */
    private Integer transmode;

    /** 开始查询时间 */
    private String beginTime;

    /** 结束查询时间 */
    private String endTime;

    /** 分页查询id */
    private String uuid;

    /** 扩展内容 */
    private String expand;

    /** 输出码流转封装格式 */
    private String streamform;

    /** 查询录像的锁定类型 */
    private Integer lockType;

    public String getCameraIndexCode() {
        return cameraIndexCode;
    }

    public void setCameraIndexCode(String cameraIndexCode) {
        this.cameraIndexCode = cameraIndexCode;
    }

    public String getRecordLocation() {
        return recordLocation;
    }

    public void setRecordLocation(String recordLocation) {
        this.recordLocation = recordLocation;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public Integer getTransmode() {
        return transmode;
    }

    public void setTransmode(Integer transmode) {
        this.transmode = transmode;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }

    public String getStreamform() {
        return streamform;
    }

    public void setStreamform(String streamform) {
        this.streamform = streamform;
    }

    public Integer getLockType() {
        return lockType;
    }

    public void setLockType(Integer lockType) {
        this.lockType = lockType;
    }

    @Override
    public String toString() {
        return "VideoMonitoringPlaybackUrlDto{" +
                "cameraIndexCode='" + cameraIndexCode + '\'' +
                ", recordLocation='" + recordLocation + '\'' +
                ", protocol='" + protocol + '\'' +
                ", transmode=" + transmode +
                ", beginTime='" + beginTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", uuid='" + uuid + '\'' +
                ", expand='" + expand + '\'' +
                ", streamform='" + streamform + '\'' +
                ", lockType=" + lockType +
                '}';
    }
}
