package com.mes.moniwarn.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson2.JSON;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.moniwarn.annotation.Idempotent;
import com.mes.moniwarn.enums.IdempotentTypeEnum;
import com.mes.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 幂等性Key生成器
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
public class IdempotentKeyGenerator {

    private static final String IDEMPOTENT_KEY_PREFIX = "idempotent:";
    private static final String TOKEN_HEADER = "Idempotent-Token";

    /**
     * 生成幂等性key
     *
     * @param joinPoint 切点
     * @param idempotent 幂等性注解
     * @return 幂等性key
     */
    public static String generateKey(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        StringBuilder keyBuilder = new StringBuilder(IDEMPOTENT_KEY_PREFIX);

        // 生成前缀（自动或手动）
        String prefix = IdempotentPrefixGenerator.generatePrefix(joinPoint, idempotent);
        if (StrUtil.isNotBlank(prefix)) {
            keyBuilder.append(prefix).append(":");
        }

        // 根据不同类型生成key
        String businessKey = generateBusinessKey(joinPoint, idempotent);
        keyBuilder.append(businessKey);

        return keyBuilder.toString();
    }

    /**
     * 生成业务key
     */
    private static String generateBusinessKey(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        IdempotentTypeEnum type = idempotent.type();

        switch (type) {
            case TOKEN:
                return generateTokenKey();
            case PARAM:
                return generateParamKey(joinPoint, idempotent);
            case USER_PARAM:
                return generateUserParamKey(joinPoint, idempotent);
            case IP_INTERFACE:
                return generateIpInterfaceKey(joinPoint);
            default:
                throw new IllegalArgumentException("不支持的幂等性类型: " + type);
        }
    }

    /**
     * 生成基于Token的key
     */
    private static String generateTokenKey() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new IllegalStateException("无法获取请求上下文");
        }

        HttpServletRequest request = attributes.getRequest();
        String token = request.getHeader(TOKEN_HEADER);

        if (StrUtil.isBlank(token)) {
            throw new IllegalArgumentException("请求头中缺少幂等性Token: " + TOKEN_HEADER);
        }

        return "token:" + token;
    }

    /**
     * 生成基于参数的key
     */
    private static String generateParamKey(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();

        // 获取方法全名
        String methodName = method.getDeclaringClass().getName() + "." + method.getName();

        // 获取参数信息
        Map<String, Object> paramMap = getFilteredParams(method, args, idempotent);

        // 生成参数的MD5值
        String paramJson = JSON.toJSONString(paramMap);
        String paramMd5 = MD5.create().digestHex(paramJson);

        return "param:" + methodName + ":" + paramMd5;
    }

    /**
     * 生成基于用户和参数的key
     */
    private static String generateUserParamKey(ProceedingJoinPoint joinPoint, Idempotent idempotent) {
        // 获取当前用户信息
        String userId = getCurrentUserId();

        // 获取参数key
        String paramKey = generateParamKey(joinPoint, idempotent);

        return "user:" + userId + ":" + paramKey;
    }

    /**
     * 生成基于IP和接口的key（推荐模式）
     * 格式: ip:*************:interface:/api/save
     */
    private static String generateIpInterfaceKey(ProceedingJoinPoint joinPoint) {
        // 获取客户端IP
        String clientIp = getClientIp();

        // 获取接口路径
        String interfacePath = getInterfacePath(joinPoint);

        return "ip:" + clientIp + ":interface:" + interfacePath;
    }

    /**
     * 获取当前用户ID
     */
    private static String getCurrentUserId() {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null && loginUser.getUserid() != null) {
                return loginUser.getUserid().toString();
            }
        } catch (Exception e) {
            log.warn("获取当前用户信息失败", e);
        }
        return "anonymous";
    }

    /**
     * 获取过滤后的参数
     */
    private static Map<String, Object> getFilteredParams(Method method, Object[] args, Idempotent idempotent) {
        Parameter[] parameters = method.getParameters();
        Map<String, Object> paramMap = new HashMap<>();

        List<String> includeKeys = Arrays.asList(idempotent.includeKeys());
        List<String> excludeKeys = Arrays.asList(idempotent.excludeKeys());

        for (int i = 0; i < parameters.length && i < args.length; i++) {
            String paramName = parameters[i].getName();
            Object paramValue = args[i];

            // 跳过不可序列化的参数
            if (isSkippableParam(paramValue)) {
                continue;
            }

            // 如果指定了includeKeys，只包含指定的参数
            if (!includeKeys.isEmpty() && !includeKeys.contains(paramName)) {
                continue;
            }

            // 排除指定的参数
            if (!excludeKeys.isEmpty() && excludeKeys.contains(paramName)) {
                continue;
            }

            paramMap.put(paramName, paramValue);
        }

        return paramMap;
    }

    /**
     * 判断是否为需要跳过的参数类型
     */
    private static boolean isSkippableParam(Object param) {
        if (param == null) {
            return false;
        }

        String className = param.getClass().getName();
        return className.startsWith("javax.servlet") ||
               className.startsWith("org.springframework.web");
    }

    /**
     * 获取客户端IP地址
     */
    private static String getClientIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return "unknown";
            }

            HttpServletRequest request = attributes.getRequest();
            String ip = request.getHeader("X-Forwarded-For");

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-Real-IP");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }

            // 处理多个IP的情况，取第一个
            if (StrUtil.isNotBlank(ip) && ip.contains(",")) {
                ip = ip.split(",")[0].trim();
            }

            return StrUtil.isNotBlank(ip) ? ip : "unknown";
        } catch (Exception e) {
            log.warn("获取客户端IP失败", e);
            return "unknown";
        }
    }

    /**
     * 获取接口路径
     */
    private static String getInterfacePath(ProceedingJoinPoint joinPoint) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return request.getRequestURI();
            }

            // 如果无法获取请求URI，则使用方法签名
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            return method.getDeclaringClass().getSimpleName() + "." + method.getName();
        } catch (Exception e) {
            log.warn("获取接口路径失败", e);
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            return method.getDeclaringClass().getSimpleName() + "." + method.getName();
        }
    }
}
