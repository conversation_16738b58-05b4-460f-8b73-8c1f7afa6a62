package com.mes.moniwarn.controller;

import com.mes.common.core.web.controller.BaseController;
import com.mes.moniwarn.domain.vo.LcResult;
import com.mes.moniwarn.service.IdempotentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 幂等性控制器
 * 提供幂等性Token生成等功能
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RestController
@RequestMapping("/idempotent")
@Slf4j
@Tag(name = "幂等性管理", description = "幂等性相关接口")
public class IdempotentController extends BaseController {

    @Autowired
    private IdempotentService idempotentService;

    /**
     * 生成幂等性Token
     * 客户端在发起需要幂等性保护的请求前，先调用此接口获取Token
     * 然后在请求头中携带 Idempotent-Token: {token}
     */
    @GetMapping("/token")
    @Operation(summary = "生成幂等性Token", description = "生成用于幂等性校验的Token")
    public LcResult generateToken() {
        try {
            String token = idempotentService.generateToken();

            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("header", "Idempotent-Token");
            result.put("expireTime", "30分钟");

            log.info("生成幂等性Token成功: {}", token);
            return LcResult.success(result);
        } catch (Exception e) {
            log.error("生成幂等性Token失败", e);
            return LcResult.error("生成幂等性Token失败: " + e.getMessage());
        }
    }
}
