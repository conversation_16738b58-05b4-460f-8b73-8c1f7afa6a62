package com.mes.moniwarn.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.mes.common.core.web.controller.BaseController;
import com.mes.moniwarn.config.aspect.OperationLogEndpoint;
import com.mes.moniwarn.domain.dto.VideoMonitoringCameraDto;
import com.mes.moniwarn.domain.dto.VideoMonitoringNodesByParamsDto;
import com.mes.moniwarn.domain.dto.VideoMonitoringPlaybackUrlDto;
import com.mes.moniwarn.domain.dto.VideoMonitoringPreviewUrlDto;
import com.mes.moniwarn.domain.dto.VideoMonitoringRegionsDto;
import com.mes.moniwarn.domain.vo.LcResult;
import com.mes.moniwarn.enums.OperationTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 视频监控Controller
 * @Description: 海康威视视频监控Controller
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/videoMonitoring")
@Slf4j
public class VideoMonitoringController extends BaseController {

    // 代理API网关nginx服务器ip端口
    @Value("${hikvision.host:}")
    private String host;
    // 秘钥appkey
    @Value("${hikvision.appKey:}")
    private String appKey;
    // 秘钥appSecret
    @Value("${hikvision.appSecret:}")
    private String appSecret;
    // httpSchema
    @Value("${hikvision.httpSchema:}")
    private String httpSchema;
    /**
     * API网关的后端服务上下文为：/artemis
     */
    private static final String ARTEMIS_PATH = "/artemis";

    /**
     * 查询区域列表
     * @Description: 根据用户请求的资源类型和资源权限获取父区域的下级区域列表，主要用于逐层获取父区域的下级区域信息，例如监控点预览业务的区域树的逐层获取。下级区域只包括直接下级子区域。
     * 注： 查询区域管理权限（resourceType为region），若父区域的子区域无权限、但是其孙区域有权限时，会返回该无权限的子区域，但是该区域的available标记为false（表示无权限）
     * @return LcResult
     * @Version 1.0
     */
    @PostMapping("/regions/nodesByParams")
    @OperationLogEndpoint(module = "视频监控-查询区域列表", operationType = OperationTypeEnum.QUERY, operationContent = "查询区域列表")
    public LcResult nodesByParams(@RequestBody VideoMonitoringNodesByParamsDto videoMonitoringNodesByParamsDto) {
        log.info("查询区域列表  VideoMonitoringNodesByParamsDto {} ", videoMonitoringNodesByParamsDto);
        String returnInfo = null;
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getCamsApi = ARTEMIS_PATH + "/api/irds/v2/region/nodesByParams";
        Map<String, Object> paramMap = new HashMap<String, Object>();// post请求Form表单参数
        // 设置必要参数
        paramMap.put("pageNo", videoMonitoringNodesByParamsDto.getPageNo() != null ? videoMonitoringNodesByParamsDto.getPageNo() : 1);
        paramMap.put("pageSize", videoMonitoringNodesByParamsDto.getPageSize() != null ? videoMonitoringNodesByParamsDto.getPageSize() : 20);
        paramMap.put("resourceType", videoMonitoringNodesByParamsDto.getResourceType() != null ? videoMonitoringNodesByParamsDto.getResourceType() : "region");

        // 设置可选参数
        if (videoMonitoringNodesByParamsDto.getParentIndexCodes() != null && !videoMonitoringNodesByParamsDto.getParentIndexCodes().isEmpty()) {
            paramMap.put("parentIndexCodes", videoMonitoringNodesByParamsDto.getParentIndexCodes());
        }
        if (videoMonitoringNodesByParamsDto.getIsSubRegion() != null) {
            paramMap.put("isSubRegion", videoMonitoringNodesByParamsDto.getIsSubRegion());
        }
        if (videoMonitoringNodesByParamsDto.getAuthCodes() != null && !videoMonitoringNodesByParamsDto.getAuthCodes().isEmpty()) {
            paramMap.put("authCodes", videoMonitoringNodesByParamsDto.getAuthCodes());
        }
        if (videoMonitoringNodesByParamsDto.getRegionType() != null) {
            paramMap.put("regionType", videoMonitoringNodesByParamsDto.getRegionType());
        }
        if (videoMonitoringNodesByParamsDto.getRegionName() != null && !videoMonitoringNodesByParamsDto.getRegionName().isEmpty()) {
            paramMap.put("regionName", videoMonitoringNodesByParamsDto.getRegionName());
        }
        if (videoMonitoringNodesByParamsDto.getSonOrgIndexCodes() != null && !videoMonitoringNodesByParamsDto.getSonOrgIndexCodes().isEmpty()) {
            paramMap.put("sonOrgIndexCodes", videoMonitoringNodesByParamsDto.getSonOrgIndexCodes());
        }
        if (videoMonitoringNodesByParamsDto.getCascadeFlag() != null) {
            paramMap.put("cascadeFlag", videoMonitoringNodesByParamsDto.getCascadeFlag());
        }
        if (videoMonitoringNodesByParamsDto.getOrderBy() != null && !videoMonitoringNodesByParamsDto.getOrderBy().isEmpty()) {
            paramMap.put("orderBy", videoMonitoringNodesByParamsDto.getOrderBy());
        }
        if (videoMonitoringNodesByParamsDto.getOrderType() != null && !videoMonitoringNodesByParamsDto.getOrderType().isEmpty()) {
            paramMap.put("orderType", videoMonitoringNodesByParamsDto.getOrderType());
        }
        if (videoMonitoringNodesByParamsDto.getExpressions() != null && !videoMonitoringNodesByParamsDto.getExpressions().isEmpty()) {
            paramMap.put("expressions", videoMonitoringNodesByParamsDto.getExpressions());
        }
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<>();
        path.put(httpSchema, getCamsApi);
        JSONObject jsonObject = null;
        try {
            returnInfo = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
            log.info("查询区域列表  returnInfo {} ", returnInfo);
            if(returnInfo != null ){
                jsonObject = JSON.parseObject(returnInfo);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return LcResult.success(jsonObject) ;
    }

    /**
     * 根据区域编号获取下一级区域列表
     * @Description: 根据用户请求的资源类型和资源权限获取父区域的下级区域列表，主要用于逐层获取父区域的下级区域信息，例如监控点预览业务的区域树的逐层获取。下级区域只包括直接下级子区域。
     * 注： 查询区域管理权限（resourceType为region），若父区域的子区域无权限、但是其孙区域有权限时，会返回该无权限的子区域，但是该区域的available标记为false（表示无权限）
     * @return LcResult
     * @Version 1.0
     */
    @PostMapping("/regions/subRegions")
    @OperationLogEndpoint(module = "视频监控-根据区域编号获取下一级区域列表", operationType = OperationTypeEnum.QUERY, operationContent = "根据区域编号获取下一级区域列表")
    public LcResult subRegions(@RequestBody VideoMonitoringRegionsDto videoMonitoringRegionsDto) {
        log.info("根据区域编号获取下一级区域列表  VideoMonitoringRegionsDto {} ", videoMonitoringRegionsDto);
        String returnInfo = null;
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getCamsApi = ARTEMIS_PATH + "/api/resource/v2/regions/subRegions";
        Map<String, String> paramMap = new HashMap<String, String>();// post请求Form表单参数
        // 设置必要参数
        paramMap.put("parentIndexCode", videoMonitoringRegionsDto.getParentIndexCode() != null ? videoMonitoringRegionsDto.getParentIndexCode() : "-1");
        paramMap.put("resourceType", videoMonitoringRegionsDto.getResourceType() != null ? videoMonitoringRegionsDto.getResourceType() : "region");
        paramMap.put("pageNo", videoMonitoringRegionsDto.getPageNo() != null ? videoMonitoringRegionsDto.getPageNo().toString() : "1");
        paramMap.put("pageSize", videoMonitoringRegionsDto.getPageSize() != null ? videoMonitoringRegionsDto.getPageSize().toString() : "20");
        // 设置可选参数
        if (videoMonitoringRegionsDto.getCascadeFlag() != null) {
            paramMap.put("cascadeFlag", videoMonitoringRegionsDto.getCascadeFlag().toString());
        }
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<>();
        path.put(httpSchema, getCamsApi);
        JSONObject jsonObject = null;
        try {
            returnInfo = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
            log.info("根据区域编号获取下一级区域列表  returnInfo {} ", returnInfo);
            if(returnInfo != null ){
                jsonObject = JSON.parseObject(returnInfo);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return LcResult.success(jsonObject) ;
    }

    /**
     * 查询监控点列表
     * @Description: 查询监控点列表
     * @return LcResult
     * @Version 1.0
     */
    @PostMapping("/searchCameraList")
    @OperationLogEndpoint(module = "视频监控-查询监控点列表", operationType = OperationTypeEnum.QUERY, operationContent = "查询监控点列表")
    public LcResult searchCameraList(@RequestBody VideoMonitoringCameraDto videoMonitoringCameraDto) {
        String returnInfo = null;
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getCamsApi = ARTEMIS_PATH + "/api/resource/v2/camera/search";
        // 构建请求参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("pageNo", videoMonitoringCameraDto.getPageNo() != null ? videoMonitoringCameraDto.getPageNo() : 1);
        paramMap.put("pageSize", videoMonitoringCameraDto.getPageSize() != null ? videoMonitoringCameraDto.getPageSize() : 20);
        // 设置查询参数
        if (videoMonitoringCameraDto.getName() != null && !videoMonitoringCameraDto.getName().isEmpty()) {
            paramMap.put("name", videoMonitoringCameraDto.getName());
        }
        if (videoMonitoringCameraDto.getRegionIndexCodes() != null && !videoMonitoringCameraDto.getRegionIndexCodes().isEmpty()) {
            paramMap.put("regionIndexCodes", videoMonitoringCameraDto.getRegionIndexCodes());
            paramMap.put("isSubRegion", videoMonitoringCameraDto.getSubRegion() != null ? videoMonitoringCameraDto.getSubRegion() : false);
        }

        if (videoMonitoringCameraDto.getAuthCodes() != null && !videoMonitoringCameraDto.getAuthCodes().isEmpty()) {
            paramMap.put("authCodes", videoMonitoringCameraDto.getAuthCodes());
        }
        if (videoMonitoringCameraDto.getExpressions() != null && !videoMonitoringCameraDto.getExpressions().isEmpty()) {
            paramMap.put("expressions", videoMonitoringCameraDto.getExpressions());
        }
        if (videoMonitoringCameraDto.getOrderBy() != null && !videoMonitoringCameraDto.getOrderBy().isEmpty()) {
            paramMap.put("orderBy", videoMonitoringCameraDto.getOrderBy());
        }
        if (videoMonitoringCameraDto.getOrderType() != null && !videoMonitoringCameraDto.getOrderType().isEmpty()) {
            paramMap.put("orderType", videoMonitoringCameraDto.getOrderType());
        }
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<>();
        path.put(httpSchema, getCamsApi);
        JSONObject jsonObject = null;
        try {
            returnInfo = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
            log.info("查询监控点列表  returnInfo {} ", returnInfo);
            if(returnInfo != null ){
                jsonObject = JSON.parseObject(returnInfo);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return LcResult.success(jsonObject) ;
    }

    /**
     * 获取监控点预览取流URL
     * @Description: 获取监控点预览取流URL
     * @return LcResult
     * @Version 1.0
     */
    @PostMapping("/previewURLs")
    @OperationLogEndpoint(module = "视频监控-获取监控点预览取流URL", operationType = OperationTypeEnum.QUERY, operationContent = "获取监控点预览取流URL")
    public LcResult previewURLs(@RequestBody VideoMonitoringPreviewUrlDto videoMonitoringPreviewUrlDto) {
        String returnInfo = null;
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getCamsApi = ARTEMIS_PATH + "/api/video/v2/cameras/previewURLs";
        // 构建请求参数
        Map<String, Object> paramMap = new HashMap<>();
        // 设置必要参数
        if (videoMonitoringPreviewUrlDto.getCameraIndexCode() != null && !videoMonitoringPreviewUrlDto.getCameraIndexCode().isEmpty()) {
            paramMap.put("cameraIndexCode", videoMonitoringPreviewUrlDto.getCameraIndexCode());
        } else {
            throw new RuntimeException("监控点唯一标识(cameraIndexCode)不能为空");
        }
        // 设置可选参数
        if (videoMonitoringPreviewUrlDto.getStreamType() != null) {
            paramMap.put("streamType", videoMonitoringPreviewUrlDto.getStreamType());
        }
        if (videoMonitoringPreviewUrlDto.getProtocol() != null && !videoMonitoringPreviewUrlDto.getProtocol().isEmpty()) {
            paramMap.put("protocol", videoMonitoringPreviewUrlDto.getProtocol());
        }
        if (videoMonitoringPreviewUrlDto.getTransmode() != null) {
            paramMap.put("transmode", videoMonitoringPreviewUrlDto.getTransmode());
        }
        if (videoMonitoringPreviewUrlDto.getExpand() != null && !videoMonitoringPreviewUrlDto.getExpand().isEmpty()) {
            paramMap.put("expand", videoMonitoringPreviewUrlDto.getExpand());
        }
        if (videoMonitoringPreviewUrlDto.getStreamform() != null && !videoMonitoringPreviewUrlDto.getStreamform().isEmpty()) {
            paramMap.put("streamform", videoMonitoringPreviewUrlDto.getStreamform());
        }
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<>();
        path.put(httpSchema, getCamsApi);
        JSONObject jsonObject = null;
        try {
            returnInfo = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
            log.info("获取监控点预览取流URL  returnInfo {} ", returnInfo);
            if(returnInfo != null ){
                jsonObject = JSON.parseObject(returnInfo);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return LcResult.success(jsonObject) ;
    }

    /**
     * 获取监控点回放取流URL
     * @Description: 获取监控点回放取流URL
     * @return LcResult
     * @Version 1.0
     */
    @PostMapping("/playbackUrls")
    @OperationLogEndpoint(module = "视频监控-获取监控点回放取流URL", operationType = OperationTypeEnum.QUERY, operationContent = "获取监控点回放取流URL")
    public LcResult playbackUrls(@RequestBody  VideoMonitoringPlaybackUrlDto videoMonitoringPlaybackUrlDto) {
        String returnInfo = null;
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getCamsApi = ARTEMIS_PATH + "/api/video/v2/cameras/playbackURLs";
        // 构建请求参数
        Map<String, Object> paramMap = new HashMap<>();
        // 设置必要参数
        if (videoMonitoringPlaybackUrlDto.getCameraIndexCode() != null && !videoMonitoringPlaybackUrlDto.getCameraIndexCode().isEmpty()) {
            paramMap.put("cameraIndexCode", videoMonitoringPlaybackUrlDto.getCameraIndexCode());
        } else {
            throw new RuntimeException("监控点唯一标识(cameraIndexCode)不能为空");
        }
        if (videoMonitoringPlaybackUrlDto.getBeginTime() != null && !videoMonitoringPlaybackUrlDto.getBeginTime().isEmpty()) {
            // 验证并格式化时间
            String formattedBeginTime = formatDateTime(videoMonitoringPlaybackUrlDto.getBeginTime());
            paramMap.put("beginTime", formattedBeginTime);
        } else {
            throw new RuntimeException("开始查询时间(beginTime)不能为空，格式示例：2025-07-14T00:00:00+08:00");
        }
        if (videoMonitoringPlaybackUrlDto.getEndTime() != null && !videoMonitoringPlaybackUrlDto.getEndTime().isEmpty()) {
            // 验证并格式化时间
            String formattedEndTime = formatDateTime(videoMonitoringPlaybackUrlDto.getEndTime());
            paramMap.put("endTime", formattedEndTime);
        } else {
            throw new RuntimeException("结束查询时间(endTime)不能为空，格式示例：2025-07-14T01:00:00+08:00");
        }
        // 设置可选参数
        if (videoMonitoringPlaybackUrlDto.getRecordLocation() != null) {
            paramMap.put("recordLocation", videoMonitoringPlaybackUrlDto.getRecordLocation());
        }
        if (videoMonitoringPlaybackUrlDto.getProtocol() != null && !videoMonitoringPlaybackUrlDto.getProtocol().isEmpty()) {
            paramMap.put("protocol", videoMonitoringPlaybackUrlDto.getProtocol());
        }
        if (videoMonitoringPlaybackUrlDto.getTransmode() != null) {
            paramMap.put("transmode", videoMonitoringPlaybackUrlDto.getTransmode());
        }
        if (videoMonitoringPlaybackUrlDto.getUuid() != null && !videoMonitoringPlaybackUrlDto.getUuid().isEmpty()) {
            paramMap.put("uuid", videoMonitoringPlaybackUrlDto.getUuid());
        }
        if (videoMonitoringPlaybackUrlDto.getExpand() != null && !videoMonitoringPlaybackUrlDto.getExpand().isEmpty()) {
            paramMap.put("expand", videoMonitoringPlaybackUrlDto.getExpand());
        }
        if (videoMonitoringPlaybackUrlDto.getStreamform() != null && !videoMonitoringPlaybackUrlDto.getStreamform().isEmpty()) {
            paramMap.put("streamform", videoMonitoringPlaybackUrlDto.getStreamform());
        }
        if (videoMonitoringPlaybackUrlDto.getLockType() != null) {
            paramMap.put("lockType", videoMonitoringPlaybackUrlDto.getLockType());
        }
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<>();
        path.put(httpSchema, getCamsApi);
        JSONObject jsonObject = null;
        log.info("获取监控点回放取流URL  body {} ", body);

        try {
            returnInfo = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
            log.info("获取监控点回放取流URL  returnInfo {} ", returnInfo);
            if(returnInfo != null ){
                jsonObject = JSON.parseObject(returnInfo);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return LcResult.success(jsonObject) ;
    }

    /**
     * 格式化日期时间以符合海康威视API要求
     * @param dateTime 日期时间字符串
     * @return 格式化后的日期时间字符串
     */
    private String formatDateTime(String dateTime) {
        // 如果已经符合标准ISO格式，则直接返回
        if (dateTime.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{3})?[+-]\\d{2}:\\d{2}")) {
            log.info("日期时间 {} 已经符合标准ISO格式", dateTime);
            return dateTime;
        }
        // 如果是UTC格式 (Z结尾)
        if (dateTime.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{3})?Z")) {
            // 替换Z为+00:00
            return dateTime.replace("Z", "+00:00");
        }
        // 如果没有时区信息，添加默认时区+08:00 (北京时间)
        if (dateTime.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{3})?")) {
            return dateTime + "+08:00";
        }
        // 其他情况直接返回原始值，让API决定是否接受
        return dateTime;
    }
}
