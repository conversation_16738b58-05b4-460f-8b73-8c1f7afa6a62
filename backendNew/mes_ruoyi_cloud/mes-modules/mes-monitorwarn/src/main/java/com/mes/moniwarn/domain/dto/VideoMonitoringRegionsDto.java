package com.mes.moniwarn.domain.dto;

import java.util.List;
import java.io.Serializable;

/**
 * 视频监控区域信息
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class VideoMonitoringRegionsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 父区域编号，-1表示根节点
     */
    private String parentIndexCode;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 当前页码
     */
    private Integer pageNo;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 权限码集合
     */
    private List<String> authCodes;

    /**
     * 级联标识
     */
    private Integer cascadeFlag = 0;

    public String getParentIndexCode() {
        return parentIndexCode;
    }

    public void setParentIndexCode(String parentIndexCode) {
        this.parentIndexCode = parentIndexCode;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getAuthCodes() {
        return authCodes;
    }

    public void setAuthCodes(List<String> authCodes) {
        this.authCodes = authCodes;
    }

    public Integer getCascadeFlag() {
        return cascadeFlag;
    }

    public void setCascadeFlag(Integer cascadeFlag) {
        this.cascadeFlag = cascadeFlag;
    }

    @Override
    public String toString() {
        return "VideoMonitoringRegionsDto{" +
                "parentIndexCode='" + parentIndexCode + '\'' +
                ", resourceType='" + resourceType + '\'' +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", authCodes=" + authCodes +
                ", cascadeFlag=" + cascadeFlag +
                '}';
    }
}
