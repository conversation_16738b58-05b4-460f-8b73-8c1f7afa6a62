package com.mes.moniwarn.domain.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 视频监控区域信息
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class VideoMonitoringCameraDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 监控点名称，用于模糊搜索 */
    private String name;

    /** 区域编号列表 */
    private List<String> regionIndexCodes;

    /** 是否包含子区域 */
    private Boolean isSubRegion;

    /** 当前页码 */
    private Integer pageNo;

    /** 分页大小 */
    private Integer pageSize;

    /** 权限码集合 */
    private List<String> authCodes;

    /** 查询表达式 */
    private List<VideoMonitoringRegionsDto> expressions;

    /** 排序字段 */
    private String orderBy;

    /** 排序类型 */
    private String orderType;

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getRegionIndexCodes() {
        return regionIndexCodes;
    }

    public void setRegionIndexCodes(List<String> regionIndexCodes) {
        this.regionIndexCodes = regionIndexCodes;
    }

    public Boolean getSubRegion() {
        return isSubRegion;
    }

    public void setSubRegion(Boolean subRegion) {
        isSubRegion = subRegion;
    }

    public List<VideoMonitoringRegionsDto> getExpressions() {
        return expressions;
    }

    public void setExpressions(List<VideoMonitoringRegionsDto> expressions) {
        this.expressions = expressions;
    }

    public List<String> getAuthCodes() {
        return authCodes;
    }

    public void setAuthCodes(List<String> authCodes) {
        this.authCodes = authCodes;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    @Override
    public String toString() {
        return "VideoMonitoringCameraDto{" +
                "name='" + name + '\'' +
                ", regionIndexCodes=" + regionIndexCodes +
                ", isSubRegion=" + isSubRegion +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", authCodes=" + authCodes +
                ", expressions=" + expressions +
                ", orderBy='" + orderBy + '\'' +
                ", orderType='" + orderType + '\'' +
                '}';
    }
}
