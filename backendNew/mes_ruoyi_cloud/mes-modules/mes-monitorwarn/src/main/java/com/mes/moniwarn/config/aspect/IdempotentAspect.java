package com.mes.moniwarn.config.aspect;

import cn.hutool.core.util.StrUtil;
import com.mes.moniwarn.annotation.Idempotent;
import com.mes.moniwarn.config.IdempotentProperties;
import com.mes.moniwarn.enums.IdempotentTypeEnum;
import com.mes.moniwarn.exception.IdempotentException;
import com.mes.moniwarn.service.IdempotentService;
import com.mes.moniwarn.utils.IdempotentKeyGenerator;
import com.mes.moniwarn.utils.IpWhitelistUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 幂等性切面
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Aspect
@Slf4j
@Component
@Order(2) // 设置为2，确保在OperationLogEndpointAspect之后执行
public class IdempotentAspect extends BaseAspectSupport {

    @Autowired
    private IdempotentService idempotentService;

    @Autowired
    private IdempotentProperties idempotentProperties;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 定义切点：拦截所有标注了@Idempotent注解的方法
     */
    @Pointcut("@annotation(com.mes.moniwarn.annotation.Idempotent)")
    public void idempotentPointcut() {
    }

    /**
     * 环绕通知：处理幂等性逻辑
     */
    @Around("idempotentPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 检查是否启用幂等性功能
        if (!isIdempotentEnabled()) {
            return joinPoint.proceed();
        }

        // 检查是否为排除路径
        if (isExcludedPath()) {
            return joinPoint.proceed();
        }

        // 检查IP白名单
        if (isInIpWhitelist()) {
            log.debug("客户端IP在白名单中，跳过幂等性检查");
            return joinPoint.proceed();
        }

        // 获取方法和注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Idempotent idempotent = method.getAnnotation(Idempotent.class);

        if (idempotent == null) {
            return joinPoint.proceed();
        }

        String idempotentKey = null;
        boolean lockAcquired = false;

        try {
            // 生成幂等性key
            idempotentKey = IdempotentKeyGenerator.generateKey(joinPoint, idempotent);
            log.debug("生成幂等性key: {}", idempotentKey);

            // 处理Token类型的特殊逻辑
            if (IdempotentTypeEnum.TOKEN.equals(idempotent.type())) {
                return handleTokenIdempotent(joinPoint, idempotent, idempotentKey);
            }

            // 尝试获取幂等性锁
            lockAcquired = idempotentService.tryLock(idempotentKey, idempotent.expireTime(), idempotent.timeUnit());

            if (!lockAcquired) {
                log.warn("幂等性检查失败，重复请求: {}", idempotentKey);
                throw new IdempotentException(idempotent.message());
            }

            log.debug("幂等性检查通过，执行业务方法: {}", idempotentKey);

            // 执行目标方法
            Object result = joinPoint.proceed();

            log.debug("业务方法执行完成: {}", idempotentKey);
            return result;

        } catch (IdempotentException e) {
            // 重新抛出幂等性异常
            throw e;
        } catch (Throwable e) {
            log.error("幂等性处理异常: {}", idempotentKey, e);
            throw e;
        } finally {
            // 根据配置决定是否删除key
            if (lockAcquired && idempotent.delKey() && idempotentKey != null) {
                idempotentService.releaseLock(idempotentKey);
                log.debug("业务完成后删除幂等性key: {}", idempotentKey);
            }
        }
    }

    /**
     * 处理基于Token的幂等性
     */
    private Object handleTokenIdempotent(ProceedingJoinPoint joinPoint, Idempotent idempotent, String tokenKey) throws Throwable {
        // 从key中提取token
        String token = extractTokenFromKey(tokenKey);

        // 验证并消费Token
        boolean tokenValid = idempotentService.validateAndConsumeToken(token);

        if (!tokenValid) {
            log.warn("Token验证失败，重复请求或Token无效: {}", token);
            throw new IdempotentException(idempotent.message());
        }

        log.debug("Token验证通过，执行业务方法: {}", token);

        // 执行目标方法
        return joinPoint.proceed();
    }

    /**
     * 从key中提取token
     */
    private String extractTokenFromKey(String tokenKey) {
        // tokenKey格式: idempotent:token:actualToken
        String[] parts = tokenKey.split(":");
        if (parts.length >= 3) {
            return parts[2];
        }
        throw new IllegalArgumentException("无效的Token key格式: " + tokenKey);
    }

    /**
     * 检查是否启用幂等性功能
     */
    private boolean isIdempotentEnabled() {
        // 检查全局开关
        if (!idempotentProperties.isEnabled()) {
            log.debug("幂等性功能已全局禁用");
            return false;
        }

        // 检查开发环境禁用
        if (idempotentProperties.isDisableInDev() && "dev".equals(activeProfile)) {
            log.debug("开发环境下幂等性功能已禁用");
            return false;
        }

        return true;
    }

    /**
     * 检查是否为排除路径
     */
    private boolean isExcludedPath() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return false;
            }

            HttpServletRequest request = attributes.getRequest();
            String requestPath = request.getRequestURI();

            for (String excludePath : idempotentProperties.getExcludePaths()) {
                if (pathMatcher.match(excludePath, requestPath)) {
                    log.debug("路径 {} 在排除列表中，跳过幂等性检查", requestPath);
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.warn("检查排除路径时发生异常", e);
            return false;
        }
    }

    /**
     * 检查客户端IP是否在白名单中
     */
    private boolean isInIpWhitelist() {
        // 检查是否启用白名单功能
        if (!idempotentProperties.isEnableWhitelist()) {
            return false;
        }

        // 检查白名单是否为空
        String[] whitelistIps = idempotentProperties.getWhitelistIps();
        if (whitelistIps == null || whitelistIps.length == 0) {
            return false;
        }

        try {
            // 获取客户端IP
            String clientIp = getClientIp();
            if (StrUtil.isBlank(clientIp) || "unknown".equals(clientIp)) {
                return false;
            }

            // 检查是否在白名单中
            boolean inWhitelist = IpWhitelistUtil.isInWhitelist(clientIp, whitelistIps);

            if (inWhitelist) {
                log.debug("客户端IP {} 在白名单中", clientIp);

                // 根据配置决定是否跳过检查
                if (idempotentProperties.isWhitelistSkipCheck()) {
                    log.debug("白名单IP完全跳过幂等性检查");
                    return true;
                } else {
                    log.debug("白名单IP使用宽松的幂等性规则");
                    // 这里可以实现更宽松的规则，比如延长过期时间等
                    return false;
                }
            }

            return false;
        } catch (Exception e) {
            log.warn("检查IP白名单时发生异常", e);
            return false;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return "unknown";
            }

            HttpServletRequest request = attributes.getRequest();
            String ip = request.getHeader("X-Forwarded-For");

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-Real-IP");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }

            // 处理多个IP的情况，取第一个
            if (StrUtil.isNotBlank(ip) && ip.contains(",")) {
                ip = ip.split(",")[0].trim();
            }

            return StrUtil.isNotBlank(ip) ? ip : "unknown";
        } catch (Exception e) {
            log.warn("获取客户端IP失败", e);
            return "unknown";
        }
    }
}
