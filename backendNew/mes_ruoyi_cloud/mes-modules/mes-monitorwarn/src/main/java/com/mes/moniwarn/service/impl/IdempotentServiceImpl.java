package com.mes.moniwarn.service.impl;

import cn.hutool.core.util.IdUtil;
import com.mes.common.redis.service.RedisService;
import com.mes.moniwarn.service.IdempotentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 幂等性服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Service
public class IdempotentServiceImpl implements IdempotentService {

    private static final String TOKEN_PREFIX = "idempotent:token:";
    private static final String LOCK_VALUE = "locked";

    @Autowired
    private RedisService redisService;

    @Override
    public boolean tryLock(String key, long expireTime, TimeUnit timeUnit) {
        try {
            // 检查key是否已存在
            if (redisService.hasKey(key)) {
                log.warn("幂等性检查失败，key已存在: {}", key);
                return false;
            }

            // 设置key，如果key不存在则设置成功
            redisService.setCacheObject(key, LOCK_VALUE, expireTime, timeUnit);

            // 再次检查确保设置成功（防止并发情况）
            if (redisService.hasKey(key)) {
                log.debug("幂等性锁获取成功: {}", key);
                return true;
            } else {
                log.warn("幂等性锁获取失败，并发冲突: {}", key);
                return false;
            }
        } catch (Exception e) {
            log.error("幂等性锁获取异常: {}", key, e);
            return false;
        }
    }

    @Override
    public void releaseLock(String key) {
        try {
            redisService.deleteObject(key);
            log.debug("幂等性锁释放成功: {}", key);
        } catch (Exception e) {
            log.error("幂等性锁释放异常: {}", key, e);
        }
    }

    @Override
    public boolean exists(String key) {
        try {
            return redisService.hasKey(key);
        } catch (Exception e) {
            log.error("检查幂等性key存在性异常: {}", key, e);
            return false;
        }
    }

    @Override
    public String generateToken() {
        try {
            // 生成唯一Token
            String token = IdUtil.fastSimpleUUID();
            String tokenKey = TOKEN_PREFIX + token;

            // 将Token存储到Redis，默认30分钟过期
            redisService.setCacheObject(tokenKey, token, 30L, TimeUnit.MINUTES);

            log.debug("幂等性Token生成成功: {}", token);
            return token;
        } catch (Exception e) {
            log.error("生成幂等性Token异常", e);
            throw new RuntimeException("生成幂等性Token失败", e);
        }
    }

    @Override
    public boolean validateAndConsumeToken(String token) {
        try {
            String tokenKey = TOKEN_PREFIX + token;

            // 检查Token是否存在
            if (!redisService.hasKey(tokenKey)) {
                log.warn("幂等性Token验证失败，Token不存在或已过期: {}", token);
                return false;
            }

            // 消费Token（删除）
            boolean deleted = redisService.deleteObject(tokenKey);
            if (deleted) {
                log.debug("幂等性Token验证并消费成功: {}", token);
                return true;
            } else {
                log.warn("幂等性Token消费失败: {}", token);
                return false;
            }
        } catch (Exception e) {
            log.error("验证幂等性Token异常: {}", token, e);
            return false;
        }
    }
}
