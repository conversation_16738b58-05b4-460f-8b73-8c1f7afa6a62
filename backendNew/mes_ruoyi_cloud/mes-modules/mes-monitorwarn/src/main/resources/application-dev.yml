# spring配置
spring:
  redis:
    host: *************
    port: 6379
    #password: test1234
  datasource:
    primary:
      driver-class-name: dm.jdbc.driver.DmDriver
      jdbc-url: jdbc:dm://*************:5236/CNEMC_ENVIRONMENT
      username: SYSDBA
      password: SYSDBA_dm001
    secondary:
      driver-class-name: dm.jdbc.driver.DmDriver
      jdbc-url: jdbc:dm://*************:5236/CNEMC_ENVIRONMENT_DATA
      username: SYSDBA
      password: SYSDBA_dm001
  rocket-api:
    open-api-enabled: Y
    open-api-platform-name: apifox
    open-api-platform-url: https://api.apifox.com/api/v1/projects/
    open-api-platform-token: APS-NEmPMflAUxkZMGJMFKNGZ5zytk9Gs9B9
    open-api-platform-project-id: 6585333
    filter:
      directory:
      api:
# mybatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.mes.moniwarn
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath:mapper/**/*.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# springdoc配置
springdoc:
  gatewayUrl: http://localhost:8080/${spring.application.name}
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '监控预警模块接口文档'
    # 描述
    description: '监控预警模块接口描述'
    # 作者信息
    contact:
      name: RuoYi
      url: https://ruoyi.vip

powerjobclient:
  address: ************:28089
  appName: mes
  appSecret: mes

# 幂等性配置
idempotent:
  # 是否启用幂等性功能，默认关闭
  enabled: false
  # 默认过期时间（秒）
  default-expire-time: 5
  # 是否启用IP限制模式（推荐）
  ip-limit-mode: true
  # IP限制模式下的过期时间（秒），默认3秒
  ip-limit-expire-time: 3
  # 是否在开发环境下禁用
  disable-in-dev: true
  # 排除的接口路径
  exclude-paths:
    - /idempotent/**
    - /actuator/**
    - /swagger-ui/**
    - /v3/api-docs/**
  # 是否启用IP白名单功能
  enable-whitelist: true
  # 白名单模式：true=完全跳过检查（推荐压测），false=使用宽松规则
  whitelist-skip-check: true
  # IP白名单配置（压测时配置压测机器IP）
  whitelist-ips:
    # 示例配置（请根据实际情况修改）
    # - "127.0.0.1"              # 本地IP
    # - "*************"          # 单个IP
    # - "***********/24"         # CIDR格式
    # - "***********-*************"  # IP段
    # - "10.0.0.*"               # 通配符

hikvision:
  host: ***********:30443
  appKey: 25837205
  appSecret: CCuDRGozrvUcHoBcadz2
  httpSchema: https://
