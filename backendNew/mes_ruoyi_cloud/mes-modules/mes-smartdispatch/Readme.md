# 项目说明
# tower-smartdispatch 智能调度
# 打包镜像
docker build -t smartdispatch:1.0.0 .
## mac 打x86架构镜像:
docker build --platform linux/amd64 -t smartdispatch:1.0.0 .

# 删除镜像
docker rmi smartdispatch:1.0.0

# 导出镜像包
docker save -o smartdispatch.tar smartdispatch:1.0.0

# 导出镜像包并压缩 使用此命令
docker save smartdispatch:1.0.0 | gzip > smartdispatch.tar.gz

# 导入镜像包
docker load -i smartdispatch.tar
docker load -i smartdispatch.tar.gz

# 启动镜像
docker run -d --name smartdispatch -p 19094:19094 -e "SPRING_PROFILES_ACTIVE=dev" smartdispatch:1.0.0
# test
docker run --rm -it smartdispatch:1.0.0 uname -m

## 1、查找容器：
docker ps -a | grep smartdispatch
docker start smartdispatch

## 2、停止容器（如果容器正在运行）：
docker stop smartdispatch

## 3、删除容器：
docker rm smartdispatch

## 4、启动命令：
docker run -d --name smartdispatch -p 19094:19094 -e "SPRING_PROFILES_ACTIVE=dev" smartdispatch:1.0.0

## 5、查看容器日志：
docker logs -f smartdispatch

## 镜像列表
docker images
REPOSITORY       TAG       IMAGE ID       CREATED         SIZE
hqy/rocket-api   latest    42dbcce089fa   4 hours ago     352MB

## 删除镜像
docker rmi 42dbcce089fa

# 现场开发环境
Nacos
**************:18848
Redis:
host: **************
port: 6379
password: mestest
datasource:
driver-class-name: dm.jdbc.driver.DmDriver
url:  jdbc:dm://*************:30033/MES_CLOUD?SCHEMA=MES_CLOUD&charset=UTF-8
username: SYSADMIN
password: @MXDtIKoOU2j


fuck-u-code analyze --top 999 --verbose --markdown > 智能调度模块-代码质量报告.md

