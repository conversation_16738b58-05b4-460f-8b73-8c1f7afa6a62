# 基础镜像
# FROM  openjdk:8-jre
# FROM hub-nj.iwhalecloud.com/public/openjdk_java8_ci_20171128:latest
# 需通公网下载jdk基础镜像
FROM docker.m.daocloud.io/library/openjdk:8-jre

# author
MAINTAINER hu.qingyun

ARG JAR_FILE=backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/target/mes-smartdispatch.jar

# 挂载目录
VOLUME /home/<USER>/smartdispatch
# 创建目录
RUN mkdir -p /home/<USER>/smartdispatch
# 指定路径
WORKDIR /home/<USER>/smartdispatch
# 本地 复制jar文件到路径
COPY ./target/mes-smartdispatch.jar /home/<USER>/smartdispatch/mes-smartdispatch.jar
# 服务器  复制jar文件到路径
#COPY ${JAR_FILE} /home/<USER>/smartdispatch/mes-smartdispatch.jar

# 启动系统服务
ENTRYPOINT ["java","-jar","mes-smartdispatch.jar"]
