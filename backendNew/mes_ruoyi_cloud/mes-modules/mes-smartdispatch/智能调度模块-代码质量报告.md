# 智能调度模块-代码分析报告

## 总体评估

- **质量评分**: 25.94/100
- **质量等级**: 🌸 基本没事，但是有伤风化
- **分析文件数**: 194
- **代码总行数**: 20966

## 质量指标

| 指标 | 得分 | 权重 | 状态 |
|------|------|------|------|
| 循环复杂度 | 2.81 | 0.25 | ✓✓ |
| 状态管理 | 8.06 | 0.15 | ✓✓ |
| 注释覆盖率 | 19.48 | 0.15 | ✓✓ |
| 命名规范 | 25.00 | 0.10 | ✓ |
| 错误处理 | 35.00 | 0.15 | ○ |
| 代码结构 | 45.00 | 0.20 | ○ |
| 代码重复度 | 55.00 | 0.15 | • |

## 问题文件 (Top 999)

### 1. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/TargetMgrServiceImpl.java (得分: 52.76)
**问题分类**: 🔄 复杂度问题:26, ⚠️ 其他问题:6

**主要问题**:
- 函数 LoggerFactory.getLogger 的循环复杂度过高 (35)，考虑重构
- 函数 qryCollectNumTargetList 的循环复杂度过高 (35)，考虑重构
- 函数 qryNationwideCollectNumTargetList 的循环复杂度过高 (35)，考虑重构
- 函数 qryProvinceCollectNumTargetList 的循环复杂度过高 (35)，考虑重构
- 函数 saveNumTarget 的循环复杂度过高 (35)，考虑重构
- 函数 delNumTarget 的循环复杂度过高 (35)，考虑重构
- 函数 qryNumTargetFileList 的循环复杂度过高 (35)，考虑重构
- 函数 saveNumTargetFile 的循环复杂度过高 (35)，考虑重构
- 函数 delNumTargetFile 的循环复杂度过高 (35)，考虑重构
- 函数 qryTargetFileAttachInfo 的循环复杂度过高 (35)，考虑重构
- 函数 qryQualityTargetList 的循环复杂度过高 (35)，考虑重构
- 函数 saveQualityTarget 的循环复杂度过高 (35)，考虑重构
- 函数 delQualityTarget 的循环复杂度过高 (35)，考虑重构
- 函数 'LoggerFactory.getLogger' () 过长 (92 行)，建议拆分
- 函数 'LoggerFactory.getLogger' () 复杂度严重过高 (35)，必须简化
- 函数 'qryCollectNumTargetList' () 复杂度严重过高 (35)，必须简化
- 函数 'qryNationwideCollectNumTargetList' () 过长 (54 行)，建议拆分
- 函数 'qryNationwideCollectNumTargetList' () 复杂度严重过高 (35)，必须简化
- 函数 'qryProvinceCollectNumTargetList' () 过长 (61 行)，建议拆分
- 函数 'qryProvinceCollectNumTargetList' () 复杂度严重过高 (35)，必须简化
- 函数 'saveNumTarget' () 过长 (65 行)，建议拆分
- 函数 'saveNumTarget' () 复杂度严重过高 (35)，必须简化
- 函数 'delNumTarget' () 复杂度严重过高 (35)，必须简化
- 函数 'qryNumTargetFileList' () 复杂度严重过高 (35)，必须简化
- 函数 'saveNumTargetFile' () 复杂度严重过高 (35)，必须简化
- 函数 'delNumTargetFile' () 复杂度严重过高 (35)，必须简化
- 函数 'qryTargetFileAttachInfo' () 复杂度严重过高 (35)，必须简化
- 函数 'qryQualityTargetList' () 过长 (65 行)，建议拆分
- 函数 'qryQualityTargetList' () 复杂度严重过高 (35)，必须简化
- 函数 'saveQualityTarget' () 过长 (62 行)，建议拆分
- 函数 'saveQualityTarget' () 复杂度严重过高 (35)，必须简化
- 函数 'delQualityTarget' () 复杂度严重过高 (35)，必须简化

### 2. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/utils/IPUtils.java (得分: 48.92)
**问题分类**: 🔄 复杂度问题:10, ⚠️ 其他问题:2

**主要问题**:
- 函数 LoggerFactory.getLogger 的循环复杂度过高 (20)，考虑重构
- 函数 getIpAddr 的循环复杂度过高 (20)，考虑重构
- 函数 isUnvalid 的循环复杂度过高 (20)，考虑重构
- 函数 printRequest 的循环复杂度过高 (20)，考虑重构
- 函数 getSystem 的循环复杂度过高 (20)，考虑重构
- 函数 'LoggerFactory.getLogger' () 复杂度严重过高 (20)，必须简化
- 函数 'getIpAddr' () 较长 (39 行)，可考虑重构
- 函数 'getIpAddr' () 复杂度严重过高 (20)，必须简化
- 函数 'isUnvalid' () 复杂度严重过高 (20)，必须简化
- 函数 'printRequest' () 复杂度严重过高 (20)，必须简化
- 函数 'getSystem' () 较长 (34 行)，可考虑重构
- 函数 'getSystem' () 复杂度严重过高 (20)，必须简化

### 3. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/GenericMgrServiceImpl.java (得分: 46.19)
**问题分类**: 🔄 复杂度问题:34, ⚠️ 其他问题:4

**主要问题**:
- 函数 getSimAreaTreeInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getAreaTreeInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getActivityParentType 的循环复杂度过高 (21)，考虑重构
- 函数 getActivityType 的循环复杂度过高 (21)，考虑重构
- 函数 getProvinceInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getCityInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getBusinessType 的循环复杂度过高 (21)，考虑重构
- 函数 getSiteType 的循环复杂度过高 (21)，考虑重构
- 函数 getPlanCategory 的循环复杂度过高 (21)，考虑重构
- 函数 getSiteInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getPackageTreeInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getRegionTreeInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getPackageProvinceTreeInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getRegionProvinceTreeInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getRegionMaintainUnitTreeInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getMaintainUnitInfo 的循环复杂度过高 (21)，考虑重构
- 函数 getAreaTreeList 的循环复杂度过高 (21)，考虑重构
- 函数 'getSimAreaTreeInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getAreaTreeInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getActivityParentType' () 复杂度严重过高 (21)，必须简化
- 函数 'getActivityType' () 复杂度严重过高 (21)，必须简化
- 函数 'getProvinceInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getCityInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getBusinessType' () 复杂度严重过高 (21)，必须简化
- 函数 'getSiteType' () 复杂度严重过高 (21)，必须简化
- 函数 'getPlanCategory' () 复杂度严重过高 (21)，必须简化
- 函数 'getSiteInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getPackageTreeInfo' () 过长 (51 行)，建议拆分
- 函数 'getPackageTreeInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getRegionTreeInfo' () 较长 (44 行)，可考虑重构
- 函数 'getRegionTreeInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getPackageProvinceTreeInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getRegionProvinceTreeInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getRegionMaintainUnitTreeInfo' () 较长 (43 行)，可考虑重构
- 函数 'getRegionMaintainUnitTreeInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getMaintainUnitInfo' () 复杂度严重过高 (21)，必须简化
- 函数 'getAreaTreeList' () 较长 (45 行)，可考虑重构
- 函数 'getAreaTreeList' () 复杂度严重过高 (21)，必须简化

### 4. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/PlanMgrServiceImpl.java (得分: 43.51)
**问题分类**: 🔄 复杂度问题:22, ⚠️ 其他问题:1

**主要问题**:
- 函数 qryPlanRule 的循环复杂度过高 (16)，考虑重构
- 函数 delPlanRule 的循环复杂度过高 (16)，考虑重构
- 函数 verifyPlanRule 的循环复杂度过高 (16)，考虑重构
- 函数 qryAssoPlanRule 的循环复杂度过高 (16)，考虑重构
- 函数 qryPlanList 的循环复杂度过高 (16)，考虑重构
- 函数 delPlanInfo 的循环复杂度过高 (16)，考虑重构
- 函数 qryPlanApprovalList 的循环复杂度过高 (16)，考虑重构
- 函数 verifyPlanInfo 的循环复杂度过高 (16)，考虑重构
- 函数 qrySiteLinkDevice 的循环复杂度过高 (16)，考虑重构
- 函数 qryPlanDetail 的循环复杂度过高 (16)，考虑重构
- 函数 addApprovalInfo 的循环复杂度过高 (16)，考虑重构
- 函数 'qryPlanRule' () 较长 (32 行)，可考虑重构
- 函数 'qryPlanRule' () 复杂度严重过高 (16)，必须简化
- 函数 'delPlanRule' () 复杂度严重过高 (16)，必须简化
- 函数 'verifyPlanRule' () 复杂度严重过高 (16)，必须简化
- 函数 'qryAssoPlanRule' () 复杂度严重过高 (16)，必须简化
- 函数 'qryPlanList' () 复杂度严重过高 (16)，必须简化
- 函数 'delPlanInfo' () 复杂度严重过高 (16)，必须简化
- 函数 'qryPlanApprovalList' () 复杂度严重过高 (16)，必须简化
- 函数 'verifyPlanInfo' () 复杂度严重过高 (16)，必须简化
- 函数 'qrySiteLinkDevice' () 复杂度严重过高 (16)，必须简化
- 函数 'qryPlanDetail' () 复杂度严重过高 (16)，必须简化
- 函数 'addApprovalInfo' () 复杂度严重过高 (16)，必须简化

### 5. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/config/aspect/BaseAspectSupport.java (得分: 38.83)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率极低 (0.00%)，几乎没有注释

### 6. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/HomePageServiceImpl.java (得分: 38.73)
**问题分类**: 🔄 复杂度问题:20, ⚠️ 其他问题:3

**主要问题**:
- 函数 LoggerFactory.getLogger 的循环复杂度较高 (13)，建议简化
- 函数 qryTodayTaskStatic 的循环复杂度较高 (13)，建议简化
- 函数 qryTodayTaskList 的循环复杂度较高 (13)，建议简化
- 函数 qryQuantityTargetPerformance 的循环复杂度较高 (13)，建议简化
- 函数 qryQualityTargetPerformance 的循环复杂度较高 (13)，建议简化
- 函数 qryMaintainPersonStatic 的循环复杂度较高 (13)，建议简化
- 函数 setQuantityTargetValue 的循环复杂度较高 (13)，建议简化
- 函数 setQuantityTargetPerformanceValue 的循环复杂度较高 (13)，建议简化
- 函数 getMonthWeekCount 的循环复杂度较高 (13)，建议简化
- 函数 getWeekCount 的循环复杂度较高 (13)，建议简化
- 函数 'LoggerFactory.getLogger' () 复杂度过高 (13)，建议简化
- 函数 'qryTodayTaskStatic' () 复杂度过高 (13)，建议简化
- 函数 'qryTodayTaskList' () 复杂度过高 (13)，建议简化
- 函数 'qryQuantityTargetPerformance' () 复杂度过高 (13)，建议简化
- 函数 'qryQualityTargetPerformance' () 较长 (33 行)，可考虑重构
- 函数 'qryQualityTargetPerformance' () 复杂度过高 (13)，建议简化
- 函数 'qryMaintainPersonStatic' () 复杂度过高 (13)，建议简化
- 函数 'setQuantityTargetValue' () 较长 (41 行)，可考虑重构
- 函数 'setQuantityTargetValue' () 复杂度过高 (13)，建议简化
- 函数 'setQuantityTargetPerformanceValue' () 较长 (33 行)，可考虑重构
- 函数 'setQuantityTargetPerformanceValue' () 复杂度过高 (13)，建议简化
- 函数 'getMonthWeekCount' () 复杂度过高 (13)，建议简化
- 函数 'getWeekCount' () 复杂度过高 (13)，建议简化

### 7. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/PushTaskServiceImpl.java (得分: 37.52)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'LoggerFactory.getLogger' () 极度过长 (127 行)，必须拆分

### 8. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/BigScreenServiceImpl.java (得分: 35.90)
**问题分类**: ⚠️ 其他问题:2

**主要问题**:
- 函数 'LoggerFactory.getLogger' () 过长 (92 行)，建议拆分
- 函数 'qryDispatchEfficiencyStatic' () 较长 (43 行)，可考虑重构

### 9. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/config/aspect/OperationLogEndpointAspect.java (得分: 35.17)
**问题分类**: 📝 注释问题:1, ⚠️ 其他问题:1

**主要问题**:
- 函数 'around' () 较长 (41 行)，可考虑重构
- 代码注释率较低 (9.35%)，建议增加注释

### 10. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/enums/OperationTypeEnum.java (得分: 34.78)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率极低 (0.00%)，几乎没有注释

### 11. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/config/aspect/OperationLogEndpoint.java (得分: 34.78)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率极低 (0.00%)，几乎没有注释

### 12. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/feign/RemoteSmartdispatchInnerService.java (得分: 34.78)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率极低 (0.00%)，几乎没有注释

### 13. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/SiteTaskListVO.java (得分: 33.48)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率极低 (4.08%)，几乎没有注释

### 14. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/utils/DynamicTaskUtils.java (得分: 33.48)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率极低 (3.67%)，几乎没有注释

### 15. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ResSiteVO.java (得分: 33.48)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率极低 (4.35%)，几乎没有注释

### 16. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/feign/factory/RemoteSmartdispatchInnerFallbackFactory.java (得分: 32.96)
**问题分类**: 📝 注释问题:1, ⚠️ 其他问题:1

**主要问题**:
- 代码注释率较低 (7.69%)，建议增加注释
- 函数 'LoggerFactory.getLogger' () 较长 (37 行)，可考虑重构

### 17. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduTargetFileDTO.java (得分: 32.17)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (6.67%)，建议增加注释

### 18. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/RegionTreeVO.java (得分: 32.17)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (5.00%)，建议增加注释

### 19. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/TaskStaticQueryDTO.java (得分: 32.17)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (6.19%)，建议增加注释

### 20. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ResDevice.java (得分: 30.24)
**问题分类**: 📝 注释问题:1, ⚠️ 其他问题:1

**主要问题**:
- 函数 'toString' () 较长 (33 行)，可考虑重构
- 代码注释率较低 (8.55%)，建议增加注释

### 21. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/SysUserCus.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (8.27%)，建议增加注释

### 22. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduPlanRule.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (9.40%)，建议增加注释

### 23. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduTaskExtendWithTaskDTO.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (9.43%)，建议增加注释

### 24. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ResPersonBasic.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (9.95%)，建议增加注释

### 25. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/SiteTreeVO.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (7.69%)，建议增加注释

### 26. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduMonitorQualityResult.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (9.83%)，建议增加注释

### 27. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduMonitorActivityInfo.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (9.68%)，建议增加注释

### 28. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduMonitorQualityTarget.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (9.76%)，建议增加注释

### 29. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduPlanInfo.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (9.14%)，建议增加注释

### 30. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduTaskInfoDetailVO.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (9.30%)，建议增加注释

### 31. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ResSite.java (得分: 30.22)
**问题分类**: 📝 注释问题:1

**主要问题**:
- 代码注释率较低 (8.71%)，建议增加注释

### 32. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IGenericMgrService.java (得分: 29.04)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'getAreaTreeInfo' () 极度过长 (116 行)，必须拆分

### 33. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduTaskInfoMapper.java (得分: 29.04)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectMonthPersonAvgPlanCount' () 极度过长 (127 行)，必须拆分

### 34. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduPlanInfoMapper.java (得分: 29.04)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectCompletePlanCountByTime' () 极度过长 (172 行)，必须拆分

### 35. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduTargetFile.java (得分: 27.61)

### 36. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/QuantityTargetYearCollectVO.java (得分: 27.61)

### 37. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduPlanHis.java (得分: 27.61)

### 38. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/AreaTreeListResultVO.java (得分: 27.61)

### 39. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduMonitorQuantityTarget.java (得分: 27.61)

### 40. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduPlanAssorule.java (得分: 27.61)

### 41. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduTaskExtend.java (得分: 27.61)

### 42. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduTaskExtendHis.java (得分: 27.61)

### 43. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduLabTaskRecord.java (得分: 27.61)

### 44. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/SysAttachInfo.java (得分: 27.61)

### 45. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduTaskHis.java (得分: 27.61)

### 46. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/MesSmartDispathApplication.java (得分: 27.61)

### 47. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduPlanExtend.java (得分: 27.61)

### 48. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduAlgorithmInvokeRecord.java (得分: 27.61)

### 49. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/MaintenanceWorkCalendarVO.java (得分: 27.61)

### 50. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/SysDict.java (得分: 27.61)

### 51. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ResAreaInfo.java (得分: 27.61)

### 52. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/SysAttachInfoRel.java (得分: 27.61)

### 53. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/DevcControlRecordQueryDTO.java (得分: 27.61)

### 54. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/PubOperationLog.java (得分: 27.61)

### 55. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduApprovalRecord.java (得分: 27.61)

### 56. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/PubLogCallbackAccess.java (得分: 27.61)

### 57. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduAlgorithmInfo.java (得分: 27.61)

### 58. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ResDeviceQueryDTO.java (得分: 27.61)

### 59. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduDevcControlRecord.java (得分: 27.61)

### 60. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduAlgruleConfig.java (得分: 27.61)

### 61. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduTaskPlanRel.java (得分: 27.61)

### 62. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduPlanInfoDetailVO.java (得分: 27.61)

### 63. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduTaskAvgDuration.java (得分: 27.61)

### 64. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduTaskInfo.java (得分: 27.61)

### 65. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/RegionSiteCoutVO.java (得分: 27.61)

### 66. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/DevcControlListQueryDTO.java (得分: 27.61)

### 67. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/ScheduDevcControlConfig.java (得分: 27.61)

### 68. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/enums/PlanSourceEnum.java (得分: 27.09)

### 69. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IPlanMgrService.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'qryPlanRule' () 过长 (72 行)，建议拆分

### 70. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduAlgorithmInvokeRecordMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduAlgorithmInvokeRecordById' () 过长 (68 行)，建议拆分

### 71. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduPlanAssoruleMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduPlanAssoruleListByMainRuleId' () 过长 (58 行)，建议拆分

### 72. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduMonitorQualityResultMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduMonitorQualityResultById' () 过长 (74 行)，建议拆分

### 73. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduMonitorQualityTargetMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduMonitorQualityTargetCount' () 过长 (68 行)，建议拆分

### 74. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduTaskPlanRelMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectTaskStaticBySiteId' () 过长 (66 行)，建议拆分

### 75. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduAlgruleConfigMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduAlgruleConfigById' () 过长 (58 行)，建议拆分

### 76. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduMonitorActivityInfoMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduMonitorActivityInfoById' () 过长 (59 行)，建议拆分

### 77. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IOpenApiService.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'updateTaskStatus' () 过长 (72 行)，建议拆分

### 78. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ResPersonBasicMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectMaintainPersonStatic' () 过长 (76 行)，建议拆分

### 79. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduApprovalRecordMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectBizApprovalRecordList' () 过长 (66 行)，建议拆分

### 80. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduMonitorQuantityTargetMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectBizCollectQuantityTarget' () 过长 (95 行)，建议拆分

### 81. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduPlanExtendMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectPlanExtendByPlanIds' () 过长 (74 行)，建议拆分

### 82. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduTaskExtendMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectTodayTaskExecutorCount' () 过长 (81 行)，建议拆分

### 83. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduTargetFileMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectTargetFileWithAttachInfoById' () 过长 (51 行)，建议拆分

### 84. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/ITargetMgrService.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'qryNumTargetList' () 过长 (82 行)，建议拆分

### 85. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduTaskHisMapper.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectTaskTrend' () 过长 (66 行)，建议拆分

### 86. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IBigScreenService.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'qryCollectQuantityTargetPerformance' () 过长 (56 行)，建议拆分

### 87. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IDispatchRuleMgrService.java (得分: 26.30)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'qryAlgorithmList' () 过长 (60 行)，建议拆分

### 88. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/DispatchRuleMgrServiceImpl.java (得分: 25.78)

### 89. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/config/exception/BizException.java (得分: 25.00)

### 90. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/PlanSourceStaticVO.java (得分: 25.00)

### 91. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/openApi/ScheduPlanInfoOpenApiDTO.java (得分: 25.00)

### 92. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduTargetFileQueryDTO.java (得分: 25.00)

### 93. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ChartBasicVO.java (得分: 25.00)

### 94. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/QualityTargetPerformanceQueryDTO.java (得分: 25.00)

### 95. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/openApi/ScheduTaskInfoOpenApiDTO.java (得分: 25.00)

### 96. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IDispatchEfficiencyMgrService.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'qryTaskStatic' () 较长 (34 行)，可考虑重构

### 97. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/SysAttachInfoMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectSysAttachInfoById' () 较长 (42 行)，可考虑重构

### 98. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IHomePageService.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'qryTodayTaskStatic' () 较长 (33 行)，可考虑重构

### 99. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ResDeviceMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectResDeviceById' () 较长 (50 行)，可考虑重构

### 100. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduDevcControlConfigMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduDevcControlConfigById' () 较长 (43 行)，可考虑重构

### 101. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/SysDictMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectSysDictById' () 较长 (42 行)，可考虑重构

### 102. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduDevcControlRecordMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduDevcControlRecordById' () 较长 (43 行)，可考虑重构

### 103. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/PubOperationLogMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectPubOperationLogById' () 较长 (42 行)，可考虑重构

### 104. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduPlanRuleMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduPlanRuleById' () 较长 (50 行)，可考虑重构

### 105. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduTaskAvgDurationMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduTaskAvgDurationById' () 较长 (42 行)，可考虑重构

### 106. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IPubOperationLogService.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectPubOperationLogById' () 较长 (42 行)，可考虑重构

### 107. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/SysAttachInfoRelMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectSysAttachInfoRelById' () 较长 (42 行)，可考虑重构

### 108. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduPlanHisMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduPlanHisById' () 较长 (50 行)，可考虑重构

### 109. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduLabTaskRecordMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduLabTaskRecordById' () 较长 (42 行)，可考虑重构

### 110. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IDispatchTaskMgrService.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'callDispatchPlan' () 较长 (42 行)，可考虑重构

### 111. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ResSiteMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectResSiteInfoList' () 较长 (34 行)，可考虑重构

### 112. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduTaskExtendHisMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduTaskExtendHisById' () 较长 (42 行)，可考虑重构

### 113. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ResAreaInfoMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectProvinceCityRegionList' () 较长 (50 行)，可考虑重构

### 114. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/ScheduAlgorithmInfoMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectScheduAlgorithmInfoById' () 较长 (42 行)，可考虑重构

### 115. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/PubLogCallbackAccessMapper.java (得分: 24.48)
**问题分类**: ⚠️ 其他问题:1

**主要问题**:
- 函数 'selectPubLogCallbackAccessByLogId' () 较长 (42 行)，可考虑重构

### 116. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/OpenApiServiceImpl.java (得分: 23.96)
**问题分类**: ⚠️ 其他问题:2

**主要问题**:
- 函数 'LoggerFactory.getLogger' () 过长 (54 行)，建议拆分
- 函数 'addTaskInfo' () 过长 (85 行)，建议拆分

### 117. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduTargetFileVO.java (得分: 23.04)

### 118. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduMonitorQualityTargetVO.java (得分: 23.04)

### 119. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/SysUserCusController.java (得分: 23.04)

### 120. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/ScheduTimerServiceImpl.java (得分: 23.04)

### 121. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/MaintenancePerformanceStaticVO.java (得分: 23.04)

### 122. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduPlanInfoVO.java (得分: 23.04)

### 123. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/openApi/BaseOpenApiDTO.java (得分: 23.04)

### 124. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduTaskInfoQueryDTO.java (得分: 23.04)

### 125. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/BizQuantityTargetVO.java (得分: 23.04)

### 126. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/QuantityTargetCollectPerformanceVO.java (得分: 23.04)

### 127. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduMonitorQualityTargetQueryDTO.java (得分: 23.04)

### 128. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduPlanRuleVO.java (得分: 23.04)

### 129. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/QuantityTargetPerformanceVO.java (得分: 23.04)

### 130. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduPlanInfoQueryDTO.java (得分: 23.04)

### 131. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduAlgruleConfigQueryDTO.java (得分: 23.04)

### 132. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduAlgruleConfigVO.java (得分: 23.04)

### 133. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduPlanRuleQueryDTO.java (得分: 23.04)

### 134. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/MaintenanceUnitVO.java (得分: 23.04)

### 135. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduAlgorithmInvokeRecordVO.java (得分: 21.74)

### 136. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ResSiteQueryDTO.java (得分: 21.74)

### 137. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/enums/ResultEnum.java (得分: 21.74)

### 138. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/MonitorQualityResultQueryDTO.java (得分: 21.74)

### 139. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduTaskInfoVO.java (得分: 21.74)

### 140. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ApprovalDTO.java (得分: 21.74)

### 141. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/constant/CommonConstant.java (得分: 21.74)

### 142. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/QuantityTargetCompleteQueryDTO.java (得分: 21.74)

### 143. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/BigScreenController.java (得分: 21.74)

### 144. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduPlanAssoruleVO.java (得分: 21.74)

### 145. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/constant/SysDictConstant.java (得分: 21.74)

### 146. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/openApi/UpdatePlanExtendApiDTO.java (得分: 21.74)

### 147. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/ScheduMonitorQuantityTargetVO.java (得分: 21.74)

### 148. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/DispatchTaskMgrController.java (得分: 21.74)

### 149. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduApprovalRecordQueryDTO.java (得分: 21.74)

### 150. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/constant/TaskConstant.java (得分: 21.74)

### 151. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/PubLogCallbackAccessController.java (得分: 21.74)

### 152. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/PubOperationLogServiceImpl.java (得分: 21.74)

### 153. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduTaskExtendQueryDTO.java (得分: 21.74)

### 154. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/DispatchTaskMgrServiceImpl.java (得分: 21.74)

### 155. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/GenericMgrController.java (得分: 21.74)

### 156. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/SiteTaskStaticVO.java (得分: 21.74)

### 157. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/mapper/SysUserMapperCus.java (得分: 21.74)

### 158. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/enums/StatusCode.java (得分: 21.74)

### 159. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/openApi/AddMonitorQualityTargetApiDTO.java (得分: 21.74)

### 160. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/DispatchEfficiencyStaticVO.java (得分: 21.74)

### 161. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/enums/ReportExportEnum.java (得分: 21.74)

### 162. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/config/PubOperationLogController.java (得分: 21.74)

### 163. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/PlanTypeStaticVO.java (得分: 21.74)

### 164. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/PlanMgrController.java (得分: 21.74)

### 165. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/CompletePlanStaticVO.java (得分: 21.74)

### 166. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IDevcControlMgrService.java (得分: 21.74)

### 167. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduMonitorQuantityTargetQueryDTO.java (得分: 21.74)

### 168. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/DispatchEfficiencyMgrServiceImpl.java (得分: 21.74)

### 169. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/openApi/AddTaskInfoOpenApiDTO.java (得分: 21.74)

### 170. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/ScheduTimerController.java (得分: 21.74)

### 171. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/DispatchEfficiencyMgrController.java (得分: 21.74)

### 172. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IScheduTimerService.java (得分: 21.74)

### 173. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/constant/PlanConstant.java (得分: 21.74)

### 174. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/MaintenancePersonStaticVO.java (得分: 21.74)

### 175. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IPushTaskService.java (得分: 21.74)

### 176. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/IPubLogCallbackAccessService.java (得分: 21.74)

### 177. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/MaintainPersonCountVO.java (得分: 21.74)

### 178. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/ISysUserServiceCus.java (得分: 21.74)

### 179. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/DevcControlMgrServiceImpl.java (得分: 21.74)

### 180. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/PubLogCallbackAccessServiceImpl.java (得分: 21.74)

### 181. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduPlanAssoruleQueryDTO.java (得分: 21.74)

### 182. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/TaskStaticVO.java (得分: 21.74)

### 183. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/dto/ScheduMonitorActivityInfoQueryDTO.java (得分: 21.74)

### 184. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/TaskCountStaticVO.java (得分: 21.74)

### 185. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/service/impl/SysUserServiceCusImpl.java (得分: 21.74)

### 186. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/constant/EnumConstant.java (得分: 21.74)

### 187. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/DevcControlMgrController.java (得分: 21.74)

### 188. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/HomePageController.java (得分: 21.74)

### 189. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/OpenApiController.java (得分: 21.74)

### 190. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/DispatchRuleMgrController.java (得分: 21.74)

### 191. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/controller/TargetMgrController.java (得分: 21.74)

### 192. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/TaskDynamicDO.java (得分: 21.74)

### 193. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/domain/vo/TaskTrendVO.java (得分: 21.74)

### 194. /Users/<USER>/work/code/environment/bjhb/backendNew/mes_ruoyi_cloud/mes-modules/mes-smartdispatch/src/main/java/com/mes/smartdispath/constant/PubRegionConstant.java (得分: 21.74)

## 改进建议

### 高优先级
- 继续保持当前的代码质量标准

### 中优先级
- 可以考虑进一步优化性能和可读性
- 完善文档和注释，便于团队协作

