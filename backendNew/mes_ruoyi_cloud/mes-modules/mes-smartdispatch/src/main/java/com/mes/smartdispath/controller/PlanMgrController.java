package com.mes.smartdispath.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.smartdispath.domain.dto.ApprovalDTO;
import com.mes.smartdispath.domain.dto.ResDeviceQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanRuleDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanRuleQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.domain.vo.ScheduPlanRuleVO;
import com.mes.smartdispath.service.IPlanMgrService;

import io.jsonwebtoken.lang.Assert;

/**
 * 计划管理 Controller
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/planmgr")
public class PlanMgrController extends BaseController {
    @Autowired
    private IPlanMgrService planMgrService;

    /**
     * 3.1.1、计划生成规则查询
     */
    @GetMapping("/qryPlanRule")
    public TableDataInfo qryPlanRule(ScheduPlanRuleQueryDTO queryDto) {
        startPage();
        List<ScheduPlanRuleVO> list = planMgrService.qryPlanRule(queryDto);
        return getDataTable(list);
    }

    /**
     * 3.1.2、计划生成规则新增/编辑
     */
    @PostMapping("/savePlanRule")
    public AjaxResult savePlanRule(@RequestBody ScheduPlanRuleDTO dto) {
        return toAjax(planMgrService.savePlanRule(dto));
    }

    /**
     * 3.1.3、计划生成规则删除
     */
    @DeleteMapping("/delPlanRule/{id}")
    public AjaxResult delPlanRule(@PathVariable String id) {
        Assert.notNull(id, "删除数据的规则编码参数不能为空,请核对请求参数!");
        return toAjax(planMgrService.delPlanRule(id));
    }

    /**
     * 3.1.4、计划生成规则审批【支持批量】
     */
    @PostMapping("/verifyPlanRule")
    public AjaxResult verifyPlanRule(@RequestBody ApprovalDTO approvalDTO) {
        Assert.notNull(approvalDTO.getId(), "规则id参数不能为空,请核对请求参数!");
        Assert.notNull(approvalDTO.getApprovalStatus(), "审批状态参数不能为空,请核对请求参数!");
        return toAjax(planMgrService.verifyPlanRule(approvalDTO));
    }

    /**
     * 3.1.5、关联伴生计划规则查询
     */
    @GetMapping("/qryAssoPlanRule")
    public AjaxResult qryAssoPlanRule(String mainRuleId) {
        Assert.notNull(mainRuleId, "查询伴生规则的主规则id参数不能为空,请核对请求参数!");
        return success(planMgrService.qryAssoPlanRule(mainRuleId));
    }

    /**
     * 3.2.1、计划信息查询
     */
    @GetMapping("/qryPlanList")
    public TableDataInfo qryPlanList(ScheduPlanInfoQueryDTO queryDto) {
        startPage();
        List<ScheduPlanInfoVO> list = planMgrService.qryPlanList(queryDto);
        return getDataTable(list);
    }

    /**
     * 3.2.2、计划信息新增/编辑
     */
    @PostMapping("/savePlanInfo")
    public AjaxResult savePlanInfo(@RequestBody ScheduPlanInfoDTO dto) {
        return toAjax(planMgrService.savePlanInfo(dto));
    }

    /**
     * 3.2.3、计划信息删除
     */
    @DeleteMapping("/delPlanInfo/{id}")
    public AjaxResult delPlanInfo(@PathVariable String id) {
        Assert.notNull(id, "删除数据的计划id参数不能为空,请核对请求参数!");
        return toAjax(planMgrService.delPlanInfo(id));
    }

    /**
     * 3.2.4、计划信息审批列表查询
     */
    @GetMapping("/qryPlanApprovalList")
    public TableDataInfo qryPlanApprovalList(ScheduPlanInfoQueryDTO queryDto) {
        startPage();
        List<ScheduPlanInfoVO> list = planMgrService.qryPlanApprovalList(queryDto);
        return getDataTable(list);
    }

    /**
     * 3.2.5、计划信息审核【支持批量】
     */
    @PostMapping("/verifyPlanInfo")
    public AjaxResult verifyPlanInfo(@RequestBody ApprovalDTO approvalDTO) {
        Assert.notNull(approvalDTO.getId(), "计划ID参数不能为空,请核对请求参数!");
        Assert.notNull(approvalDTO.getApprovalStatus(), "审批状态参数不能为空,请核对请求参数!");
        return toAjax(planMgrService.verifyPlanInfo(approvalDTO));
    }

    /**
     * 3.2.6、计划新增时查询站点关联的设备信息
     */
    @GetMapping("/qrySiteLinkDevice")
    public AjaxResult qrySiteLinkDevice(ResDeviceQueryDTO queryDTO) {
        Assert.notNull(queryDTO.getSiteId(), "查询站点关联设备数据的站点id参数不能为空,请核对请求参数!");
        return success(planMgrService.qrySiteLinkDevice(queryDTO));
    }

    /**
     * 3.2.7、计划信息详情查询
     */
    @GetMapping("/qryPlanDetail")
    public AjaxResult qryPlanDetail(String id) {
        Assert.notNull(id, "计划id参数不能为空,请核对请求参数!");
        return success(planMgrService.qryPlanDetail(id));
    }
}
