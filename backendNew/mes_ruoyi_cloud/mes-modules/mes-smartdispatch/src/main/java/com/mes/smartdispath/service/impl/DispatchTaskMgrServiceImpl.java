package com.mes.smartdispath.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mes.common.core.utils.StringUtils;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.smartdispath.constant.CommonConstant;
import com.mes.smartdispath.constant.PlanConstant;
import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.constant.TaskConstant;
import com.mes.smartdispath.domain.ResPersonCertificate;
import com.mes.smartdispath.domain.ScheduApprovalRecord;
import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;
import com.mes.smartdispath.domain.ScheduPlanInfo;
import com.mes.smartdispath.domain.ScheduTaskExtend;
import com.mes.smartdispath.domain.ScheduTaskExtendHis;
import com.mes.smartdispath.domain.ScheduTaskHis;
import com.mes.smartdispath.domain.ScheduTaskInfo;
import com.mes.smartdispath.domain.ScheduTaskPlanRel;
import com.mes.smartdispath.domain.dto.ResPersonBasicQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorActivityInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskExtendQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.MaintenanceWorkCalendarVO;
import com.mes.smartdispath.domain.vo.ResPersonBasicVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.domain.vo.ScheduTaskExtendWithTaskDTO;
import com.mes.smartdispath.domain.vo.ScheduTaskInfoVO;
import com.mes.smartdispath.domain.vo.SiteTaskListVO;
import com.mes.smartdispath.domain.vo.SiteTaskStaticVO;
import com.mes.smartdispath.domain.vo.TaskStaticVO;
import com.mes.smartdispath.enums.BusinessTypeShortPrefixEnum;
import com.mes.smartdispath.mapper.ResPersonBasicMapper;
import com.mes.smartdispath.mapper.ResPersonCertificateMapper;
import com.mes.smartdispath.mapper.ResSiteMapper;
import com.mes.smartdispath.mapper.ScheduApprovalRecordMapper;
import com.mes.smartdispath.mapper.ScheduMonitorActivityInfoMapper;
import com.mes.smartdispath.mapper.ScheduPlanInfoMapper;
import com.mes.smartdispath.mapper.ScheduTaskExtendHisMapper;
import com.mes.smartdispath.mapper.ScheduTaskExtendMapper;
import com.mes.smartdispath.mapper.ScheduTaskHisMapper;
import com.mes.smartdispath.mapper.ScheduTaskInfoMapper;
import com.mes.smartdispath.mapper.ScheduTaskPlanRelMapper;
import com.mes.smartdispath.service.IDispatchTaskMgrService;
import com.mes.smartdispath.service.IPushTaskService;
import com.mes.smartdispath.utils.DateUtils;
import com.mes.system.api.model.LoginUser;

import cn.hutool.core.lang.Assert;

/**
 * 调度任务管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class DispatchTaskMgrServiceImpl implements IDispatchTaskMgrService {
    private static final Logger log = LoggerFactory.getLogger(DispatchTaskMgrServiceImpl.class);

    @Autowired
    private ScheduTaskPlanRelMapper scheduTaskPlanRelMapper;

    @Autowired
    private ScheduTaskInfoMapper scheduTaskInfoMapper;

    @Autowired
    private ScheduTaskExtendMapper scheduTaskExtendMapper;

    @Autowired
    private ScheduPlanInfoMapper scheduPlanInfoMapper;

    @Autowired
    private IPushTaskService pushTaskService;

    @Autowired
    private ScheduMonitorActivityInfoMapper scheduMonitorActivityInfoMapper;

    @Autowired
    private ScheduTaskHisMapper scheduTaskHisMapper;

    @Autowired
    private ScheduApprovalRecordMapper scheduApprovalRecordMapper;

    @Autowired
    private ResSiteMapper resSiteMapper;

    @Autowired
    private ResPersonBasicMapper resPersonBasicMapper;

    @Autowired
    private ScheduTaskExtendHisMapper scheduTaskExtendHisMapper;

    @Autowired
    private ResPersonCertificateMapper resPersonCertificateMapper;

    /**
     * 查询计划列表
     *
     * @param queryDto
     * @return
     */
    public List<ScheduPlanInfoVO> qryDispatchtaskPlanList(ScheduPlanInfoQueryDTO queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getCityCode())) {
            queryDto.setCityCodeArr(Arrays.asList(StringUtils.split(queryDto.getCityCode(), ",")));
        }
        // 如果不传参，那么查询待调度和已调度的计划
        queryDto.setScheduStatusArr(new ArrayList<String>() {
            {
                add(PlanConstant.SCHEDU_STATUS_WAIT);
                add(PlanConstant.SCHEDU_STATUS_DONE);
            }
        });
        if (StringUtils.isNotEmpty(queryDto.getScheduStatus())) {
            queryDto.setScheduStatusArr(Arrays.asList(StringUtils.split(queryDto.getScheduStatus(), ",")));
        }
        if (StringUtils.isNotEmpty(queryDto.getSiteId())) {
            queryDto.setSiteIdArr(Arrays.asList(StringUtils.split(queryDto.getSiteId(), ",")));
        }
        queryDto.setApprovalStatus(CommonConstant.APPROVAL_STATUS_APPROVED);
        return scheduPlanInfoMapper.selectScheduPlanInfoList(queryDto);
    }

    /**
     * 手工分配任务
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int manualDispatchPlan(ScheduTaskInfoDTO dto) {
        if (null == dto.getPlanInfos() || dto.getPlanInfos().isEmpty()) {
            Assert.isTrue(false, "手动分配任务必须选择计划，请重新操作");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String operationUser = loginUser.getUsername();
        Date curDateTime = DateUtils.getCurDateTime();
        // 找出是否存在重要活动的任务，重要任务的活动需要审批
        List<String> businessTypeArr = new ArrayList<>();
        List<String> activityTypeArr = new ArrayList<>();
        List<String> activitySubtypeArr = new ArrayList<>();
        List<String> uniqueKeyArr = new ArrayList<>();
        for (ScheduPlanInfo planInfo : dto.getPlanInfos()) {
            businessTypeArr.add(planInfo.getBusinessType());
            activityTypeArr.add(planInfo.getActivityType());
            activitySubtypeArr.add(planInfo.getActivitySubtype());
            uniqueKeyArr.add(
                planInfo.getBusinessType() + "_" + planInfo.getActivityType() + "_" + planInfo.getActivitySubtype());
        }
        ScheduMonitorActivityInfoQueryDTO activityInfoQueryDTO = new ScheduMonitorActivityInfoQueryDTO();
        activityInfoQueryDTO.setBusinessTypeArr(businessTypeArr);
        activityInfoQueryDTO.setActivityTypeArr(activityTypeArr);
        activityInfoQueryDTO.setActivitySubtypeArr(activitySubtypeArr);
        List<ScheduMonitorActivityInfo> monitorActivityInfos = scheduMonitorActivityInfoMapper
            .selectScheduMonitorActivityInfoList(activityInfoQueryDTO);
        boolean isImportant = false;
        for (ScheduMonitorActivityInfo monitorActivityInfo : monitorActivityInfos) {
            if (uniqueKeyArr.contains(monitorActivityInfo.getBusinessType() + "_"
                + monitorActivityInfo.getActivityTypeCode() + "_" + monitorActivityInfo.getActivitySubtypeCode())) {
                isImportant = true;
                break;
            }
        }
        ScheduTaskInfo taskInfo = new ScheduTaskInfo();
        BeanUtils.copyProperties(dto, taskInfo);
        String approvalStatus = isImportant ? CommonConstant.APPROVAL_STATUS_PENDING
            : CommonConstant.APPROVAL_STATUS_APPROVED;
        taskInfo.setApprovalStatus(approvalStatus);
        String businessType = dto.getPlanInfos().get(0).getBusinessType();
        taskInfo.setBusinessType(businessType);
        String businessTypeShortPrefix = BusinessTypeShortPrefixEnum.getShortPrefixByType(businessType);
        String curDate = DateUtils.getCurDateWithoutHyphen();
        String taskCode = CommonConstant.TASK_INFO_CODE_PREFIX + businessTypeShortPrefix + curDate + "-"
            + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8);
        taskInfo.setTaskCode(taskCode);
        taskInfo.setCreateBy(operationUser);
        taskInfo.setCreateTime(curDateTime);
        taskInfo.setCreateBy(operationUser);
        taskInfo.setUpdateTime(curDateTime);
        taskInfo.setIsDeleted(CommonConstant.IS_DELETE_N);
        int taskCount = scheduTaskInfoMapper.insertScheduTaskInfo(taskInfo);
        // 保存任务历史
        ScheduTaskHis taskHis = new ScheduTaskHis();
        BeanUtils.copyProperties(taskInfo, taskHis);
        taskHis.setTaskId(taskInfo.getId());
        int taskHisCount = scheduTaskHisMapper.insertScheduTaskHis(taskHis);
        log.info("保存任务历史信息条数:{}", taskHisCount);
        // 如果需要审核，那么存一条信息到审核表
        if (CommonConstant.APPROVAL_STATUS_PENDING.equals(approvalStatus)) {
            ScheduApprovalRecord scheduApprovalRecord = new ScheduApprovalRecord();
            scheduApprovalRecord.setBusinessId(taskInfo.getId());
            scheduApprovalRecord.setModuleType(CommonConstant.APPROVAL_SOURCE_TASK_INFO);
            scheduApprovalRecord.setApprovalStatus(approvalStatus);
            scheduApprovalRecord.setUpdateBy(operationUser);
            scheduApprovalRecord.setUpdateTime(curDateTime);
            scheduApprovalRecord.setCreateBy(operationUser);
            scheduApprovalRecord.setCreateTime(curDateTime);
            scheduApprovalRecord.setStatus(CommonConstant.DATA_STATUS_A);
            int approvalRecordCount = scheduApprovalRecordMapper.insertScheduApprovalRecord(scheduApprovalRecord);
            log.info("保存任务审批信息条数:{}", approvalRecordCount);
        }
        // 处理扩展信息
        if (null != dto.getExtendInfos() && !dto.getExtendInfos().isEmpty()) {
            for (ScheduTaskExtend extendInfo : dto.getExtendInfos()) {
                BeanUtils.copyProperties(dto, extendInfo);
                BeanUtils.copyProperties(taskInfo, extendInfo);
                extendInfo.setTaskId(taskInfo.getId());
            }
            // 批量保存
            int extendInfoCount = scheduTaskExtendMapper.batchInsertScheduTaskExtend(dto.getExtendInfos());
            log.info("保存任务扩展信息条数:{}", extendInfoCount);
        }
        // 存入任务和计划的关系
        List<ScheduTaskPlanRel> scheduTaskPlanRelList = new ArrayList<>();
        // 更新任务的调度状态
        List<ScheduPlanInfo> scheduPlanInfoList = new ArrayList<>();
        for (ScheduPlanInfo planInfo : dto.getPlanInfos()) {
            ScheduTaskPlanRel scheduTaskPlanRel = new ScheduTaskPlanRel();
            BeanUtils.copyProperties(planInfo, scheduTaskPlanRel);
            BeanUtils.copyProperties(taskInfo, scheduTaskPlanRel);
            scheduTaskPlanRel.setPlanId(planInfo.getId());
            scheduTaskPlanRel.setTaskId(taskInfo.getId());
            scheduTaskPlanRelList.add(scheduTaskPlanRel);
            ScheduPlanInfo updatePlanInfo = new ScheduPlanInfo();
            updatePlanInfo.setId(planInfo.getId());
            updatePlanInfo.setScheduStatus(PlanConstant.SCHEDU_STATUS_DONE);
            updatePlanInfo.setUpdateBy(operationUser);
            updatePlanInfo.setUpdateTime(curDateTime);
            scheduPlanInfoList.add(updatePlanInfo);
        }
        int relCount = scheduTaskPlanRelMapper.batchInsertScheduTaskPlanRel(scheduTaskPlanRelList);
        log.info("保存任务计划关系条数:{}", relCount);
        int updatePlanCount = scheduPlanInfoMapper.batchUpdateScheduPlanInfo(scheduPlanInfoList);
        log.info("更新计划调度状态条数:{}", updatePlanCount);
        return taskCount;
    }

    /**
     * 立刻调用算法分配任务-调用清华算法的接口
     *
     * @param id
     * @return
     */
    @Override
    public int callDispatchPlan(String id) {
        return 1;
    }

    /**
     * 查询手工分配任务的资源【人员】列表
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<ResPersonBasicVO> qryManualDispatchResList(ResPersonBasicQueryDTO queryDto) {
        // List<String> siteIdArr = Arrays.asList(StringUtils.split(queryDto.getSiteId(), ","));
        // ResSiteQueryDTO resSiteQueryDTO = new ResSiteQueryDTO();
        // resSiteQueryDTO.setSiteIdArr(siteIdArr);
        // List<ResSiteVO> resSiteInfos = resSiteMapper.selectResSiteInfoList(resSiteQueryDTO);
        // 站点的运维单位
        if (StringUtils.isNotEmpty(queryDto.getDeptId())) {
            queryDto.setDeptIdArr(Arrays.asList(StringUtils.split(queryDto.getDeptId(), ",")));
        }
        // List<String> deptIdArr =
        // resSiteInfos.stream().map(ResSiteVO::getOperationUnit).distinct().collect(Collectors.toList());
        // queryDto.setDeptIdArr(deptIdArr);
        queryDto.setPersonType(SysDictConstant.MAINTENANCE_PERSON_TYPE);
        queryDto.setStatusArr(new ArrayList<String>() {
            {
                add(SysDictConstant.MAINTENANCE_PERSON_STATUS_ON_DUTY);
                add(SysDictConstant.MAINTENANCE_PERSON_STATUS_LEAVE);
            }
        });
        List<ResPersonBasicVO> result = resPersonBasicMapper.selectResPersonBasicList(queryDto);
        if (!result.isEmpty()) {
            List<String> idCardArr = result.stream().map(ResPersonBasicVO::getIdCard).collect(Collectors.toList());
            List<ResPersonCertificate> personCertificates = resPersonCertificateMapper
                .selectPersonCertificateListByIdCards(idCardArr);
            // 分组
            Map<String, ResPersonCertificate> personCertificateMap = personCertificates.stream()
                .collect(Collectors.toMap(ResPersonCertificate::getIdCard, Function.identity(), (v1, v2) -> v1));
            for (ResPersonBasicVO resPersonBasicVO : result) {
                if (null != personCertificateMap.get(resPersonBasicVO.getIdCard())) {
                    resPersonBasicVO.setCertificateName(
                        personCertificateMap.get(resPersonBasicVO.getIdCard()).getCertificateName());
                }
            }
        }
        return result;
    }

    /**
     * 任务列表
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<ScheduTaskInfoVO> qryTaskList(ScheduTaskInfoQueryDTO queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getTaskStatus())) {
            queryDto.setTaskStatusArr(Arrays.asList(StringUtils.split(queryDto.getTaskStatus(), ",")));
        }
        if (StringUtils.isNotEmpty(queryDto.getApprovalStatus())) {
            queryDto.setApprovalStatusArr(Arrays.asList(StringUtils.split(queryDto.getApprovalStatus(), ",")));
        }
        List<ScheduTaskInfoVO> list = scheduTaskInfoMapper.selectScheduTaskInfoList(queryDto);
        // 补充关联的计划信息
        if (!list.isEmpty()) {
            List<String> taskIdArr = list.stream().map(ScheduTaskInfoVO::getId).collect(Collectors.toList());
            ScheduPlanInfoQueryDTO planInfoQueryDTO = new ScheduPlanInfoQueryDTO();
            planInfoQueryDTO.setTaskIdArr(taskIdArr);
            List<ScheduPlanInfoVO> planInfos = scheduPlanInfoMapper.selectTaskLinkPlanInfoList(planInfoQueryDTO);
            // 按照taskId分组
            Map<String, List<ScheduPlanInfoVO>> planInfoMap = planInfos.stream()
                .collect(Collectors.groupingBy(ScheduPlanInfoVO::getTaskId));
            for (ScheduTaskInfoVO task : list) {
                task.setPlanInfos(planInfoMap.get(task.getId()));
            }
        }
        return list;
    }

    /**
     * 查询任务详情
     *
     * @param id
     * @return
     */
    @Override
    public ScheduTaskInfoVO qryTaskDetail(String id) {
        ScheduTaskInfoVO taskInfo = scheduTaskInfoMapper.selectScheduTaskInfoById(id);
        // 补充关联的计划信息和任务扩展信息
        if (taskInfo != null) {
            ScheduPlanInfoQueryDTO planInfoQueryDTO = new ScheduPlanInfoQueryDTO();
            planInfoQueryDTO.setTaskIdArr(Arrays.asList(id));
            List<ScheduPlanInfoVO> planInfos = scheduPlanInfoMapper.selectTaskLinkPlanInfoList(planInfoQueryDTO);
            taskInfo.setPlanInfos(planInfos);
            ScheduTaskExtendQueryDTO extendQueryDTO = new ScheduTaskExtendQueryDTO();
            extendQueryDTO.setTaskId(id);
            List<ScheduTaskExtend> extendInfos = scheduTaskExtendMapper.selectScheduTaskExtendList(extendQueryDTO);
            taskInfo.setExtendInfos(extendInfos);
        }
        return taskInfo;
    }

    /**
     * 任务调整
     *
     * @param dto
     * @return
     */
    @Override
    public int updateTaskInfo(ScheduTaskInfoDTO dto) {
        log.info("修改调度任务信息记录，id: {}", dto.getId());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String operationUser = loginUser.getUsername();
        Date curDateTime = DateUtils.getCurDateTime();
        // 如果任务数据有更新
        if (CommonConstant.UPDATE_DATA_FLAG.equals(dto.getTaskUpdateFlag())) {
            // 查询任务信息
            ScheduTaskInfoVO taskInfo = scheduTaskInfoMapper.selectScheduTaskInfoById(dto.getId());
            // 存一份到任务历史表
            ScheduTaskHis taskHisInfo = new ScheduTaskHis();
            BeanUtils.copyProperties(taskInfo, taskHisInfo);
            taskHisInfo.setTaskId(taskInfo.getId());
            taskHisInfo.setCreateBy(operationUser);
            taskHisInfo.setCreateTime(curDateTime);
            taskHisInfo.setUpdateBy(operationUser);
            taskHisInfo.setUpdateTime(curDateTime);
            int hisCount = scheduTaskHisMapper.insertScheduTaskHis(taskHisInfo);
            log.info("插入任务历史记录条数: {}", hisCount);
        }
        // 更新任务信息（更新任务状态为待推送）
        ScheduTaskInfo updateTaskInfo = new ScheduTaskInfo();
        BeanUtils.copyProperties(dto, updateTaskInfo);
        updateTaskInfo.setTaskStatus(TaskConstant.TASK_STATUS_WAIT_PUSH);
        updateTaskInfo.setUpdateBy(operationUser);
        updateTaskInfo.setUpdateTime(curDateTime);
        int updateCount = scheduTaskInfoMapper.updateScheduTaskInfo(updateTaskInfo);
        log.info("更新调度任务信息记录条数: {}", updateCount);
        // 更新关联的计划的执行状态为空
        ScheduTaskPlanRel taskPlanRelQueryParam = new ScheduTaskPlanRel();
        taskPlanRelQueryParam.setTaskId(dto.getId());
        taskPlanRelQueryParam.setStatus(CommonConstant.DATA_STATUS_A);
        List<ScheduTaskPlanRel> taskPlanRelList = scheduTaskPlanRelMapper
            .selectScheduTaskPlanRelList(taskPlanRelQueryParam);
        if (!taskPlanRelList.isEmpty()) {
            List<ScheduPlanInfo> scheduPlanInfoList = new ArrayList<>();
            for (ScheduTaskPlanRel scheduTaskPlanRel : taskPlanRelList) {
                ScheduPlanInfo updatePlanInfo = new ScheduPlanInfo();
                updatePlanInfo.setId(scheduTaskPlanRel.getPlanId());
                updatePlanInfo.setExecuteStatus(null);
                updatePlanInfo.setUpdateBy(operationUser);
                updatePlanInfo.setUpdateTime(curDateTime);
                scheduPlanInfoList.add(updatePlanInfo);
            }
            int updatePlanCount = scheduPlanInfoMapper.batchUpdateScheduPlanInfoExecuteStatus(scheduPlanInfoList);
            log.info("更新调度计划信息记录条数: {}", updatePlanCount);
        }
        // 如果扩展信息有更新
        if (CommonConstant.UPDATE_DATA_FLAG.equals(dto.getExtendUpdateFlag())) {
            // 查询已有的扩展信息
            ScheduTaskExtendQueryDTO extendQueryDTO = new ScheduTaskExtendQueryDTO();
            extendQueryDTO.setTaskId(dto.getId());
            List<ScheduTaskExtend> oldExtends = scheduTaskExtendMapper.selectScheduTaskExtendList(extendQueryDTO);
            List<ScheduTaskExtendHis> extendHisList = new ArrayList<>();
            oldExtends.forEach(info -> {
                ScheduTaskExtendHis extendHis = new ScheduTaskExtendHis();
                BeanUtils.copyProperties(info, extendHis);
                extendHis.setUpdateBy(operationUser);
                extendHis.setUpdateTime(curDateTime);
                extendHis.setCreateBy(operationUser);
                extendHis.setCreateTime(curDateTime);
                extendHisList.add(extendHis);
            });
            int extendHisCount = scheduTaskExtendHisMapper.batchInsertScheduTaskExtendHis(extendHisList);
            log.info("插入任务扩展历史记录条数: {}", extendHisCount);
            // 删除现在的扩展信息
            int deleteExtendCount = scheduTaskExtendMapper.deleteScheduTaskExtendByTaskId(dto.getId());
            log.info("删除任务扩展信息记录条数: {}", deleteExtendCount);
            List<ScheduTaskExtend> newExtends = new ArrayList<>();
            dto.getExtendInfos().forEach(info -> {
                ScheduTaskExtend extend = new ScheduTaskExtend();
                BeanUtils.copyProperties(info, extend);
                extend.setTaskId(dto.getId());
                extend.setTaskName(dto.getTaskName());
                extend.setTaskCode(dto.getTaskCode());
                extend.setUpdateBy(operationUser);
                extend.setUpdateTime(curDateTime);
                extend.setCreateBy(operationUser);
                extend.setCreateTime(curDateTime);
                newExtends.add(extend);
            });
            int insertExtendCount = scheduTaskExtendMapper.batchInsertScheduTaskExtend(newExtends);
            log.info("插入任务扩展信息记录条数: {}", insertExtendCount);
        }
        return updateCount;
    }

    /**
     * 任务推送活动系统
     *
     * @param id
     * @return
     */
    @Override
    public int pushActivity(String id) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<String> taskIdArr = Arrays.asList(StringUtils.split(id, ","));
        // 任务信息查询参数
        ScheduTaskInfoQueryDTO queryDTO = new ScheduTaskInfoQueryDTO();
        queryDTO.setIdArr(taskIdArr);
        int count = pushTaskService.pushTaskToActivity(queryDTO, loginUser.getUsername());
        if (count == 0) {
            Assert.isTrue(false, "未查询到任务或任务推送至活动侧未返回结果");
        }
        return count;
    }

    /**
     * 站点任务统计信息
     *
     * @param siteId 站点ID
     * @return 站点任务统计信息
     */
    @Override
    public SiteTaskStaticVO qryTaskStaticBySite(String siteId) {
        return scheduTaskPlanRelMapper.selectTaskStaticBySiteId(siteId);
    }

    /**
     * 站点任务列表
     *
     * @param siteId 站点ID
     * @return 站点任务列表
     */
    @Override
    public List<SiteTaskListVO> qryTaskListBySite(String siteId) {
        TaskStaticQueryDTO queryDto = new TaskStaticQueryDTO();
        queryDto.setSiteId(siteId);
        queryDto.setTaskStausArr(new ArrayList<String>() {
            {
                add(TaskConstant.TASK_STATUS_WAIT_PUSH);
                add(TaskConstant.TASK_STATUS_PUSHED);
                add(TaskConstant.TASK_STATUS_IN_PROGRESS);
            }
        });
        return scheduTaskInfoMapper.selectCollectTaskList(queryDto);
    }

    /**
     * 站点某段时间内的任务统计信息
     *
     * @param queryDTO 参数
     * @return 站点任务统计信息
     */
    @Override
    public TaskStaticVO qryPeriodTaskStaticBySite(TaskStaticQueryDTO queryDTO) {
        return scheduTaskInfoMapper.selectPeriodTaskStatic(queryDTO);
    }

    /**
     * 站点运维甘特图数据
     *
     * @param queryDTO 参数
     * @return 站点运维甘特图数据
     */
    @Override
    public List<MaintenanceWorkCalendarVO> qryCompanyWorkGanttData(TaskStaticQueryDTO queryDTO) {
        List<ScheduTaskExtendWithTaskDTO> extendInfo = scheduTaskExtendMapper
            .selectScheduTaskExtendWithTaskList(queryDTO);
        // 按照人员分组
        Map<String, List<ScheduTaskExtendWithTaskDTO>> executorMap = extendInfo.stream()
            .collect(Collectors.groupingBy(ScheduTaskExtendWithTaskDTO::getExecutorId));
        // 循环输出结果
        List<MaintenanceWorkCalendarVO> result = new ArrayList<>();
        for (Map.Entry<String, List<ScheduTaskExtendWithTaskDTO>> entry : executorMap.entrySet()) {
            MaintenanceWorkCalendarVO vo = new MaintenanceWorkCalendarVO();
            vo.setExecutorId(entry.getKey());
            vo.setExecutorName(entry.getValue().get(0).getExecutorName());
            vo.setMaintainUnitCode(entry.getValue().get(0).getMaintainUnitCode());
            vo.setMaintainUnitName(entry.getValue().get(0).getMaintainUnitName());
            vo.setTaskList(entry.getValue());
            result.add(vo);
        }
        return result;
    }
}
