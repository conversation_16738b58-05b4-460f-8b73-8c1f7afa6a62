package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 算法调用执行记录对象 tb_schedu_algorithm_invoke_record
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduAlgorithmInvokeRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** $column.columnComment */
    private String algorithmId;

    /** 算法编码，与算法同步，确认各个算法的唯一标识 */
    @Excel(name = "算法编码", sort = 1)
    private String algorithmCode;

    /** 算法名称，新建算法时手动输入，用于区分不同算法 */
    @Excel(name = "算法名称", sort = 2)
    private String algorithmName;

    /** 调用时间，具体调用时间 */
    @Excel(name = "调用时间", sort = 4)
    private String invokeTime;

    /** 处理时长（单位：秒） */
    private String duration;

    /** 计划名称 */
    private String planName;

    /** 计划编号 */
    private String planCode;

    /** 任务名称 */
    private String taskName;

    /** 任务编号 */
    private String taskCode;

    /** 状态：正常 / 异常 */
    @Excel(name = "状态", sort = 8)
    private String status;

    /** 算法调用日期，按天聚合使用 */
    @Excel(name = "调用日期", sort = 3)
    private String invokeDate;

    /** 软删除标志（Y-已删除，N-未删除） */
    private String isDeleted;

    /** 租户ID */
    private String tenantId;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmCode(String algorithmCode) {
        this.algorithmCode = algorithmCode;
    }

    public String getAlgorithmCode() {
        return algorithmCode;
    }

    public void setAlgorithmName(String algorithmName) {
        this.algorithmName = algorithmName;
    }

    public String getAlgorithmName() {
        return algorithmName;
    }

    public void setInvokeTime(String invokeTime) {
        this.invokeTime = invokeTime;
    }

    public String getInvokeTime() {
        return invokeTime;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getDuration() {
        return duration;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setInvokeDate(String invokeDate) {
        this.invokeDate = invokeDate;
    }

    public String getInvokeDate() {
        return invokeDate;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("algorithmId", getAlgorithmId()).append("algorithmCode", getAlgorithmCode())
            .append("algorithmName", getAlgorithmName()).append("invokeTime", getInvokeTime())
            .append("duration", getDuration()).append("planName", getPlanName()).append("planCode", getPlanCode())
            .append("taskName", getTaskName()).append("taskCode", getTaskCode()).append("status", getStatus())
            .append("invokeDate", getInvokeDate()).append("createBy", getCreateBy())
            .append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("isDeleted", getIsDeleted()).append("tenantId", getTenantId())
            .toString();
    }
}
