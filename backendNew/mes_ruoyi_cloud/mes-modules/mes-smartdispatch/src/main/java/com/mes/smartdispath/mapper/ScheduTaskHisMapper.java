package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduTaskHis;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.TaskTrendVO;

/**
 * 调度任务信息历史Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduTaskHisMapper {
    /**
     * 任务完成情况趋势
     * 
     * @param queryDto
     * @return 总站任务统计信息
     */
    public List<TaskTrendVO> selectTaskTrend(TaskStaticQueryDTO queryDto);

    /**
     * 查询调度任务信息历史
     * 
     * @param id 调度任务信息历史主键
     * @return 调度任务信息历史
     */
    public ScheduTaskHis selectScheduTaskHisById(String id);

    /**
     * 查询调度任务信息历史列表
     * 
     * @param scheduTaskHis 调度任务信息历史
     * @return 调度任务信息历史集合
     */
    public List<ScheduTaskHis> selectScheduTaskHisList(ScheduTaskHis scheduTaskHis);

    /**
     * 新增调度任务信息历史
     * 
     * @param scheduTaskHis 调度任务信息历史
     * @return 结果
     */
    public int insertScheduTaskHis(ScheduTaskHis scheduTaskHis);

    /**
     * 批量新增调度任务信息
     *
     * @param scheduTaskHisList 调度任务信息List
     * @return 结果
     */
    public int batchInsertScheduTaskHis(List<ScheduTaskHis> scheduTaskHisList);

    /**
     * 修改调度任务信息历史
     * 
     * @param scheduTaskHis 调度任务信息历史
     * @return 结果
     */
    public int updateScheduTaskHis(ScheduTaskHis scheduTaskHis);

    /**
     * 批量修改调度任务信息
     *
     * @param scheduTaskHisList 调度任务信息List
     * @return 结果
     */
    public int batchUpdateScheduTaskHis(List<ScheduTaskHis> scheduTaskHisList);

    /**
     * 删除调度任务信息历史
     * 
     * @param id 调度任务信息历史主键
     * @return 结果
     */
    public int deleteScheduTaskHisById(String id);

    /**
     * 批量删除调度任务信息历史
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduTaskHisByIds(String[] ids);
}
