package com.mes.smartdispath.constant;

/**
 * @Author: li.haoyang
 * @Description：公共常量 @Date： 2025/7/3
 */
public class CommonConstant {
    /**
     * 全国区域节点code
     */
    public static final String NATIONWIDE_REGION_CODE = "000000";

    /**
     * 全国区域节点名称
     */
    public static final String NATIONWIDE_REGION_NAME = "全国";

    /**
     * 部分表的数据有效标志
     */
    public static final String DATA_STATUS_A = "A";

    /**
     * 部分表的删除标志
     */
    public static final String DATA_STATUS_X = "X";

    /**
     * 部分表的删除标志
     */
    public static final String IS_DELETE_1 = "1";

    /**
     * 部分表的数据有效标志
     */
    public static final String IS_DELETE_N = "N";

    /**
     * 部分表的删除标志
     */
    public static final String IS_DELETE_Y = "Y";

    /**
     * 附件表里的来源是数量目标文件（tb_schedu_target_file）
     */
    public static final String ATTACH_SOURCE_TB_SCHEDU_TARGET_FILE = "tb_schedu_target_file";

    /**
     * 审批表，计划规则来源
     */
    public static final String APPROVAL_SOURCE_PLAN_RULE = "plan_rule";

    /**
     * 审批表，计划信息来源
     */
    public static final String APPROVAL_SOURCE_PLAN_INFO = "plan_info";

    /**
     * 审批表，任务信息来源
     */
    public static final String APPROVAL_SOURCE_TASK_INFO = "task_info";

    /**
     * 待审核状态
     */
    public static final String APPROVAL_STATUS_PENDING = "pending";

    /**
     * 审核通过状态
     */
    public static final String APPROVAL_STATUS_APPROVED = "approved";

    /**
     * 算法调用规则code前缀
     */
    public static final String ALGORITHM_RULE_CODE_PREFIX = "AR";

    /**
     * 重要的活动
     */
    public static final String IMPORTANT_ACTIVITY = "1";

    /**
     * 默认的定时任务操作用户
     */
    public static final String DEFAULT_SCHEDU_TIMER_USER = "system";

    /**
     * 计划规则和计划信息提交标志，1-是提交（提交后无法编辑，进入审核流程）
     */
    public static final String SUBMIT_FLAG_1 = "1";

    /**
     * 活动信息有伴生活动的标志，1-有
     */
    public static final String IS_ASSO_ACTIVITY_FLAG_1 = "1";

    /**
     * 规则有伴生规则的标志，1-有
     */
    public static final String IS_ASSO_RULE_FLAG_1 = "1";

    /**
     * 是否是伴生计划，1-是
     */
    public static final String IS_ASSO_PLAN_FLAG_1 = "1";

    /**
     * 是否是伴生计划，0-否
     */
    public static final String IS_ASSO_PLAN_FLAG_0 = "0";

    /**
     * 业务类型短前缀，水
     */
    public static final String BUSINESS_TYPE_SHORT_PREFIX_WATER = "W";

    /**
     * 业务类型短前缀，气
     */
    public static final String BUSINESS_TYPE_SHORT_PREFIX_AIR = "A";

    /**
     * 计划规则code的前缀
     */
    public static final String PLAN_RULE_CODE_PREFIX = "R";

    /**
     * 计划信息code的前缀
     */
    public static final String PLAN_INFO_CODE_PREFIX = "P";

    /**
     * 任务信息code的前缀
     */
    public static final String TASK_INFO_CODE_PREFIX = "T";

    /**
     * 更新数据的标志，1-更新
     */
    public static final String UPDATE_DATA_FLAG = "1";
}
