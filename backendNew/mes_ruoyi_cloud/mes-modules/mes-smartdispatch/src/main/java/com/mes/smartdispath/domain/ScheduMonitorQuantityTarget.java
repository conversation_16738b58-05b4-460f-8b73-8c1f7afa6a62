package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 年度监测活动数量目标对象 tb_schedu_monitor_quantity_target
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduMonitorQuantityTarget extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 省行政区域代码 */
    @Excel(name = "省行政区域代码")
    private String provinceCode;

    /** 省名称（如：北京市） */
    @Excel(name = "省名称", readConverterExp = "如=：北京市")
    private String provinceName;

    /** 市行政区域代码 */
    @Excel(name = "市行政区域代码")
    private String cityCode;

    /** 市名称（如：上海市） */
    @Excel(name = "市名称", readConverterExp = "如=：上海市")
    private String cityName;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String siteName;

    /** 站点ID */
    @Excel(name = "站点ID")
    private String siteId;

    /** 业务分类 */
    @Excel(name = "业务分类")
    private String businessType;

    /** 站点类型 */
    @Excel(name = "站点类型")
    private String siteType;

    /** 活动大类 */
    @Excel(name = "活动大类")
    private String activityType;

    /** 活动子类 */
    @Excel(name = "活动子类")
    private String activitySubtype;

    /** 目标值 */
    @Excel(name = "目标值")
    private String targetValue;

    /** 统计年度 */
    @Excel(name = "统计年度")
    private String statYear;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType;
    }

    public String getSiteType() {
        return siteType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivitySubtype(String activitySubtype) {
        this.activitySubtype = activitySubtype;
    }

    public String getActivitySubtype() {
        return activitySubtype;
    }

    public void setTargetValue(String targetValue) {
        this.targetValue = targetValue;
    }

    public String getTargetValue() {
        return targetValue;
    }

    public void setStatYear(String statYear) {
        this.statYear = statYear;
    }

    public String getStatYear() {
        return statYear;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("provinceCode", getProvinceCode()).append("provinceName", getProvinceName())
            .append("cityCode", getCityCode()).append("cityName", getCityName()).append("siteName", getSiteName())
            .append("siteId", getSiteId()).append("businessType", getBusinessType()).append("siteType", getSiteType())
            .append("activityType", getActivityType()).append("activitySubtype", getActivitySubtype())
            .append("targetValue", getTargetValue()).append("statYear", getStatYear()).append("createBy", getCreateBy())
            .append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("status", getStatus()).append("tenantId", getTenantId())
            .toString();
    }
}
