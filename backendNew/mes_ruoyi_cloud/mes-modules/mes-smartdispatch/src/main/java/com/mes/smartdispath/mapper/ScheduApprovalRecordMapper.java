package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduApprovalRecord;
import com.mes.smartdispath.domain.dto.ScheduApprovalRecordQueryDTO;

/**
 * 全局审批记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduApprovalRecordMapper {
    /**
     * 查询业务相关的审批记录
     *
     * @param queryDto 查询参数
     * @return 全局审批记录集合
     */
    public List<ScheduApprovalRecord> selectBizApprovalRecordList(ScheduApprovalRecordQueryDTO queryDto);

    /**
     * 查询全局审批记录
     * 
     * @param id 全局审批记录主键
     * @return 全局审批记录
     */
    public ScheduApprovalRecord selectScheduApprovalRecordById(String id);

    /**
     * 查询全局审批记录列表
     * 
     * @param scheduApprovalRecord 全局审批记录
     * @return 全局审批记录集合
     */
    public List<ScheduApprovalRecord> selectScheduApprovalRecordList(ScheduApprovalRecord scheduApprovalRecord);

    /**
     * 新增全局审批记录
     * 
     * @param scheduApprovalRecord 全局审批记录
     * @return 结果
     */
    public int insertScheduApprovalRecord(ScheduApprovalRecord scheduApprovalRecord);

    /**
     * 批量新增全局审批记录
     *
     * @param approvalRecordList 审批记录列表
     * @return 结果
     */
    public int batchInsertScheduApprovalRecord(List<ScheduApprovalRecord> approvalRecordList);

    /**
     * 修改全局审批记录
     * 
     * @param scheduApprovalRecord 全局审批记录
     * @return 结果
     */
    public int updateScheduApprovalRecord(ScheduApprovalRecord scheduApprovalRecord);

    /**
     * 修改全局审批记录
     *
     * @param scheduApprovalRecord 全局审批记录
     * @return 结果
     */
    public int updateByBizId(ScheduApprovalRecord scheduApprovalRecord);

    /**
     * 删除全局审批记录
     * 
     * @param id 全局审批记录主键
     * @return 结果
     */
    public int deleteScheduApprovalRecordById(String id);

    /**
     * 批量删除全局审批记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduApprovalRecordByIds(String[] ids);
}
