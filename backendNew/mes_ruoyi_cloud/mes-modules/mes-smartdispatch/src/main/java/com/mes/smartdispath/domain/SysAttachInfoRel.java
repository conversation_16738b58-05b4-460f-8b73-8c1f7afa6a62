package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 通用附件关系对象 tb_sys_attach_info_rel
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public class SysAttachInfoRel extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 业务ID */
    @Excel(name = "业务ID")
    private String businessId;

    /** 来源表名 */
    @Excel(name = "来源表名")
    private String tbMark;

    /** 附件ID */
    @Excel(name = "附件ID")
    private String attachId;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setTbMark(String tbMark) {
        this.tbMark = tbMark;
    }

    public String getTbMark() {
        return tbMark;
    }

    public void setAttachId(String attachId) {
        this.attachId = attachId;
    }

    public String getAttachId() {
        return attachId;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("businessId", getBusinessId()).append("tbMark", getTbMark()).append("attachId", getAttachId())
            .append("createBy", getCreateBy()).append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("status", getStatus()).toString();
    }
}
