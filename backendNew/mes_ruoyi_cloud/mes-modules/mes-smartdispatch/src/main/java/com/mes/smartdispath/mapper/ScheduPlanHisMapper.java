package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduPlanHis;

/**
 * 调度计划信息历史Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduPlanHisMapper {
    /**
     * 查询调度计划信息历史
     * 
     * @param id 调度计划信息历史主键
     * @return 调度计划信息历史
     */
    public ScheduPlanHis selectScheduPlanHisById(String id);

    /**
     * 查询调度计划信息历史列表
     * 
     * @param scheduPlanHis 调度计划信息历史
     * @return 调度计划信息历史集合
     */
    public List<ScheduPlanHis> selectScheduPlanHisList(ScheduPlanHis scheduPlanHis);

    /**
     * 新增调度计划信息历史
     * 
     * @param scheduPlanHis 调度计划信息历史
     * @return 结果
     */
    public int insertScheduPlanHis(ScheduPlanHis scheduPlanHis);

    /**
     * 批量新增调度计划信息历史
     *
     * @param scheduPlanHisList 调度计划信息历史List
     * @return 结果
     */
    public int batchInsertScheduPlanHis(List<ScheduPlanHis> scheduPlanHisList);

    /**
     * 修改调度计划信息历史
     * 
     * @param scheduPlanHis 调度计划信息历史
     * @return 结果
     */
    public int updateScheduPlanHis(ScheduPlanHis scheduPlanHis);

    /**
     * 批量修改调度计划信息历史
     *
     * @param scheduPlanHisList 调度计划信息历史List
     * @return 结果
     */
    public int batchUpdateScheduPlanHis(List<ScheduPlanHis> scheduPlanHisList);

    /**
     * 修改调度计划信息历史
     *
     * @param scheduPlanHis 调度计划信息历史
     * @return 结果
     */
    public int updateScheduPlanHisByPlanId(ScheduPlanHis scheduPlanHis);

    /**
     * 删除调度计划信息历史
     * 
     * @param id 调度计划信息历史主键
     * @return 结果
     */
    public int deleteScheduPlanHisById(String id);

    /**
     * 批量删除调度计划信息历史
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduPlanHisByIds(String[] ids);
}
