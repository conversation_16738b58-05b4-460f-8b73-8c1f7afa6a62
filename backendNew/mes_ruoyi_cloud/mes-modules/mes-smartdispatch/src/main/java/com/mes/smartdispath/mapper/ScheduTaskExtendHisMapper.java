package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduTaskExtendHis;

/**
 * 调度任务拓展信息历史Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduTaskExtendHisMapper {
    /**
     * 查询调度任务拓展信息历史
     * 
     * @param id 调度任务拓展信息历史主键
     * @return 调度任务拓展信息历史
     */
    public ScheduTaskExtendHis selectScheduTaskExtendHisById(String id);

    /**
     * 查询调度任务拓展信息历史列表
     * 
     * @param scheduTaskExtendHis 调度任务拓展信息历史
     * @return 调度任务拓展信息历史集合
     */
    public List<ScheduTaskExtendHis> selectScheduTaskExtendHisList(ScheduTaskExtendHis scheduTaskExtendHis);

    /**
     * 新增调度任务拓展信息历史
     * 
     * @param scheduTaskExtendHis 调度任务拓展信息历史
     * @return 结果
     */
    public int insertScheduTaskExtendHis(ScheduTaskExtendHis scheduTaskExtendHis);

    /**
     * 批量新增调度任务拓展信息历史
     *
     * @param scheduTaskExtendHisList 调度任务拓展信息历史List
     * @return 结果
     */
    public int batchInsertScheduTaskExtendHis(List<ScheduTaskExtendHis> scheduTaskExtendHisList);

    /**
     * 修改调度任务拓展信息历史
     * 
     * @param scheduTaskExtendHis 调度任务拓展信息历史
     * @return 结果
     */
    public int updateScheduTaskExtendHis(ScheduTaskExtendHis scheduTaskExtendHis);

    /**
     * 批量修改调度任务拓展信息历史
     *
     * @param scheduTaskExtendHisList 调度任务拓展信息历史List
     * @return 结果
     */
    public int batchUpdateScheduTaskExtendHis(List<ScheduTaskExtendHis> scheduTaskExtendHisList);

    /**
     * 删除调度任务拓展信息历史
     * 
     * @param id 调度任务拓展信息历史主键
     * @return 结果
     */
    public int deleteScheduTaskExtendHisById(String id);

    /**
     * 批量删除调度任务拓展信息历史
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduTaskExtendHisByIds(String[] ids);
}
