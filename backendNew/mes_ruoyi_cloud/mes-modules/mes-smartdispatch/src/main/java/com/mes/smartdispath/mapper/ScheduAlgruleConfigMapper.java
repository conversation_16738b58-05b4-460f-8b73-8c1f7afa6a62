package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduAlgruleConfig;
import com.mes.smartdispath.domain.dto.ScheduAlgruleConfigQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduAlgruleConfigVO;

/**
 * 调度算法规则库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduAlgruleConfigMapper {
    /**
     * 查询调度算法规则库
     * 
     * @param id 调度算法规则库主键
     * @return 调度算法规则库
     */
    public ScheduAlgruleConfig selectScheduAlgruleConfigById(String id);

    /**
     * 查询调度算法规则库列表
     * 
     * @param scheduAlgruleConfig 调度算法规则库
     * @return 调度算法规则库集合
     */
    public List<ScheduAlgruleConfigVO> selectScheduAlgruleConfigList(ScheduAlgruleConfigQueryDTO scheduAlgruleConfig);

    /**
     * 新增调度算法规则库
     * 
     * @param scheduAlgruleConfig 调度算法规则库
     * @return 结果
     */
    public int insertScheduAlgruleConfig(ScheduAlgruleConfig scheduAlgruleConfig);

    /**
     * 批量新增调度算法规则库
     *
     * @param scheduAlgruleConfigList 调度算法规则库List
     * @return 结果
     */
    public int batchInsertScheduAlgruleConfig(List<ScheduAlgruleConfig> scheduAlgruleConfigList);

    /**
     * 修改调度算法规则库
     * 
     * @param scheduAlgruleConfig 调度算法规则库
     * @return 结果
     */
    public int updateScheduAlgruleConfig(ScheduAlgruleConfig scheduAlgruleConfig);

    /**
     * 批量修改调度算法规则库
     *
     * @param scheduAlgruleConfigList 调度算法规则库List
     * @return 结果
     */
    public int batchUpdateScheduAlgruleConfig(List<ScheduAlgruleConfig> scheduAlgruleConfigList);

    /**
     * 删除调度算法规则库
     * 
     * @param id 调度算法规则库主键
     * @return 结果
     */
    public int deleteScheduAlgruleConfigById(String id);

    /**
     * 批量删除调度算法规则库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduAlgruleConfigByIds(String[] ids);
}
