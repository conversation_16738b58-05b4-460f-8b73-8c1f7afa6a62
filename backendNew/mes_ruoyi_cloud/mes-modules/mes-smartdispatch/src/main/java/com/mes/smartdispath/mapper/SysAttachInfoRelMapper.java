package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.SysAttachInfoRel;

/**
 * 通用附件关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface SysAttachInfoRelMapper {
    /**
     * 查询通用附件关系
     * 
     * @param id 通用附件关系主键
     * @return 通用附件关系
     */
    public SysAttachInfoRel selectSysAttachInfoRelById(String id);

    /**
     * 查询通用附件关系列表
     * 
     * @param sysAttachInfoRel 通用附件关系
     * @return 通用附件关系集合
     */
    public List<SysAttachInfoRel> selectSysAttachInfoRelList(SysAttachInfoRel sysAttachInfoRel);

    /**
     * 新增通用附件关系
     * 
     * @param sysAttachInfoRel 通用附件关系
     * @return 结果
     */
    public int insertSysAttachInfoRel(SysAttachInfoRel sysAttachInfoRel);

    /**
     * 修改通用附件关系
     * 
     * @param sysAttachInfoRel 通用附件关系
     * @return 结果
     */
    public int updateSysAttachInfoRel(SysAttachInfoRel sysAttachInfoRel);

    /**
     * 删除通用附件关系
     * 
     * @param id 通用附件关系主键
     * @return 结果
     */
    public int deleteSysAttachInfoRelById(String id);

    /**
     * 批量删除通用附件关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAttachInfoRelByIds(String[] ids);
}
