package com.mes.smartdispath.domain.openApi;

import java.util.List;

import com.mes.smartdispath.domain.ScheduMonitorQualityTarget;

/**
 * 批量新增质量目标DTO
 *
 * @Author: li.haoyang @Date： 2025/7/19
 */
public class AddMonitorQualityTargetApiDTO extends BaseOpenApiDTO {
    private List<ScheduMonitorQualityTarget> targetList;

    public List<ScheduMonitorQualityTarget> getTargetList() {
        return targetList;
    }

    public void setTargetList(List<ScheduMonitorQualityTarget> targetList) {
        this.targetList = targetList;
    }
}
