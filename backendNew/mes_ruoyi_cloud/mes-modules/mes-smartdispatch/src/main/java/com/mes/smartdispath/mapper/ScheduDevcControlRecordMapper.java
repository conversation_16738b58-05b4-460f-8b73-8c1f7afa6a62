package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduDevcControlRecord;
import com.mes.smartdispath.domain.dto.DevcControlRecordQueryDTO;

/**
 * 设备反控记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduDevcControlRecordMapper {
    /**
     * 查询设备反控记录
     * 
     * @param id 设备反控记录主键
     * @return 设备反控记录
     */
    public ScheduDevcControlRecord selectScheduDevcControlRecordById(String id);

    /**
     * 查询设备反控记录列表
     * 
     * @param devcControlRecordQueryDTO 设备反控记录
     * @return 设备反控记录集合
     */
    public List<ScheduDevcControlRecord> selectScheduDevcControlRecordList(
        DevcControlRecordQueryDTO devcControlRecordQueryDTO);

    /**
     * 新增设备反控记录
     * 
     * @param scheduDevcControlRecord 设备反控记录
     * @return 结果
     */
    public int insertScheduDevcControlRecord(ScheduDevcControlRecord scheduDevcControlRecord);

    /**
     * 修改设备反控记录
     * 
     * @param scheduDevcControlRecord 设备反控记录
     * @return 结果
     */
    public int updateScheduDevcControlRecord(ScheduDevcControlRecord scheduDevcControlRecord);

    /**
     * 删除设备反控记录
     * 
     * @param id 设备反控记录主键
     * @return 结果
     */
    public int deleteScheduDevcControlRecordById(String id);

    /**
     * 批量删除设备反控记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduDevcControlRecordByIds(String[] ids);
}
