package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduMonitorQualityResult;
import com.mes.smartdispath.domain.dto.MonitorQualityResultQueryDTO;

/**
 * 质量计算结果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface ScheduMonitorQualityResultMapper {
    /**
     * 查询质量计算结果
     *
     * @param id 质量计算结果主键
     * @return 质量计算结果
     */
    public ScheduMonitorQualityResult selectScheduMonitorQualityResultById(String id);

    /**
     * 查询质量计算结果列表
     *
     * @param scheduMonitorQualityResult 质量计算结果
     * @return 质量计算结果集合
     */
    public List<ScheduMonitorQualityResult> selectScheduMonitorQualityResultList(
        ScheduMonitorQualityResult scheduMonitorQualityResult);

    /**
     * 查询汇总的质量计算结果列表
     *
     * @param queryDTO
     * @return 质量计算结果集合
     */
    public List<ScheduMonitorQualityResult> selectCollectMonitorQualityResultList(
        MonitorQualityResultQueryDTO queryDTO);

    /**
     * 按照业务类型和年度汇总-查询汇总的质量计算结果列表
     *
     * @param queryDTO
     * @return 质量计算结果集合
     */
    public List<ScheduMonitorQualityResult> selectStatYearCollectMonitorQualityResultList(
        MonitorQualityResultQueryDTO queryDTO);

    /**
     * 新增质量计算结果
     *
     * @param scheduMonitorQualityResult 质量计算结果
     * @return 结果
     */
    public int insertScheduMonitorQualityResult(ScheduMonitorQualityResult scheduMonitorQualityResult);

    /**
     * 批量新增质量计算结果
     *
     * @param scheduMonitorQualityResultList 质量计算结果List
     * @return 结果
     */
    public int batchInsertScheduMonitorQualityResult(List<ScheduMonitorQualityResult> scheduMonitorQualityResultList);

    /**
     * 修改质量计算结果
     *
     * @param scheduMonitorQualityResult 质量计算结果
     * @return 结果
     */
    public int updateScheduMonitorQualityResult(ScheduMonitorQualityResult scheduMonitorQualityResult);

    /**
     * 批量修改质量计算结果
     *
     * @param scheduMonitorQualityResultList 质量计算结果List
     * @return 结果
     */
    public int batchUpdateScheduMonitorQualityResult(List<ScheduMonitorQualityResult> scheduMonitorQualityResultList);

    /**
     * 删除质量计算结果
     *
     * @param id 质量计算结果主键
     * @return 结果
     */
    public int deleteScheduMonitorQualityResultById(String id);

    /**
     * 批量删除质量计算结果
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduMonitorQualityResultByIds(String[] ids);
}
