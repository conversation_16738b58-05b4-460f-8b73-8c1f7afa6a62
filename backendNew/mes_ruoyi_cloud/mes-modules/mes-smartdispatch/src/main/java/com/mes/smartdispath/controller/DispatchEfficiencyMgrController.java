package com.mes.smartdispath.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.MaintenancePerformanceStaticVO;
import com.mes.smartdispath.service.IDispatchEfficiencyMgrService;

/**
 * 调度效率评估 Controller
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/dispatchEfficiencymgr")
public class DispatchEfficiencyMgrController extends BaseController {
    @Autowired
    private IDispatchEfficiencyMgrService dispatchEfficiencyMgrService;

    /**
     * 6.1.1、任务完成情况统计
     */
    @GetMapping("/qryTaskStatic")
    public AjaxResult qryTaskStatic(TaskStaticQueryDTO queryDto) {
        return success(dispatchEfficiencyMgrService.qryTaskStatic(queryDto));
    }

    /**
     * 6.1.2、任务完成情况趋势图
     */
    @GetMapping("/qryTaskTrend")
    public AjaxResult qryTaskTrend(TaskStaticQueryDTO queryDto) {
        return success(dispatchEfficiencyMgrService.qryTaskTrend(queryDto));
    }

    /**
     * 6.1.4、计划类型统计
     */
    @GetMapping("/qryPlanTypeStatic")
    public AjaxResult qryPlanTypeStatic(TaskStaticQueryDTO queryDto) {
        return success(dispatchEfficiencyMgrService.qryPlanTypeStatic(queryDto));
    }

    /**
     * 6.1.5、运维人员任务完成情况统计
     */
    @GetMapping("/qryMaintenanceStatic")
    public AjaxResult qryMaintenanceStatic(TaskStaticQueryDTO queryDto) {
        return success(dispatchEfficiencyMgrService.qryMaintenanceStatic(queryDto));
    }

    /**
     * 6.1.6、运维人员任务完成情况统计表
     */
    @GetMapping("/qryMaintenanceList")
    public TableDataInfo qryMaintenanceList(TaskStaticQueryDTO queryDto) {
        startPage();
        List<MaintenancePerformanceStaticVO> list = dispatchEfficiencyMgrService.qryMaintenanceList(queryDto);
        return getDataTable(list);
    }
}
