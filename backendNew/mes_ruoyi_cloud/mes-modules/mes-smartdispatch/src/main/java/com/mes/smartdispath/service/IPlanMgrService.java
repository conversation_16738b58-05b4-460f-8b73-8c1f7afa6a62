package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.ResDevice;
import com.mes.smartdispath.domain.dto.ApprovalDTO;
import com.mes.smartdispath.domain.dto.ResDeviceQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanRuleDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanRuleQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduPlanAssoruleVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoDetailVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.domain.vo.ScheduPlanRuleVO;

/**
 * 计划管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IPlanMgrService {
    /**
     * 计划生成规则查询
     * 
     * @param queryDto 查询参数
     * @return 计划生成规则列表
     */
    public List<ScheduPlanRuleVO> qryPlanRule(ScheduPlanRuleQueryDTO queryDto);

    /**
     * 计划生成规则新增/编辑
     *
     * @param dto
     * @return
     */
    public int savePlanRule(ScheduPlanRuleDTO dto);

    /**
     * 计划生成规则删除
     *
     * @param id
     * @return
     */
    public int delPlanRule(String id);

    /**
     * 计划生成规则审批【支持批量】
     * 
     * @param approvalDTO 审批参数
     */
    public int verifyPlanRule(ApprovalDTO approvalDTO);

    /**
     * 关联伴生计划规则查询
     * 
     * @param mainRuleId 主规则id
     * @return 伴生计划规则列表
     */
    public List<ScheduPlanAssoruleVO> qryAssoPlanRule(String mainRuleId);

    /**
     * 计划信息查询
     *
     * @param queryDto
     * @return 计划信息列表
     */
    public List<ScheduPlanInfoVO> qryPlanList(ScheduPlanInfoQueryDTO queryDto);

    /**
     * 计划生成规则新增/编辑
     *
     * @param dto
     * @return
     */
    public int savePlanInfo(ScheduPlanInfoDTO dto);

    /**
     * 计划信息删除
     *
     * @param id
     * @return
     */
    public int delPlanInfo(String id);

    /**
     * 计划信息审批列表查询
     *
     * @param queryDto
     * @return 计划信息列表
     */
    public List<ScheduPlanInfoVO> qryPlanApprovalList(ScheduPlanInfoQueryDTO queryDto);

    /**
     * 计划信息审核【支持批量】
     *
     * @param approvalDTO 审批参数
     */
    public int verifyPlanInfo(ApprovalDTO approvalDTO);

    /**
     * 计划新增时查询站点关联的设备信息
     *
     * @param queryDTO
     * @return 设备信息列表
     */
    public List<ResDevice> qrySiteLinkDevice(ResDeviceQueryDTO queryDTO);

    /**
     * 计划信息详情查询
     *
     * @param id
     * @return 计划详情
     */
    public ScheduPlanInfoDetailVO qryPlanDetail(String id);
}
