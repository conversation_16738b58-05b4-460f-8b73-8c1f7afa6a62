package com.mes.smartdispath.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mes.common.core.utils.StringUtils;
import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.ScheduMonitorQualityResult;
import com.mes.smartdispath.domain.dto.MonitorQualityResultQueryDTO;
import com.mes.smartdispath.domain.dto.QualityTargetPerformanceQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.MaintainPersonCountVO;
import com.mes.smartdispath.domain.vo.PlanTypeStaticVO;
import com.mes.smartdispath.domain.vo.QuantityTargetPerformanceVO;
import com.mes.smartdispath.domain.vo.QuantityTargetYearCollectVO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQualityTargetVO;
import com.mes.smartdispath.domain.vo.SiteTaskListVO;
import com.mes.smartdispath.domain.vo.TaskStaticVO;
import com.mes.smartdispath.mapper.ResPersonBasicMapper;
import com.mes.smartdispath.mapper.ScheduMonitorQualityResultMapper;
import com.mes.smartdispath.mapper.ScheduMonitorQualityTargetMapper;
import com.mes.smartdispath.mapper.ScheduMonitorQuantityTargetMapper;
import com.mes.smartdispath.mapper.ScheduPlanInfoMapper;
import com.mes.smartdispath.mapper.ScheduTaskInfoMapper;
import com.mes.smartdispath.service.IHomePageService;

import cn.hutool.core.lang.Assert;

/**
 * 首页Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class HomePageServiceImpl implements IHomePageService {
    private static final Logger log = LoggerFactory.getLogger(HomePageServiceImpl.class);

    @Autowired
    private ScheduTaskInfoMapper scheduTaskInfoMapper;

    @Autowired
    private ScheduMonitorQuantityTargetMapper scheduMonitorQuantityTargetMapper;

    @Autowired
    private ScheduPlanInfoMapper scheduPlanInfoMapper;

    @Autowired
    private ResPersonBasicMapper resPersonBasicMapper;

    @Autowired
    private ScheduMonitorQualityTargetMapper scheduMonitorQualityTargetMapper;

    @Autowired
    private ScheduMonitorQualityResultMapper scheduMonitorQualityResultMapper;

    /**
     * 气类周质控活动类型
     */
    @Value("${activityType.weeklyGas}")
    private String weeklyGasActivityType;

    /**
     * 气类月质控活动类型
     */
    @Value("${activityType.monthlyGas}")
    private String monthlyGasActivityType;

    /**
     * 水类周质控活动类型
     */
    @Value("${activityType.weeklyWater}")
    private String weeklyWaterActivityType;

    /**
     * 水类月质控活动类型
     */
    @Value("${activityType.monthlyWater}")
    private String monthlyWaterActivityType;

    /**
     * 统计当日的调度任务
     *
     * @param queryDTO 参数
     * @return 站点任务统计信息
     */
    @Override
    public TaskStaticVO qryTodayTaskStatic(TaskStaticQueryDTO queryDTO) {
        if (StringUtils.isEmpty(queryDTO.getStartDate()) || StringUtils.isEmpty(queryDTO.getEndDate())) {
            // 当日日期
            LocalDate today = LocalDate.now();
            String todayStr = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            queryDTO.setStartDate(todayStr);
            queryDTO.setEndDate(todayStr);
        }
        return scheduTaskInfoMapper.selectPeriodTaskStatic(queryDTO);
    }

    /**
     * 当日的调度任务列表
     *
     * @param queryDTO
     * @return 调度任务列表
     */
    @Override
    public List<SiteTaskListVO> qryTodayTaskList(TaskStaticQueryDTO queryDTO) {
        if (StringUtils.isEmpty(queryDTO.getStartDate()) || StringUtils.isEmpty(queryDTO.getEndDate())) {
            // 当日日期
            LocalDate today = LocalDate.now();
            String todayStr = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            queryDTO.setStartDate(todayStr);
            queryDTO.setEndDate(todayStr);
        }
        // 状态处理
        if (StringUtils.isNotEmpty(queryDTO.getTaskStaus())) {
            List<String> taskStatusArr = Arrays.asList(StringUtils.split(queryDTO.getTaskStaus(), ","));
            queryDTO.setTaskStausArr(taskStatusArr);
        }
        return scheduTaskInfoMapper.selectCollectTaskList(queryDTO);
    }

    /**
     * 数量目标完成情况
     *
     * @param businessType
     * @return
     */
    @Override
    public QuantityTargetPerformanceVO qryQuantityTargetPerformance(String businessType) {
        QuantityTargetPerformanceVO result = new QuantityTargetPerformanceVO();
        // 查询年度的周质控和月质控数量目标总数
        String monthlyActivityType = null;
        String weeklyActivityType = null;
        if (SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE.equals(businessType)) {
            monthlyActivityType = monthlyWaterActivityType;
            weeklyActivityType = weeklyWaterActivityType;
        }
        else if (SysDictConstant.BUSINESS_TYPE_AIR_DICT_CODE.equals(businessType)) {
            monthlyActivityType = monthlyGasActivityType;
            weeklyActivityType = weeklyGasActivityType;
        }
        // 如果没有配置活动类型抛出异常
        if (StringUtils.isEmpty(monthlyActivityType) || StringUtils.isEmpty(weeklyActivityType)) {
            Assert.isTrue(false, "未配置活动类型");
        }
        // 设置目标值
        setQuantityTargetValue(result, businessType, monthlyActivityType, weeklyActivityType);
        // 设置已完成数值
        setQuantityTargetPerformanceValue(result, businessType, monthlyActivityType, weeklyActivityType);
        return result;
    }

    /**
     * 质量目标完成情况
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ScheduMonitorQualityTargetVO> qryQualityTargetPerformance(QualityTargetPerformanceQueryDTO queryDTO) {
        if (StringUtils.isEmpty(queryDTO.getStatYear())) {
            // 统计当年
            queryDTO.setStatYear(String.valueOf(LocalDate.now().getYear()));
        }
        if (StringUtils.isNotEmpty(queryDTO.getMaintainUnitCode())) {
            queryDTO.setMaintainUnitCodeArr(Arrays.asList(StringUtils.split(queryDTO.getMaintainUnitCode(), ",")));
        }
        if (StringUtils.isNotEmpty(queryDTO.getProvinceCode())) {
            queryDTO.setProvinceCodeArr(Arrays.asList(StringUtils.split(queryDTO.getProvinceCode(), ",")));
        }
        List<ScheduMonitorQualityTargetVO> result = scheduMonitorQualityTargetMapper
            .selectStatYearCollectMonitorQualityTargetList(queryDTO);
        // 补充完成情况
        MonitorQualityResultQueryDTO resultQueryDTO = new MonitorQualityResultQueryDTO();
        BeanUtils.copyProperties(queryDTO, resultQueryDTO);
        List<ScheduMonitorQualityResult> qualityResultList = scheduMonitorQualityResultMapper
            .selectStatYearCollectMonitorQualityResultList(resultQueryDTO);
        // 生成字典，key-监测项
        Map<String, ScheduMonitorQualityResult> qualityResultMap = qualityResultList.stream()
            .collect(Collectors.toMap(ScheduMonitorQualityResult::getMonitIndex, Function.identity(), (o1, o2) -> o1));
        for (ScheduMonitorQualityTargetVO qualityTarget : result) {
            // 补充完成情况
            if (null != qualityResultMap.get(qualityTarget.getMonitIndex())) {
                qualityTarget.setAccuracyResult(qualityResultMap.get(qualityTarget.getMonitIndex()).getAccuracy());
                qualityTarget.setPrecisionResult(qualityResultMap.get(qualityTarget.getMonitIndex()).getPrecision());
                qualityTarget.setEffectivenessRateResult(
                    qualityResultMap.get(qualityTarget.getMonitIndex()).getEffectivenessRate());
                qualityTarget
                    .setCaptureRateResult(qualityResultMap.get(qualityTarget.getMonitIndex()).getCaptureRate());
                qualityTarget
                    .setQuactrlPassRateResult(qualityResultMap.get(qualityTarget.getMonitIndex()).getQuactrlPassRate());
            }
        }
        return result;
    }

    /**
     * 运维人员数量统计
     *
     * @return
     */
    @Override
    public MaintainPersonCountVO qryMaintainPersonStatic(String businessType) {
        return resPersonBasicMapper.selectMaintainPersonStatic(businessType, SysDictConstant.MAINTENANCE_PERSON_TYPE,
            SysDictConstant.MAINTENANCE_PERSON_STATUS_ON_DUTY, SysDictConstant.MAINTENANCE_PERSON_STATUS_LEAVE);
    }

    /**
     * 数量目标完成情况-目标值
     *
     * @param result
     * @param businessType
     * @param monthlyActivityType
     * @param weeklyActivityType
     */
    private void setQuantityTargetValue(QuantityTargetPerformanceVO result, String businessType,
        String monthlyActivityType, String weeklyActivityType) {
        // 本月所处年份
        int curMonthYear = LocalDate.now().getYear();
        // 上月所处年份
        int lastMonthYear = LocalDate.now().minusMonths(1).getYear();
        List<QuantityTargetYearCollectVO> quantityTargetYearCollectList = scheduMonitorQuantityTargetMapper
            .selectQuantityTargetYearCollect(businessType, monthlyActivityType, weeklyActivityType, curMonthYear,
                lastMonthYear);
        // 转成字典
        Map<String, QuantityTargetYearCollectVO> quantityTargetYearCollectMap = quantityTargetYearCollectList.stream()
            .collect(Collectors.toMap(QuantityTargetYearCollectVO::getYear, Function.identity(), (o1, o2) -> o1));
        // 本月的目标处理
        if (null != quantityTargetYearCollectMap.get(String.valueOf(curMonthYear))) {
            // 本月所处年份共有多少周
            int curMonthYearWeekCount = getWeekCount(curMonthYear);
            // 本月共多少周
            int curMonthWeekCount = getMonthWeekCount(curMonthYear, LocalDate.now().getMonthValue());
            // 计算公式：周质控目标总数/本月所处年份共有多少周*本月共多少周=本月周质控目标
            result.setCurMonthWeeklyTarget((int) Math
                .ceil(quantityTargetYearCollectMap.get(String.valueOf(curMonthYear)).getWeeklyTarget().doubleValue()
                    / curMonthYearWeekCount * curMonthWeekCount));
            // 计算公式：月质控目标总数/12=本月月质控目标
            result.setCurMonthMonthlyTarget((int) Math.ceil(
                quantityTargetYearCollectMap.get(String.valueOf(curMonthYear)).getMonthlyTarget().doubleValue() / 12));
            result.setCurMonthTotalTarget(result.getCurMonthWeeklyTarget() + result.getCurMonthMonthlyTarget());
        }
        // 上月的目标处理
        if (null != quantityTargetYearCollectMap.get(String.valueOf(lastMonthYear))) {
            // 上月所处年份共有多少周
            int lastMonthYearWeekCount = getWeekCount(lastMonthYear);
            // 上月共多少周
            int lastMonthWeekCount = getMonthWeekCount(lastMonthYear, LocalDate.now().minusMonths(1).getMonthValue());
            result.setLastMonthWeeklyTarget((int) Math
                .ceil(quantityTargetYearCollectMap.get(String.valueOf(lastMonthYear)).getWeeklyTarget().doubleValue()
                    / lastMonthYearWeekCount * lastMonthWeekCount));
            result.setLastMonthMonthlyTarget((int) Math
                .ceil(quantityTargetYearCollectMap.get(String.valueOf(lastMonthYear)).getMonthlyTarget() / 12));
            result.setLastMonthTotalTarget(result.getLastMonthWeeklyTarget() + result.getLastMonthMonthlyTarget());
        }
    }

    private void setQuantityTargetPerformanceValue(QuantityTargetPerformanceVO result, String businessType,
        String monthlyActivityType, String weeklyActivityType) {
        List<String> activityTypeArr = new ArrayList<>();
        activityTypeArr.add(monthlyActivityType);
        activityTypeArr.add(weeklyActivityType);
        List<PlanTypeStaticVO> PlanCompleteCountList = scheduPlanInfoMapper.selectPlanCompleteCount(activityTypeArr,
            businessType);
        // 转字典
        Map<String, PlanTypeStaticVO> planCompleteCountMap = PlanCompleteCountList.stream()
            .collect(Collectors.toMap(PlanTypeStaticVO::getActivityType, Function.identity(), (o1, o2) -> o1));
        int curMonthTotalPerformance = 0;
        int lastMonthTotalPerformance = 0;
        // 本月月质控完成情况
        if (null != planCompleteCountMap.get(monthlyActivityType)) {
            result
                .setCurMonthWeeklyPerformance(planCompleteCountMap.get(monthlyActivityType).getCurMonthCompleteCount());
            result.setLastMonthWeeklyPerformance(
                planCompleteCountMap.get(monthlyActivityType).getLastMonthCompleteCount());
            curMonthTotalPerformance = curMonthTotalPerformance + result.getCurMonthWeeklyPerformance();
            lastMonthTotalPerformance = lastMonthTotalPerformance + result.getLastMonthWeeklyPerformance();
        }
        // 本月周质控完成情况
        if (null != planCompleteCountMap.get(weeklyActivityType)) {
            result
                .setCurMonthMonthlyPerformance(planCompleteCountMap.get(weeklyActivityType).getCurMonthCompleteCount());
            result.setLastMonthMonthlyPerformance(
                planCompleteCountMap.get(weeklyActivityType).getLastMonthCompleteCount());
            curMonthTotalPerformance = curMonthTotalPerformance + result.getCurMonthMonthlyPerformance();
            lastMonthTotalPerformance = lastMonthTotalPerformance + result.getLastMonthMonthlyPerformance();
        }
        result.setCurMonthTotalPerformance(curMonthTotalPerformance);
        result.setLastMonthTotalPerformance(lastMonthTotalPerformance);
    }

    /**
     * 获取某年某月有多少周
     * 
     * @param year
     * @param month
     * @return
     */
    private int getMonthWeekCount(int year, int month) {
        // 获取本月第一天和最后一天
        LocalDate firstDay = LocalDate.of(year, month, 1);
        LocalDate lastDay = firstDay.withDayOfMonth(firstDay.lengthOfMonth());
        // 以周一为一周的开始（中国习惯）
        WeekFields weekFields = WeekFields.of(Locale.CHINA);
        // 获取本月第一天和最后一天分别是第几周
        int startWeek = firstDay.get(weekFields.weekOfMonth());
        int endWeek = lastDay.get(weekFields.weekOfMonth());
        int weekCount = endWeek - startWeek + 1;
        return weekCount;
    }

    /**
     * 获取某年有多少周
     * 
     * @param year
     * @return
     */
    private int getWeekCount(int year) {
        // 获取本年最后一天
        LocalDate lastDay = LocalDate.of(year, 12, 31);
        // 获取以周一为一周开始的周数
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekCount = lastDay.get(weekFields.weekOfYear());
        return weekCount;
    }
}
