package com.mes.smartdispath.constant;

/**
 * @Author: li.haoyang
 * @Description：字典表常量 @Date： 2025/6/30
 */
public class SysDictConstant {
    /**
     * 业务分类的class_code
     */
    public static final String BUSINESS_TYPE_CLASS_CODE = "monitoring_element";

    /**
     * 水类业务类型
     */
    public static final String BUSINESS_TYPE_WATER_DICT_CODE = "water";

    /**
     * 气类业务类型
     */
    public static final String BUSINESS_TYPE_AIR_DICT_CODE = "air";

    /**
     * 站点类型的class_code
     */
    public static final String SITE_TYPE_CLASS_CODE = "site_type";

    /**
     * 计划类别的class_code
     */
    public static final String PLAN_CATEGORY_CLASS_CODE = "plan_category";

    /**
     * 运维人员类型
     */
    public static final String MAINTENANCE_PERSON_TYPE = "1";

    /**
     * 运维人员状态-在岗
     */
    public static final String MAINTENANCE_PERSON_STATUS_ON_DUTY = "0";

    /**
     * 运维人员状态-休假
     */
    public static final String MAINTENANCE_PERSON_STATUS_LEAVE = "2";

    /**
     * 算法基础规则字典的class_code
     */
    public static final String ALGRULE_SUBTYPE_BAS_CLASS_CODE = "algrule_subtype_bas";

    /**
     * 算法动态规则字典的class_code
     */
    public static final String ALGRULE_SUBTYPE_DYN_DICT_CODE = "algrule_subtype_dyn";

    /**
     * 运维公司的角色编码
     */
    public static final String MAINTENANCE_UNIT_ROLE_CODE = "2";
}
