package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 通用附件对象 tb_sys_attach_info
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public class SysAttachInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 附件ID */
    @Excel(name = "附件ID")
    private String attachId;

    /** 附件名称 */
    @Excel(name = "附件名称")
    private String attachName;

    /** 附件类型（1文件，2图片，3视频，4音频，5其他） */
    @Excel(name = "附件类型", readConverterExp = "1=文件，2图片，3视频，4音频，5其他")
    private String attachType;

    /** 存储地址 */
    @Excel(name = "存储地址")
    private String attachPath;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    /** 附件大小(字节) */
    @Excel(name = "附件大小(字节)")
    private String attachSize;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setAttachId(String attachId) {
        this.attachId = attachId;
    }

    public String getAttachId() {
        return attachId;
    }

    public void setAttachName(String attachName) {
        this.attachName = attachName;
    }

    public String getAttachName() {
        return attachName;
    }

    public void setAttachType(String attachType) {
        this.attachType = attachType;
    }

    public String getAttachType() {
        return attachType;
    }

    public void setAttachPath(String attachPath) {
        this.attachPath = attachPath;
    }

    public String getAttachPath() {
        return attachPath;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setAttachSize(String attachSize) {
        this.attachSize = attachSize;
    }

    public String getAttachSize() {
        return attachSize;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("attachId", getAttachId()).append("attachName", getAttachName())
            .append("attachType", getAttachType()).append("attachPath", getAttachPath())
            .append("createTime", getCreateTime()).append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime()).append("updateBy", getUpdateBy()).append("status", getStatus())
            .append("attachSize", getAttachSize()).toString();
    }
}
