package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 调度任务与调度计划关系对象 tb_schedu_task_plan_rel
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduTaskPlanRel extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 任务ID */
    @Excel(name = "任务ID")
    private String taskId;

    /** 任务编号，全局唯一标识 */
    @Excel(name = "任务编号，全局唯一标识")
    private String taskCode;

    /** 调度任务名称 */
    @Excel(name = "调度任务名称")
    private String taskName;

    /** 计划ID */
    @Excel(name = "计划ID")
    private String planId;

    /** 调度计划code */
    @Excel(name = "调度计划code")
    private String planCode;

    /** 调度计划名称 */
    @Excel(name = "调度计划名称")
    private String planName;

    /** 数据状态(A有效，X无效) */
    @Excel(name = "数据状态(A有效，X无效)")
    private String status;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("taskId", getTaskId()).append("taskCode", getTaskCode()).append("taskName", getTaskName())
            .append("planId", getPlanId()).append("planCode", getPlanCode()).append("planName", getPlanName())
            .append("status", getStatus()).append("createTime", getCreateTime()).append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime()).append("updateBy", getUpdateBy()).toString();
    }
}
