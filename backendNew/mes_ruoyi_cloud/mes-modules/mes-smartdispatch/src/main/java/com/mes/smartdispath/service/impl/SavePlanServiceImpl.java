package com.mes.smartdispath.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.IntStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mes.common.core.utils.StringUtils;
import com.mes.smartdispath.constant.CommonConstant;
import com.mes.smartdispath.constant.PlanConstant;
import com.mes.smartdispath.domain.ResDevice;
import com.mes.smartdispath.domain.ResSite;
import com.mes.smartdispath.domain.ResSiteInspectionItem;
import com.mes.smartdispath.domain.ScheduApprovalRecord;
import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;
import com.mes.smartdispath.domain.ScheduPlanExtend;
import com.mes.smartdispath.domain.ScheduPlanHis;
import com.mes.smartdispath.domain.ScheduPlanInfo;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoDTO;
import com.mes.smartdispath.enums.BusinessTypeShortPrefixEnum;
import com.mes.smartdispath.mapper.ResDeviceMapper;
import com.mes.smartdispath.mapper.ResSiteInspectionItemMapper;
import com.mes.smartdispath.mapper.ResSiteMapper;
import com.mes.smartdispath.mapper.ScheduApprovalRecordMapper;
import com.mes.smartdispath.mapper.ScheduMonitorActivityInfoMapper;
import com.mes.smartdispath.mapper.ScheduPlanAssoruleMapper;
import com.mes.smartdispath.mapper.ScheduPlanExtendMapper;
import com.mes.smartdispath.mapper.ScheduPlanHisMapper;
import com.mes.smartdispath.mapper.ScheduPlanInfoMapper;
import com.mes.smartdispath.mapper.ScheduPlanRuleMapper;
import com.mes.smartdispath.service.IGenericMgrService;
import com.mes.smartdispath.service.ISavePlanService;
import com.mes.smartdispath.utils.DateUtils;

/**
 * 保存计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class SavePlanServiceImpl implements ISavePlanService {
    private static final Logger log = LoggerFactory.getLogger(SavePlanServiceImpl.class);

    @Autowired
    private ScheduPlanRuleMapper scheduPlanRuleMapper;

    @Autowired
    private ScheduApprovalRecordMapper scheduApprovalRecordMapper;

    @Autowired
    private ScheduPlanAssoruleMapper scheduPlanAssoruleMapper;

    @Autowired
    private ScheduPlanInfoMapper scheduPlanInfoMapper;

    @Autowired
    private ScheduPlanHisMapper scheduPlanHisMapper;

    @Autowired
    private ResDeviceMapper resDeviceMapper;

    @Autowired
    private ScheduPlanExtendMapper scheduPlanExtendMapper;

    @Autowired
    private IGenericMgrService genericMgrService;

    @Autowired
    private ResSiteMapper resSiteMapper;

    @Autowired
    private ResSiteInspectionItemMapper resSiteInspectionItemMapper;

    /**
     * 断面采样的监测活动大类code
     */
    @Value("${activityType.sectionSampling}")
    private String sectionSamplingActivityType;

    @Autowired
    private ScheduMonitorActivityInfoMapper scheduMonitorActivityInfoMapper;

    /**
     * 保存计划信息
     *
     * @param dto 计划信息
     * @param operationUser 操作用户
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int savePlan(ScheduPlanInfoDTO dto, String operationUser) {
        Date curDateTime = DateUtils.getCurDateTime();
        dto.setUpdateBy(operationUser);
        dto.setUpdateTime(curDateTime);
        String id = null;
        String approvalStatus = null != dto.getSubmitFlag()
            && StringUtils.equals(dto.getSubmitFlag(), CommonConstant.SUBMIT_FLAG_1)
                ? CommonConstant.APPROVAL_STATUS_PENDING
                : null;
        // 如果是提交，那么状态是待调度，否则是暂存 计划调度状态：1-暂存、2-待调度、3-已调度
        String scheduStatus = StringUtils.equals(dto.getSubmitFlag(), CommonConstant.SUBMIT_FLAG_1)
            ? PlanConstant.SCHEDU_STATUS_WAIT
            : PlanConstant.SCHEDU_STATUS_TEMP;
        dto.setApprovalStatus(approvalStatus);
        dto.setScheduStatus(scheduStatus);
        String businessTypeShortPrefix = BusinessTypeShortPrefixEnum.getShortPrefixByType(dto.getBusinessType());
        String curDate = DateUtils.getCurDateWithoutHyphen();
        ScheduPlanInfo scheduPlanInfo = new ScheduPlanInfo();
        BeanUtils.copyProperties(dto, scheduPlanInfo);
        scheduPlanInfo.setIsCompanion(CommonConstant.IS_ASSO_PLAN_FLAG_0);
        int count = 0;
        if (StringUtils.isEmpty(dto.getId())) {
            // 新增逻辑
            log.info("新增计划");
            String planCode = CommonConstant.PLAN_INFO_CODE_PREFIX + businessTypeShortPrefix + curDate + "-"
                + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8);
            scheduPlanInfo.setPlanCode(planCode);
            scheduPlanInfo.setCreateBy(operationUser);
            scheduPlanInfo.setCreateTime(curDateTime);
            scheduPlanInfo.setStatus(CommonConstant.DATA_STATUS_A);
            count = scheduPlanInfoMapper.insertScheduPlanInfo(scheduPlanInfo);
            id = scheduPlanInfo.getId();
            log.info("新增任务计划，id: {}", id);
        }
        else {
            // 编辑逻辑
            log.info("修改计划，id:{}", dto.getId());
            id = dto.getId();
            count = scheduPlanInfoMapper.updateScheduPlanInfo(dto);
        }
        // 如果是提交，那么生成计划相关的伴生计划、扩展信息、任务历史、审批信息、拆分计划等
        if (StringUtils.equals(dto.getSubmitFlag(), CommonConstant.SUBMIT_FLAG_1)) {
            log.info("提交计划，id:{}", id);
            // 要保存的拆分出来的计划
            List<ScheduPlanInfo> planList = new ArrayList<>();
            // 要保存的历史数据
            List<ScheduPlanHis> planHisList = new ArrayList<>();
            ScheduPlanHis planHisInfo = new ScheduPlanHis();
            BeanUtils.copyProperties(scheduPlanInfo, planHisInfo);
            planHisInfo.setPlanId(id);
            planHisList.add(planHisInfo);
            // 要保存的审批数据
            List<ScheduApprovalRecord> approvalRecordList = new ArrayList<>();
            approvalRecordList.add(getApprovalRecord(id, approvalStatus, operationUser, curDateTime));
            // 拆分计划
            ScheduMonitorActivityInfo activityInfo = scheduMonitorActivityInfoMapper
                .selectScheduMonitorActivityInfoByCode(scheduPlanInfo.getActivityType(),
                    scheduPlanInfo.getActivitySubtype());
            // 查询设备列表
            List<ResDevice> deviceList = new ArrayList<>();
            if (null != activityInfo && StringUtils.isNotEmpty(activityInfo.getTestItems())) {
                ResDevice resDeviceParam = new ResDevice();
                resDeviceParam.setCurrentCityId(scheduPlanInfo.getSiteId());
                resDeviceParam.setTestItems(activityInfo.getTestItems());
                deviceList = resDeviceMapper.selectResDeviceList(resDeviceParam);
            }
            if (!deviceList.isEmpty()) {
                String firstDeviceId = deviceList.get(0).getId();
                // 更新主计划的设备
                ScheduPlanInfo planInfoWithDeviceDto = new ScheduPlanInfo();
                planInfoWithDeviceDto.setId(id);
                planInfoWithDeviceDto.setDevcModel(firstDeviceId);
                int updateDeciceCount = scheduPlanInfoMapper.updateScheduPlanInfo(planInfoWithDeviceDto);
                log.info("更新主计划设备条数:{}", updateDeciceCount);
                scheduPlanInfo.setDevcModel(firstDeviceId);
                planHisList.get(0).setDevcModel(firstDeviceId);
                List<ResDevice> finalDeviceList = deviceList;
                IntStream.range(1, deviceList.size()).forEach(i -> {
                    ResDevice device = finalDeviceList.get(i);
                    String planCode = CommonConstant.PLAN_INFO_CODE_PREFIX + businessTypeShortPrefix + curDate + "-"
                        + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8);
                    String planName = scheduPlanInfo.getPlanName() + "-" + i;
                    ScheduPlanInfo planInfo = new ScheduPlanInfo();
                    BeanUtils.copyProperties(scheduPlanInfo, planInfo);
                    planInfo.setPlanCode(planCode);
                    planInfo.setPlanName(planName);
                    planInfo.setDevcModel(device.getId());
                    planList.add(planInfo);
                });
            }
            log.info("要保存的拆分计划条数:{}", planList.size());
            // 处理扩展信息，断面采样需要存储一些扩展信息
            if (dto.getActivityType().equals(sectionSamplingActivityType)) {
                ResSite siteInfo = resSiteMapper.selectResSiteById(scheduPlanInfo.getSiteId());
                ResSiteInspectionItem inspectionItem = resSiteInspectionItemMapper
                    .selectInspectionItemBySiteId(scheduPlanInfo.getSiteId());
                ScheduPlanExtend planExtend = new ScheduPlanExtend();
                BeanUtils.copyProperties(scheduPlanInfo, planExtend);
                planExtend.setPlanId(id);
                if (null != siteInfo) {
                    planExtend.setSiteAttr(siteInfo.getSiteAttribute());
                }
                if (null != inspectionItem) {
                    planExtend.setRiskLevel(inspectionItem.getRiskLevel());
                    planExtend.setRiskPara(inspectionItem.getRiskReason());
                    planExtend.setCollectPara(inspectionItem.getParameters());
                }
                int planExtendCount = scheduPlanExtendMapper.insertScheduPlanExtend(planExtend);
                log.info("保存计划扩展信息条数:{}", planExtendCount);
            }
            List<ScheduMonitorActivityInfo> effectiveActivityList = genericMgrService
                .getEffectiveActivityListByActivityInfo(dto.getSiteId(), activityInfo);
            if (!effectiveActivityList.isEmpty()) {
                List<ScheduPlanInfo> assoPlanList = new ArrayList<>();
                for (ScheduMonitorActivityInfo effectiveActivityInfo : effectiveActivityList) {
                    ScheduPlanInfo assoPlan = new ScheduPlanInfo();
                    BeanUtils.copyProperties(scheduPlanInfo, assoPlan);
                    assoPlan.setPlanCode(CommonConstant.PLAN_INFO_CODE_PREFIX + businessTypeShortPrefix + curDate + "-"
                        + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8));
                    assoPlan.setPlanName(
                        scheduPlanInfo.getPlanName() + "-" + effectiveActivityInfo.getActivityTypeName() + "-伴生计划");
                    assoPlan.setActivityType(effectiveActivityInfo.getActivityTypeCode());
                    assoPlan.setActivitySubtype(effectiveActivityInfo.getActivitySubtypeCode());
                    assoPlan.setIsCompanion(CommonConstant.IS_ASSO_PLAN_FLAG_1);
                    assoPlan.setMainPlanId(id);
                    // 伴生计划无需审批
                    assoPlan.setApprovalStatus(null);
                    assoPlanList.add(assoPlan);
                }
                log.info("要保存伴生计划条数:{}", assoPlanList.size());
                planList.addAll(assoPlanList);
                // 批量保存
                // int assoPlanCount = scheduPlanInfoMapper.batchInsertScheduPlanInfo(assoPlanList);
                // log.info("保存伴生计划条数:{}", assoPlanCount);
            }
            // 保存拆分计划和伴生计划
            if (!planList.isEmpty()) {
                int planCount = scheduPlanInfoMapper.batchInsertScheduPlanInfo(planList);
                log.info("保存拆分计划和伴生计划条数:{}", planCount);
            }
            // 相关历史和审批数据
            for (ScheduPlanInfo planInfo : planList) {
                // 非伴生计划
                if (planInfo.getIsCompanion().equals(CommonConstant.IS_ASSO_PLAN_FLAG_0)) {
                    // 历史数据
                    ScheduPlanHis splitPlanHisInfo = new ScheduPlanHis();
                    BeanUtils.copyProperties(planInfo, splitPlanHisInfo);
                    planHisInfo.setPlanId(planInfo.getId());
                    planHisList.add(planHisInfo);
                    // 审批数据
                    approvalRecordList
                        .add(getApprovalRecord(planInfo.getId(), approvalStatus, operationUser, curDateTime));
                }
            }
            // 保存审核信息到审核表
            int approvalCount = scheduApprovalRecordMapper.batchInsertScheduApprovalRecord(approvalRecordList);
            log.info("保存审核信息数量:{}", approvalCount);
            // 保存一条计划历史信息
            int planHisCount = scheduPlanHisMapper.batchInsertScheduPlanHis(planHisList);
            log.info("保存计划历史信息数量:{}", planHisCount);
        }
        return count;
    }

    /**
     * 保存计划历史信息
     * 
     * @param id
     * @param approvalStatus
     * @param operationUser
     * @param curDateTime
     * @return
     */
    private ScheduApprovalRecord getApprovalRecord(String id, String approvalStatus, String operationUser,
        Date curDateTime) {
        ScheduApprovalRecord scheduApprovalRecord = new ScheduApprovalRecord();
        scheduApprovalRecord.setBusinessId(id);
        scheduApprovalRecord.setModuleType(CommonConstant.APPROVAL_SOURCE_PLAN_INFO);
        scheduApprovalRecord.setApprovalStatus(approvalStatus);
        scheduApprovalRecord.setUpdateBy(operationUser);
        scheduApprovalRecord.setUpdateTime(curDateTime);
        scheduApprovalRecord.setCreateBy(operationUser);
        scheduApprovalRecord.setCreateTime(curDateTime);
        scheduApprovalRecord.setStatus(CommonConstant.DATA_STATUS_A);
        return scheduApprovalRecord;
    }
}
