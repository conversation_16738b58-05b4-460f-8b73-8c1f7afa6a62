package com.mes.smartdispath.domain.openApi;

import java.util.List;

import com.mes.smartdispath.domain.vo.ScheduTaskInfoDetailVO;

/**
 * 调度任务创建接口DTO
 *
 * @Author: li.haoyang @Date： 2025/7/18
 */
public class AddTaskInfoOpenApiDTO extends BaseOpenApiDTO {
    private List<ScheduTaskInfoDetailVO> taskInfos;

    public List<ScheduTaskInfoDetailVO> getTaskInfos() {
        return taskInfos;
    }

    public void setTaskInfos(List<ScheduTaskInfoDetailVO> taskInfos) {
        this.taskInfos = taskInfos;
    }
}
