package com.mes.smartdispath.domain.dto;

import java.util.List;

/**
 * @Author: li.haoyang
 * @Description：数量目标已完成查询DTO @Date： 2025/7/15
 */
public class QuantityTargetCompleteQueryDTO {
    /**
     * 站点ID数组
     */
    private List<String> siteIdArr;

    /**
     * 活动类型数组
     */
    private List<String> activityTypeArr;

    /**
     * 活动子类型数组
     */
    private List<String> activitySubtypeArr;

    /**
     * 统计年数组
     */
    private List<String> statYearArr;

    /**
     * 省份编码数组
     */
    private List<String> provinceCodeArr;

    /**
     * 站点类型数组
     */
    private List<String> siteTypeArr;

    public List<String> getSiteIdArr() {
        return siteIdArr;
    }

    public void setSiteIdArr(List<String> siteIdArr) {
        this.siteIdArr = siteIdArr;
    }

    public List<String> getActivityTypeArr() {
        return activityTypeArr;
    }

    public void setActivityTypeArr(List<String> activityTypeArr) {
        this.activityTypeArr = activityTypeArr;
    }

    public List<String> getActivitySubtypeArr() {
        return activitySubtypeArr;
    }

    public void setActivitySubtypeArr(List<String> activitySubtypeArr) {
        this.activitySubtypeArr = activitySubtypeArr;
    }

    public List<String> getStatYearArr() {
        return statYearArr;
    }

    public void setStatYearArr(List<String> statYearArr) {
        this.statYearArr = statYearArr;
    }

    public List<String> getProvinceCodeArr() {
        return provinceCodeArr;
    }

    public void setProvinceCodeArr(List<String> provinceCodeArr) {
        this.provinceCodeArr = provinceCodeArr;
    }

    public List<String> getSiteTypeArr() {
        return siteTypeArr;
    }

    public void setSiteTypeArr(List<String> siteTypeArr) {
        this.siteTypeArr = siteTypeArr;
    }
}
