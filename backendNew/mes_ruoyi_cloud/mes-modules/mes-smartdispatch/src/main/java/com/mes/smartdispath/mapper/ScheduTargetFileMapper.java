package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ScheduTargetFile;
import com.mes.smartdispath.domain.dto.ScheduTargetFileDTO;
import com.mes.smartdispath.domain.dto.ScheduTargetFileQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduTargetFileVO;

/**
 * 年度数量目标文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduTargetFileMapper {
    /**
     * 查询年度数量目标文件（附件信息一并查询）
     *
     * @param id 年度数量目标文件主键
     * @return 年度数量目标文件
     */
    public ScheduTargetFileDTO selectTargetFileWithAttachInfoById(@Param("id") String id,
        @Param("attachSource") String attachSource);

    /**
     * 查询年度数量目标文件
     * 
     * @param id 年度数量目标文件主键
     * @return 年度数量目标文件
     */
    public ScheduTargetFile selectScheduTargetFileById(String id);

    /**
     * 查询年度数量目标文件列表
     * 
     * @param scheduTargetFile 年度数量目标文件
     * @return 年度数量目标文件集合
     */
    public List<ScheduTargetFileVO> selectScheduTargetFileList(ScheduTargetFileQueryDTO scheduTargetFile);

    /**
     * 新增年度数量目标文件
     * 
     * @param scheduTargetFile 年度数量目标文件
     * @return 结果
     */
    public int insertScheduTargetFile(ScheduTargetFile scheduTargetFile);

    /**
     * 修改年度数量目标文件
     * 
     * @param scheduTargetFile 年度数量目标文件
     * @return 结果
     */
    public int updateScheduTargetFile(ScheduTargetFile scheduTargetFile);

    /**
     * 删除年度数量目标文件
     * 
     * @param id 年度数量目标文件主键
     * @return 结果
     */
    public int deleteScheduTargetFileById(String id);

    /**
     * 批量删除年度数量目标文件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduTargetFileByIds(String[] ids);
}
