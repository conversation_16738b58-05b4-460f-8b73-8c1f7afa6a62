package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.dto.ScheduMonitorQualityTargetQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorQuantityTargetQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTargetFileDTO;
import com.mes.smartdispath.domain.dto.ScheduTargetFileQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQualityTargetVO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQuantityTargetVO;
import com.mes.smartdispath.domain.vo.ScheduTargetFileVO;

/**
 * 目标管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ITargetMgrService {
    /**
     * 数量目标查询
     * 
     * @param queryDto 查询参数
     * @return 数量目标列表
     */
    public List<ScheduMonitorQuantityTargetVO> qryNumTargetList(ScheduMonitorQuantityTargetQueryDTO queryDto);

    /**
     * 数量目标汇总查询
     *
     * @param queryDto 查询参数
     * @return 数量目标列表
     */
    public List<ScheduMonitorQuantityTargetVO> qryCollectNumTargetList(ScheduMonitorQuantityTargetQueryDTO queryDto);

    /**
     * 数量目标新增/编辑
     *
     * @param scheduMonitorQuantityTarget 数量目标信息
     * @return 结果
     */
    public int saveNumTarget(ScheduMonitorQuantityTargetQueryDTO scheduMonitorQuantityTarget);

    /**
     * 数量目标删除
     *
     * @param id 目标信息id
     * @return 结果
     */
    public int delNumTarget(String id);

    /**
     * 数量目标文件列表
     *
     * @param queryParam
     * @return 结果
     */
    public List<ScheduTargetFileVO> qryNumTargetFileList(ScheduTargetFileQueryDTO queryParam);

    /**
     * 数量目标文件新增/编辑
     *
     * @param scheduTargetFileDTO
     * @return 结果
     */
    public int saveNumTargetFile(ScheduTargetFileDTO scheduTargetFileDTO);

    /**
     * 数量目标文件删除
     *
     * @param id 目标信息文件id
     * @return 结果
     */
    public int delNumTargetFile(String id);

    /**
     * 预览数量目标文件查询
     *
     * @param id 目标信息文件id
     * @return 结果
     */
    public ScheduTargetFileDTO qryTargetFileAttachInfo(String id);

    /**
     * 质量目标查询
     *
     * @param queryParam
     * @return 结果
     */
    public List<ScheduMonitorQualityTargetVO> qryQualityTargetList(ScheduMonitorQualityTargetQueryDTO queryParam);

    /**
     * 质量目标新增/编辑
     *
     * @param scheduMonitorQualityTarget
     * @return 结果
     */
    public int saveQualityTarget(ScheduMonitorQualityTargetQueryDTO scheduMonitorQualityTarget);

    /**
     * 质量目标删除
     *
     * @param id 目标信息文件id
     * @return 结果
     */
    public int delQualityTarget(String id);
}
