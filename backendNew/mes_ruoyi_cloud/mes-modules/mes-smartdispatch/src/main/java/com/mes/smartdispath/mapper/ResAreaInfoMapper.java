package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ResAreaInfo;

/**
 * 中国省市区域代码Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface ResAreaInfoMapper {

    /**
     * 查询省、市行政区划表列表
     *
     * @return 行政区划表集合
     */
    public List<ResAreaInfo> selectProvinceCityRegionList();

    /**
     * 查询中国省市区域代码
     * 
     * @param id 中国省市区域代码主键
     * @return 中国省市区域代码
     */
    public ResAreaInfo selectResAreaInfoById(String id);

    /**
     * 查询中国省市区域代码列表
     * 
     * @param resAreaInfo 中国省市区域代码
     * @return 中国省市区域代码集合
     */
    public List<ResAreaInfo> selectResAreaInfoList(ResAreaInfo resAreaInfo);

    /**
     * 新增中国省市区域代码
     * 
     * @param resAreaInfo 中国省市区域代码
     * @return 结果
     */
    public int insertResAreaInfo(ResAreaInfo resAreaInfo);

    /**
     * 修改中国省市区域代码
     * 
     * @param resAreaInfo 中国省市区域代码
     * @return 结果
     */
    public int updateResAreaInfo(ResAreaInfo resAreaInfo);

    /**
     * 删除中国省市区域代码
     * 
     * @param id 中国省市区域代码主键
     * @return 结果
     */
    public int deleteResAreaInfoById(String id);

    /**
     * 批量删除中国省市区域代码
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteResAreaInfoByIds(String[] ids);
}
