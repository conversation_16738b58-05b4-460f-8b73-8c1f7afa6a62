package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;

/**
 * @Author: li.haoyang
 * @Description: 调度监控活动信息查询DTO @Date： 2025/7/9
 */
public class ScheduMonitorActivityInfoQueryDTO extends ScheduMonitorActivityInfo {
    /**
     * 是否区分自动站数组
     */
    private List<String> isAutositeArr;

    private List<String> activityCodeArr;

    private List<String> businessTypeArr;

    private List<String> activityTypeArr;

    private List<String> activitySubtypeArr;

    public List<String> getIsAutositeArr() {
        return isAutositeArr;
    }

    public void setIsAutositeArr(List<String> isAutositeArr) {
        this.isAutositeArr = isAutositeArr;
    }

    public List<String> getActivityCodeArr() {
        return activityCodeArr;
    }

    public void setActivityCodeArr(List<String> activityCodeArr) {
        this.activityCodeArr = activityCodeArr;
    }

    public List<String> getBusinessTypeArr() {
        return businessTypeArr;
    }

    public void setBusinessTypeArr(List<String> businessTypeArr) {
        this.businessTypeArr = businessTypeArr;
    }

    public List<String> getActivityTypeArr() {
        return activityTypeArr;
    }

    public void setActivityTypeArr(List<String> activityTypeArr) {
        this.activityTypeArr = activityTypeArr;
    }

    public List<String> getActivitySubtypeArr() {
        return activitySubtypeArr;
    }

    public void setActivitySubtypeArr(List<String> activitySubtypeArr) {
        this.activitySubtypeArr = activitySubtypeArr;
    }
}
