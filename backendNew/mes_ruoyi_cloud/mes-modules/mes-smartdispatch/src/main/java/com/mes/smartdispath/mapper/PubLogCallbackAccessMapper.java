package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.PubLogCallbackAccess;

/**
 * 接口访问日志记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface PubLogCallbackAccessMapper {
    /**
     * 查询接口访问日志记录
     *
     * @param logId 接口访问日志记录主键
     * @return 接口访问日志记录
     */
    public PubLogCallbackAccess selectPubLogCallbackAccessByLogId(String logId);

    /**
     * 查询接口访问日志记录列表
     *
     * @param pubLogCallbackAccess 接口访问日志记录
     * @return 接口访问日志记录集合
     */
    public List<PubLogCallbackAccess> selectPubLogCallbackAccessList(PubLogCallbackAccess pubLogCallbackAccess);

    /**
     * 新增接口访问日志记录
     *
     * @param pubLogCallbackAccess 接口访问日志记录
     * @return 结果
     */
    public int insertPubLogCallbackAccess(PubLogCallbackAccess pubLogCallbackAccess);

    /**
     * 修改接口访问日志记录
     *
     * @param pubLogCallbackAccess 接口访问日志记录
     * @return 结果
     */
    public int updatePubLogCallbackAccess(PubLogCallbackAccess pubLogCallbackAccess);

    /**
     * 删除接口访问日志记录
     *
     * @param logId 接口访问日志记录主键
     * @return 结果
     */
    public int deletePubLogCallbackAccessByLogId(String logId);

    /**
     * 批量删除接口访问日志记录
     *
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePubLogCallbackAccessByLogIds(String[] logIds);
}
