package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.ResAreaInfo;
import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;
import com.mes.smartdispath.domain.SysDict;
import com.mes.smartdispath.domain.dto.ResSiteQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorActivityInfoQueryDTO;
import com.mes.smartdispath.domain.vo.MaintenanceUnitVO;
import com.mes.smartdispath.domain.vo.RegionTreeVO;
import com.mes.smartdispath.domain.vo.ResSiteVO;
import com.mes.smartdispath.domain.vo.SiteTreeVO;

/**
 * 通用查询Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IGenericMgrService {
    /**
     * 获取行政区划tree
     *
     * @return 行政区划tree(全国根节点)
     */
    public RegionTreeVO getAreaTreeInfo();

    /**
     * 行政区划tree【无全国节点】
     *
     * @return 行政区划列表
     */
    public List<RegionTreeVO> getSimAreaTreeInfo();

    /**
     * 获取监测活动大类
     * 
     * @param queryDTO 监测活动类型
     * @return 监测活动类型集合
     */
    public List<ScheduMonitorActivityInfo> getActivityParentType(ScheduMonitorActivityInfoQueryDTO queryDTO);

    /**
     * 获取监测活动小类
     *
     * @param queryDTO 监测活动类型
     * @return 监测活动类型集合
     */
    public List<ScheduMonitorActivityInfo> getActivityType(ScheduMonitorActivityInfoQueryDTO queryDTO);

    /**
     * 获取行政区划-省
     *
     * @param resAreaInfo 行政区划表
     * @return 行政区划表集合
     */
    public List<ResAreaInfo> getProvinceInfo(ResAreaInfo resAreaInfo);

    /**
     * 获取行政区划-市
     *
     * @param resAreaInfo 行政区划表
     * @return 行政区划表集合
     */
    public List<ResAreaInfo> getCityInfo(ResAreaInfo resAreaInfo);

    /**
     * 业务分类字典列表
     *
     * @param sysDict 字典
     * @return 字典集合
     */
    public List<SysDict> getBusinessType(SysDict sysDict);

    /**
     * 站点类型字典列表
     *
     * @param sysDict 字典
     * @return 字典集合
     */
    public List<SysDict> getSiteType(SysDict sysDict);

    /**
     * 计划类别字典列表
     *
     * @param sysDict 字典
     * @return 字典集合
     */
    public List<SysDict> getPlanCategory(SysDict sysDict);

    /**
     * 站点信息
     *
     * @param queryDto 站点
     * @return 站点集合
     */
    public List<ResSiteVO> getSiteInfo(ResSiteQueryDTO queryDto);

    /**
     * 片区站点树
     *
     * @return 片区站点树
     */
    public List<SiteTreeVO> getPackageTreeInfo();

    /**
     * 行政区划站点树
     *
     * @return 行政区划站点树
     */
    public SiteTreeVO getRegionTreeInfo();

    /**
     * 片区省份树
     *
     * @return 片区省份树
     */
    public List<SiteTreeVO> getPackageProvinceTreeInfo();

    /**
     * 行政区划省份树
     *
     * @return 行政区划省份树
     */
    public SiteTreeVO getRegionProvinceTreeInfo();

    /**
     * 行政区划运维公司树
     *
     * @return 行政区划运维公司树
     */
    public SiteTreeVO getRegionMaintainUnitTreeInfo();

    /**
     * 运维公司查询
     *
     * @param businessType
     * @return
     */
    public List<MaintenanceUnitVO> getMaintainUnitInfo(String businessType);

    /**
     * 站点是否是高风险
     *
     * @param siteId
     * @return
     */
    public Boolean getSiteHighRisk(String siteId);

    /**
     * 获取有效的伴生活动列表
     *
     * @param siteId
     * @param activityType
     * @param activitySubtype
     * @return
     */
    public List<ScheduMonitorActivityInfo> getEffectiveActivityList(String siteId, String activityType,
        String activitySubtype);

    /**
     * 获取有效的伴生活动列表(入参活动信息)
     *
     * @param siteId
     * @param activityInfo
     * @return
     */
    public List<ScheduMonitorActivityInfo> getEffectiveActivityListByActivityInfo(String siteId,
        ScheduMonitorActivityInfo activityInfo);
}
