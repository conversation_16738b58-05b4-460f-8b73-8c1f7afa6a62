package com.mes.smartdispath.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.smartdispath.service.IScheduTimerService;

/**
 * 定时任务调用接口
 *
 * @Author: li.haoyang @Date： 2025/7/22
 */
@RestController
@RequestMapping("/scheduTimer")
public class ScheduTimerController {
    @Autowired
    private IScheduTimerService scheduTimerService;

    /**
     * 调度任务定时推送活动模块
     */
    @GetMapping("/pushTaskToActivitySys")
    public int pushTaskToActivitySys() {
        return scheduTimerService.pushTaskToActivitySys();
    }
}
