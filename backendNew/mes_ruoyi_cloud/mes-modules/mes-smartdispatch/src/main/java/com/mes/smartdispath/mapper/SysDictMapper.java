package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.SysDict;

/**
 * 字典Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface SysDictMapper {
    /**
     * 查询字典
     * 
     * @param id 字典主键
     * @return 字典
     */
    public SysDict selectSysDictById(String id);

    /**
     * 查询字典列表
     * 
     * @param sysDict 字典
     * @return 字典集合
     */
    public List<SysDict> selectSysDictList(SysDict sysDict);

    /**
     * 新增字典
     * 
     * @param sysDict 字典
     * @return 结果
     */
    public int insertSysDict(SysDict sysDict);

    /**
     * 修改字典
     * 
     * @param sysDict 字典
     * @return 结果
     */
    public int updateSysDict(SysDict sysDict);

    /**
     * 删除字典
     * 
     * @param id 字典主键
     * @return 结果
     */
    public int deleteSysDictById(String id);

    /**
     * 批量删除字典
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysDictByIds(String[] ids);
}
