package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.ScheduAlgruleConfig;

/**
 * @Author: li.haoyang @Description： @Date： 2025/7/17
 */
public class ScheduAlgruleConfigQueryDTO extends ScheduAlgruleConfig {
    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 生效区域数组
     */
    private List<String> effectiveRegionArr;

    /**
     * 基础规则编码
     */
    private String algruleSubtypeBasClassCode;

    /**
     * 动态规则编码
     */
    private String algruleSubtypeDynClassCode;

    public ScheduAlgruleConfigQueryDTO() {
        this.algruleSubtypeBasClassCode = SysDictConstant.ALGRULE_SUBTYPE_BAS_CLASS_CODE;
        this.algruleSubtypeDynClassCode = SysDictConstant.ALGRULE_SUBTYPE_DYN_DICT_CODE;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<String> getEffectiveRegionArr() {
        return effectiveRegionArr;
    }

    public void setEffectiveRegionArr(List<String> effectiveRegionArr) {
        this.effectiveRegionArr = effectiveRegionArr;
    }

    public String getAlgruleSubtypeBasClassCode() {
        return algruleSubtypeBasClassCode;
    }

    public void setAlgruleSubtypeBasClassCode(String algruleSubtypeBasClassCode) {
        this.algruleSubtypeBasClassCode = algruleSubtypeBasClassCode;
    }

    public String getAlgruleSubtypeDynClassCode() {
        return algruleSubtypeDynClassCode;
    }

    public void setAlgruleSubtypeDynClassCode(String algruleSubtypeDynClassCode) {
        this.algruleSubtypeDynClassCode = algruleSubtypeDynClassCode;
    }
}
