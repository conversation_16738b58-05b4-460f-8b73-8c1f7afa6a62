package com.mes.smartdispath.service.impl;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mes.common.core.utils.DateUtils;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.smartdispath.domain.PubLogCallbackAccess;
import com.mes.smartdispath.mapper.PubLogCallbackAccessMapper;
import com.mes.smartdispath.service.IPubLogCallbackAccessService;
import com.mes.system.api.model.LoginUser;

/**
 * 接口访问日志记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
public class PubLogCallbackAccessServiceImpl implements IPubLogCallbackAccessService {
    @Autowired
    private PubLogCallbackAccessMapper pubLogCallbackAccessMapper;

    /**
     * 查询接口访问日志记录
     *
     * @param logId 接口访问日志记录主键
     * @return 接口访问日志记录
     */
    @Override
    public PubLogCallbackAccess selectPubLogCallbackAccessByLogId(String logId) {
        return pubLogCallbackAccessMapper.selectPubLogCallbackAccessByLogId(logId);
    }

    /**
     * 查询接口访问日志记录列表
     *
     * @param pubLogCallbackAccess 接口访问日志记录
     * @return 接口访问日志记录
     */
    @Override
    public List<PubLogCallbackAccess> selectPubLogCallbackAccessList(PubLogCallbackAccess pubLogCallbackAccess) {
        return pubLogCallbackAccessMapper.selectPubLogCallbackAccessList(pubLogCallbackAccess);
    }

    /**
     * 新增接口访问日志记录
     *
     * @param pubLogCallbackAccess 接口访问日志记录
     * @param request 接口访问日志记录
     * @return 结果
     */
    @Override
    public int insertPubLogCallbackAccess(PubLogCallbackAccess pubLogCallbackAccess, HttpServletRequest request) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (null != loginUser) {
            pubLogCallbackAccess.setCreateBy(loginUser.getUsername());
        }
        // 获取请求相关信息
        String reqIp = request.getRemoteAddr();

        pubLogCallbackAccess.setReqIp(reqIp);
        pubLogCallbackAccess.setCreateTime(DateUtils.getNowDate());
        pubLogCallbackAccess
            .setSource(pubLogCallbackAccess.getSource() != null ? pubLogCallbackAccess.getSource() : "external");
        return pubLogCallbackAccessMapper.insertPubLogCallbackAccess(pubLogCallbackAccess);
    }
}
