package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ResSite;
import com.mes.smartdispath.domain.dto.ResSiteQueryDTO;
import com.mes.smartdispath.domain.vo.MaintenanceUnitVO;
import com.mes.smartdispath.domain.vo.RegionSiteCoutVO;
import com.mes.smartdispath.domain.vo.ResSiteVO;

/**
 * 点位Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ResSiteMapper {
    /**
     * 运维公司查询
     *
     * @param businessType
     * @return
     */
    List<MaintenanceUnitVO> selectMaintenanceUnitList(@Param("businessType") String businessType);

    /**
     * 查询带有字典等值的点位信息
     *
     * @return 点位信息集合
     */
    List<ResSiteVO> selectSiteVOList(ResSiteQueryDTO resSiteQueryDTO);

    /**
     * 查询片区点位信息
     *
     * @return 片区点位信息集合
     */
    List<ResSiteVO> selectPackageSiteInfos();

    /**
     * 查询区域点位信息
     *
     * @return 区域点位信息集合
     */
    List<ResSiteVO> selectRegionSiteInfos();

    /**
     * 查询区域点位的运维公司信息
     *
     * @return 区域点位信息集合
     */
    List<ResSiteVO> selectRegionSiteMaintainUnitInfos();

    /**
     * 查询区域点位数量
     *
     * @return 区域点位数量集合
     */
    List<RegionSiteCoutVO> selectRegionSiteCoutList(@Param("nationwideRegionCode") String nationwideRegionCode);

    /**
     * 查询点位列表，siteId是id
     *
     * @param queryDto 点位
     * @return 点位集合
     */
    public List<ResSiteVO> selectResSiteInfoList(ResSiteQueryDTO queryDto);

    /**
     * 查询点位
     * 
     * @param id 点位主键
     * @return 点位
     */
    public ResSite selectResSiteById(String id);

    /**
     * 查询点位列表
     * 
     * @param tbResSite 点位
     * @return 点位集合
     */
    public List<ResSite> selectResSiteList(ResSite tbResSite);
}
