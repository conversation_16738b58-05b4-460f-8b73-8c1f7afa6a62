package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduTaskInfo;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.ChartBasicVO;
import com.mes.smartdispath.domain.vo.ScheduTaskInfoVO;
import com.mes.smartdispath.domain.vo.SiteTaskListVO;
import com.mes.smartdispath.domain.vo.TaskCountStaticVO;
import com.mes.smartdispath.domain.vo.TaskStaticVO;

/**
 * 调度任务信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduTaskInfoMapper {
    /**
     * 今年按月统计，单人平均每天要参与的计划数
     *
     * @return
     */
    public List<ChartBasicVO> selectMonthPersonAvgPlanCount();

    /**
     * 今年按月统计，每个任务平均多少计划数
     *
     * @return
     */
    public List<ChartBasicVO> selectMonthPerTaskAvgPlanCount();

    /**
     * 调度任务分布统计，按区域
     *
     * @param queryDTO
     * @return
     */
    public List<TaskCountStaticVO> selectRegionTaskCountStatic(ScheduTaskInfoQueryDTO queryDTO);

    /**
     * 调度任务分布统计，按运维单位
     *
     * @param queryDTO
     * @return
     */
    public List<TaskCountStaticVO> selectMaintainUnitTaskCountStatic(ScheduTaskInfoQueryDTO queryDTO);

    /**
     * 查询某段时间内的任务统计信息
     *
     * @param queryDto
     * @return 任务统计信息
     */
    public TaskStaticVO selectPeriodTaskStatic(TaskStaticQueryDTO queryDto);

    /**
     * 站点任务列表（汇总了某些关联信息）
     *
     * @param queryDto
     * @return 站点任务列表
     */
    List<SiteTaskListVO> selectCollectTaskList(TaskStaticQueryDTO queryDto);

    /**
     * 查询总站任务统计信息
     * 
     * @param queryDto
     * @return 总站任务统计信息
     */
    public TaskStaticVO selectMasterStationTaskStatic(TaskStaticQueryDTO queryDto);

    /**
     * 查询调度任务信息
     * 
     * @param id 调度任务信息主键
     * @return 调度任务信息
     */
    public ScheduTaskInfoVO selectScheduTaskInfoById(String id);

    /**
     * 根据taskCode查询调度任务信息
     *
     * @param taskCode
     * @return 调度任务信息
     */
    public ScheduTaskInfoVO selectScheduTaskInfoByTaskCode(String taskCode);

    /**
     * 查询调度任务信息列表
     * 
     * @param scheduTaskInfo 调度任务信息
     * @return 调度任务信息集合
     */
    public List<ScheduTaskInfoVO> selectScheduTaskInfoList(ScheduTaskInfoQueryDTO scheduTaskInfo);

    /**
     * 新增调度任务信息
     * 
     * @param scheduTaskInfo 调度任务信息
     * @return 结果
     */
    public int insertScheduTaskInfo(ScheduTaskInfo scheduTaskInfo);

    /**
     * 批量新增调度任务信息
     *
     * @param scheduTaskInfoList 调度任务信息List
     * @return 结果
     */
    public int batchInsertScheduTaskInfo(List<ScheduTaskInfo> scheduTaskInfoList);

    /**
     * 修改调度任务信息
     * 
     * @param scheduTaskInfo 调度任务信息
     * @return 结果
     */
    public int updateScheduTaskInfo(ScheduTaskInfo scheduTaskInfo);

    /**
     * 根据taskCode修改调度任务信息
     *
     * @param scheduTaskInfo 调度任务信息
     * @return 结果
     */
    public int updateScheduTaskInfoByTaskCode(ScheduTaskInfo scheduTaskInfo);

    /**
     * 批量修改调度任务信息
     *
     * @param scheduTaskInfoList 调度任务信息List
     * @return 结果
     */
    public int batchUpdateScheduTaskInfo(List<ScheduTaskInfo> scheduTaskInfoList);

    /**
     * 根据taskCode批量修改调度任务信息
     *
     * @param scheduTaskInfoList 调度任务信息List
     * @return 结果
     */
    public int batchUpdateScheduTaskInfoByTaskCode(List<ScheduTaskInfo> scheduTaskInfoList);

    /**
     * 删除调度任务信息
     * 
     * @param id 调度任务信息主键
     * @return 结果
     */
    public int deleteScheduTaskInfoById(String id);

    /**
     * 批量删除调度任务信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduTaskInfoByIds(String[] ids);
}
