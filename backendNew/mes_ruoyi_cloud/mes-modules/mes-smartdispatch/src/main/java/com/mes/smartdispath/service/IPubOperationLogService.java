package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.PubOperationLog;

/**
 * 操作日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IPubOperationLogService {
    /**
     * 查询操作日志
     * 
     * @param id 操作日志主键
     * @return 操作日志
     */
    public PubOperationLog selectPubOperationLogById(String id);

    /**
     * 查询操作日志列表
     * 
     * @param pubOperationLog 操作日志
     * @return 操作日志集合
     */
    public List<PubOperationLog> selectPubOperationLogList(PubOperationLog pubOperationLog);

    /**
     * 新增操作日志
     * 
     * @param pubOperationLog 操作日志
     * @return 结果
     */
    public int insertPubOperationLog(PubOperationLog pubOperationLog);

    /**
     * 修改操作日志
     * 
     * @param pubOperationLog 操作日志
     * @return 结果
     */
    public int updatePubOperationLog(PubOperationLog pubOperationLog);

    /**
     * 批量删除操作日志
     * 
     * @param ids 需要删除的操作日志主键集合
     * @return 结果
     */
    public int deletePubOperationLogByIds(String[] ids);

    /**
     * 删除操作日志信息
     * 
     * @param id 操作日志主键
     * @return 结果
     */
    public int deletePubOperationLogById(String id);
}
