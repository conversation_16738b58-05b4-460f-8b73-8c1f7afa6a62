package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ScheduTaskExtend;
import com.mes.smartdispath.domain.dto.ScheduTaskExtendQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.MaintenancePerformanceStaticVO;
import com.mes.smartdispath.domain.vo.ScheduTaskExtendWithTaskDTO;

/**
 * 调度任务拓展信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduTaskExtendMapper {
    /**
     * 今日执行任务的人员数量
     *
     * @param businessType
     * @return
     */
    public Integer selectTodayTaskExecutorCount(@Param("businessType") String businessType);

    /**
     * 调度任务拓展信息与任务信息联合查询
     *
     * @param queryDto
     * @return
     */
    public List<ScheduTaskExtendWithTaskDTO> selectScheduTaskExtendWithTaskList(TaskStaticQueryDTO queryDto);

    /**
     * 运维人员任务完成情况统计
     * 
     * @param queryDto
     * @return 统计信息
     */
    public List<MaintenancePerformanceStaticVO> selectMaintenanceList(TaskStaticQueryDTO queryDto);

    /**
     * 查询任务覆盖的运维人员数量
     *
     * @param queryDto
     * @return 运维人员数量
     */
    public Integer selectTaskMaintenancePersonCount(TaskStaticQueryDTO queryDto);

    /**
     * 查询调度任务拓展信息
     * 
     * @param id 调度任务拓展信息主键
     * @return 调度任务拓展信息
     */
    public ScheduTaskExtend selectScheduTaskExtendById(String id);

    /**
     * 查询调度任务拓展信息列表
     * 
     * @param scheduTaskExtend 调度任务拓展信息
     * @return 调度任务拓展信息集合
     */
    public List<ScheduTaskExtend> selectScheduTaskExtendList(ScheduTaskExtendQueryDTO scheduTaskExtend);

    /**
     * 新增调度任务拓展信息
     * 
     * @param scheduTaskExtend 调度任务拓展信息
     * @return 结果
     */
    public int insertScheduTaskExtend(ScheduTaskExtend scheduTaskExtend);

    /**
     * 批量新增调度任务拓展信息
     *
     * @param scheduTaskExtendList 调度任务拓展信息List
     * @return 结果
     */
    public int batchInsertScheduTaskExtend(List<ScheduTaskExtend> scheduTaskExtendList);

    /**
     * 修改调度任务拓展信息
     * 
     * @param scheduTaskExtend 调度任务拓展信息
     * @return 结果
     */
    public int updateScheduTaskExtend(ScheduTaskExtend scheduTaskExtend);

    /**
     * 删除调度任务拓展信息
     * 
     * @param id 调度任务拓展信息主键
     * @return 结果
     */
    public int deleteScheduTaskExtendById(String id);

    /**
     * 批量删除调度任务拓展信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduTaskExtendByIds(String[] ids);

    /**
     * 删除调度任务拓展信息
     *
     * @param taskId 调度任务id
     * @return 结果
     */
    public int deleteScheduTaskExtendByTaskId(String taskId);
}
