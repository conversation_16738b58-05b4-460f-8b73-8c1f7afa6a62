package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 点位对象 tb_res_site
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public class ResSite extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 站点统一编码(唯一) */
    @Excel(name = "站点统一编码(唯一)")
    private String siteNumber;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String siteName;

    /** 站点编码(需提供规则) */
    @Excel(name = "站点编码(需提供规则)")
    private String siteCode;

    /** 曾用编码(关系数据，JSON数组) */
    @Excel(name = "曾用编码(关系数据，JSON数组)")
    private String formerCodes;

    /** 监测要素(字典) */
    @Excel(name = "监测要素(字典)")
    private String monitoringElement;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 站点类型(字典) */
    @Excel(name = "站点类型(字典)")
    private String siteType;

    /** 站点状态(字典) */
    @Excel(name = "站点状态(字典)")
    private String siteStatus;

    /** 站点批次(字典) */
    @Excel(name = "站点批次(字典)")
    private String siteBatch;

    /** 省份 */
    @Excel(name = "省份")
    private String province;

    /** 所属地市 */
    @Excel(name = "所属地市")
    private String city;

    /** 运维单位 */
    @Excel(name = "运维单位")
    private String operationUnit;

    /** 所属包件 */
    @Excel(name = "所属包件")
    private String packageId;

    /** 发文经度 */
    @Excel(name = "发文经度")
    private String officialLongitude;

    /** 发文纬度 */
    @Excel(name = "发文纬度")
    private String officialLatitude;

    /** 建设时间 */
    @Excel(name = "建设时间")
    private String constructionTime;

    /** 建设单位 */
    @Excel(name = "建设单位")
    private String constructionUnit;

    /** 部发文站点地址(气独有) */
    @Excel(name = "部发文站点地址(气独有)")
    private String ministryAddress;

    /** 站点实际地址(气必填，水选填) */
    @Excel(name = "站点实际地址(气必填，水选填)")
    private String actualAddress;

    /** 入网时间 */
    @Excel(name = "入网时间")
    private String networkTime;

    /** 水质目标级别(水独有) */
    @Excel(name = "水质目标级别(水独有)")
    private String waterQualityTarget;

    /** 所在流域(水独有) */
    @Excel(name = "所在流域(水独有)")
    private String riverBasin;

    /** 所在水体(水独有) */
    @Excel(name = "所在水体(水独有)")
    private String waterBody;

    /** 河流级别(水独有) */
    @Excel(name = "河流级别(水独有)")
    private String riverLevel;

    /** 汇入水体(水独有) */
    @Excel(name = "汇入水体(水独有)")
    private String inflowWaterBody;

    /** 断面属性(水独有)(字典) */
    @Excel(name = "断面属性(水独有)(字典)")
    private String sectionAttribute;

    /** 断面方向(水独有) */
    @Excel(name = "断面方向(水独有)")
    private String sectionDirection;

    /** 考核省(水独有) */
    @Excel(name = "考核省(水独有)")
    private String assessmentProvince;

    /** 考核地市(水独有) */
    @Excel(name = "考核地市(水独有)")
    private String assessmentCity;

    /** 是否要盐度指标(水独有,Y/N) */
    @Excel(name = "是否要盐度指标(水独有,Y/N)")
    private String hasSalinity;

    /** 采样点数(水独有) */
    @Excel(name = "采样点数(水独有)")
    private String samplingPoints;

    /** 采样方式(水独有) */
    @Excel(name = "采样方式(水独有)")
    private String samplingMethod;

    /** 是否自采自测(水独有,Y/N) */
    @Excel(name = "是否自采自测(水独有,Y/N)")
    private String isSelfTesting;

    /** 是否有自动站(水独有,Y/N) */
    @Excel(name = "是否有自动站(水独有,Y/N)")
    private String hasAutomaticStation;

    /** 建站时间(水独有) */
    @Excel(name = "建站时间(水独有)")
    private String stationConstructionTime;

    /** 承建省份(水独有) */
    @Excel(name = "承建省份(水独有)")
    private String constructionProvince;

    /** 站点分类(水独有)(字典) */
    @Excel(name = "站点分类(水独有)(字典)")
    private String stationClassification;

    /** 站点介绍 */
    @Excel(name = "站点介绍")
    private String siteIntroduction;

    /** 上游(水独有) */
    @Excel(name = "上游(水独有)")
    private String upstream;

    /** 下游(水独有) */
    @Excel(name = "下游(水独有)")
    private String downstream;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatedBy;

    /** 全采时长（单位：分钟），表示完整采样周期的时间长度 */
    @Excel(name = "全采时长", readConverterExp = "单=位：分钟")
    private String fullSamplingDuration;

    /** 站点属性（字典），用于标识站点的功能或特性，如城区站、区域站等 */
    @Excel(name = "站点属性", readConverterExp = "字=典")
    private String siteAttribute;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setSiteNumber(String siteNumber) {
        this.siteNumber = siteNumber;
    }

    public String getSiteNumber() {
        return siteNumber;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }

    public String getSiteCode() {
        return siteCode;
    }

    public void setFormerCodes(String formerCodes) {
        this.formerCodes = formerCodes;
    }

    public String getFormerCodes() {
        return formerCodes;
    }

    public void setMonitoringElement(String monitoringElement) {
        this.monitoringElement = monitoringElement;
    }

    public String getMonitoringElement() {
        return monitoringElement;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType;
    }

    public String getSiteType() {
        return siteType;
    }

    public void setSiteStatus(String siteStatus) {
        this.siteStatus = siteStatus;
    }

    public String getSiteStatus() {
        return siteStatus;
    }

    public void setSiteBatch(String siteBatch) {
        this.siteBatch = siteBatch;
    }

    public String getSiteBatch() {
        return siteBatch;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvince() {
        return province;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCity() {
        return city;
    }

    public void setOperationUnit(String operationUnit) {
        this.operationUnit = operationUnit;
    }

    public String getOperationUnit() {
        return operationUnit;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setOfficialLongitude(String officialLongitude) {
        this.officialLongitude = officialLongitude;
    }

    public String getOfficialLongitude() {
        return officialLongitude;
    }

    public void setOfficialLatitude(String officialLatitude) {
        this.officialLatitude = officialLatitude;
    }

    public String getOfficialLatitude() {
        return officialLatitude;
    }

    public void setConstructionTime(String constructionTime) {
        this.constructionTime = constructionTime;
    }

    public String getConstructionTime() {
        return constructionTime;
    }

    public void setConstructionUnit(String constructionUnit) {
        this.constructionUnit = constructionUnit;
    }

    public String getConstructionUnit() {
        return constructionUnit;
    }

    public void setMinistryAddress(String ministryAddress) {
        this.ministryAddress = ministryAddress;
    }

    public String getMinistryAddress() {
        return ministryAddress;
    }

    public void setActualAddress(String actualAddress) {
        this.actualAddress = actualAddress;
    }

    public String getActualAddress() {
        return actualAddress;
    }

    public void setNetworkTime(String networkTime) {
        this.networkTime = networkTime;
    }

    public String getNetworkTime() {
        return networkTime;
    }

    public void setWaterQualityTarget(String waterQualityTarget) {
        this.waterQualityTarget = waterQualityTarget;
    }

    public String getWaterQualityTarget() {
        return waterQualityTarget;
    }

    public void setRiverBasin(String riverBasin) {
        this.riverBasin = riverBasin;
    }

    public String getRiverBasin() {
        return riverBasin;
    }

    public void setWaterBody(String waterBody) {
        this.waterBody = waterBody;
    }

    public String getWaterBody() {
        return waterBody;
    }

    public void setRiverLevel(String riverLevel) {
        this.riverLevel = riverLevel;
    }

    public String getRiverLevel() {
        return riverLevel;
    }

    public void setInflowWaterBody(String inflowWaterBody) {
        this.inflowWaterBody = inflowWaterBody;
    }

    public String getInflowWaterBody() {
        return inflowWaterBody;
    }

    public void setSectionAttribute(String sectionAttribute) {
        this.sectionAttribute = sectionAttribute;
    }

    public String getSectionAttribute() {
        return sectionAttribute;
    }

    public void setSectionDirection(String sectionDirection) {
        this.sectionDirection = sectionDirection;
    }

    public String getSectionDirection() {
        return sectionDirection;
    }

    public void setAssessmentProvince(String assessmentProvince) {
        this.assessmentProvince = assessmentProvince;
    }

    public String getAssessmentProvince() {
        return assessmentProvince;
    }

    public void setAssessmentCity(String assessmentCity) {
        this.assessmentCity = assessmentCity;
    }

    public String getAssessmentCity() {
        return assessmentCity;
    }

    public void setHasSalinity(String hasSalinity) {
        this.hasSalinity = hasSalinity;
    }

    public String getHasSalinity() {
        return hasSalinity;
    }

    public void setSamplingPoints(String samplingPoints) {
        this.samplingPoints = samplingPoints;
    }

    public String getSamplingPoints() {
        return samplingPoints;
    }

    public void setSamplingMethod(String samplingMethod) {
        this.samplingMethod = samplingMethod;
    }

    public String getSamplingMethod() {
        return samplingMethod;
    }

    public void setIsSelfTesting(String isSelfTesting) {
        this.isSelfTesting = isSelfTesting;
    }

    public String getIsSelfTesting() {
        return isSelfTesting;
    }

    public void setHasAutomaticStation(String hasAutomaticStation) {
        this.hasAutomaticStation = hasAutomaticStation;
    }

    public String getHasAutomaticStation() {
        return hasAutomaticStation;
    }

    public void setStationConstructionTime(String stationConstructionTime) {
        this.stationConstructionTime = stationConstructionTime;
    }

    public String getStationConstructionTime() {
        return stationConstructionTime;
    }

    public void setConstructionProvince(String constructionProvince) {
        this.constructionProvince = constructionProvince;
    }

    public String getConstructionProvince() {
        return constructionProvince;
    }

    public void setStationClassification(String stationClassification) {
        this.stationClassification = stationClassification;
    }

    public String getStationClassification() {
        return stationClassification;
    }

    public void setSiteIntroduction(String siteIntroduction) {
        this.siteIntroduction = siteIntroduction;
    }

    public String getSiteIntroduction() {
        return siteIntroduction;
    }

    public void setUpstream(String upstream) {
        this.upstream = upstream;
    }

    public String getUpstream() {
        return upstream;
    }

    public void setDownstream(String downstream) {
        this.downstream = downstream;
    }

    public String getDownstream() {
        return downstream;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setFullSamplingDuration(String fullSamplingDuration) {
        this.fullSamplingDuration = fullSamplingDuration;
    }

    public String getFullSamplingDuration() {
        return fullSamplingDuration;
    }

    public void setSiteAttribute(String siteAttribute) {
        this.siteAttribute = siteAttribute;
    }

    public String getSiteAttribute() {
        return siteAttribute;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("siteNumber", getSiteNumber()).append("siteName", getSiteName()).append("siteCode", getSiteCode())
            .append("formerCodes", getFormerCodes()).append("monitoringElement", getMonitoringElement())
            .append("longitude", getLongitude()).append("latitude", getLatitude()).append("siteType", getSiteType())
            .append("siteStatus", getSiteStatus()).append("siteBatch", getSiteBatch()).append("province", getProvince())
            .append("city", getCity()).append("operationUnit", getOperationUnit()).append("packageId", getPackageId())
            .append("officialLongitude", getOfficialLongitude()).append("officialLatitude", getOfficialLatitude())
            .append("constructionTime", getConstructionTime()).append("constructionUnit", getConstructionUnit())
            .append("ministryAddress", getMinistryAddress()).append("actualAddress", getActualAddress())
            .append("networkTime", getNetworkTime()).append("waterQualityTarget", getWaterQualityTarget())
            .append("riverBasin", getRiverBasin()).append("waterBody", getWaterBody())
            .append("riverLevel", getRiverLevel()).append("inflowWaterBody", getInflowWaterBody())
            .append("sectionAttribute", getSectionAttribute()).append("sectionDirection", getSectionDirection())
            .append("assessmentProvince", getAssessmentProvince()).append("assessmentCity", getAssessmentCity())
            .append("hasSalinity", getHasSalinity()).append("samplingPoints", getSamplingPoints())
            .append("samplingMethod", getSamplingMethod()).append("isSelfTesting", getIsSelfTesting())
            .append("hasAutomaticStation", getHasAutomaticStation())
            .append("stationConstructionTime", getStationConstructionTime())
            .append("constructionProvince", getConstructionProvince())
            .append("stationClassification", getStationClassification())
            .append("siteIntroduction", getSiteIntroduction()).append("upstream", getUpstream())
            .append("downstream", getDownstream()).append("remarks", getRemarks()).append("tenantId", getTenantId())
            .append("createdBy", getCreatedBy()).append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy()).append("updateTime", getUpdateTime())
            .append("fullSamplingDuration", getFullSamplingDuration()).append("siteAttribute", getSiteAttribute())
            .toString();
    }
}
