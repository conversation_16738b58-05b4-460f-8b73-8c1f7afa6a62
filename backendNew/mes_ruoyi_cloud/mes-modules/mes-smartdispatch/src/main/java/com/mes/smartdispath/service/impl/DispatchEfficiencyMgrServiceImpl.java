package com.mes.smartdispath.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.MaintenancePerformanceStaticVO;
import com.mes.smartdispath.domain.vo.MaintenancePersonStaticVO;
import com.mes.smartdispath.domain.vo.PlanTypeStaticVO;
import com.mes.smartdispath.domain.vo.TaskStaticVO;
import com.mes.smartdispath.domain.vo.TaskTrendVO;
import com.mes.smartdispath.mapper.ResPersonBasicMapper;
import com.mes.smartdispath.mapper.ScheduPlanInfoMapper;
import com.mes.smartdispath.mapper.ScheduTaskExtendMapper;
import com.mes.smartdispath.mapper.ScheduTaskHisMapper;
import com.mes.smartdispath.mapper.ScheduTaskInfoMapper;
import com.mes.smartdispath.service.IDispatchEfficiencyMgrService;

/**
 * 通用查询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class DispatchEfficiencyMgrServiceImpl implements IDispatchEfficiencyMgrService {
    @Autowired
    private ScheduTaskInfoMapper scheduTaskInfoMapper;

    @Autowired
    private ScheduPlanInfoMapper scheduPlanInfoMapper;

    @Autowired
    private ResPersonBasicMapper resPersonBasicMapper;

    @Autowired
    private ScheduTaskExtendMapper scheduTaskExtendMapper;

    @Autowired
    private ScheduTaskHisMapper scheduTaskHisMapper;

    /**
     * 任务完成情况统计
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    @Override
    public TaskStaticVO qryTaskStatic(TaskStaticQueryDTO queryDto) {
        return scheduTaskInfoMapper.selectMasterStationTaskStatic(queryDto);
    }

    /**
     * 任务完成情况趋势图
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    @Override
    public List<TaskTrendVO> qryTaskTrend(TaskStaticQueryDTO queryDto) {
        return scheduTaskHisMapper.selectTaskTrend(queryDto);
    }

    /**
     * 计划类型统计
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    @Override
    public List<PlanTypeStaticVO> qryPlanTypeStatic(TaskStaticQueryDTO queryDto) {
        return scheduPlanInfoMapper.selectPlanTypeStatic(queryDto);
    }

    /**
     * 运维人员数量统计
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    @Override
    public MaintenancePersonStaticVO qryMaintenanceStatic(TaskStaticQueryDTO queryDto) {
        MaintenancePersonStaticVO result = new MaintenancePersonStaticVO();
        queryDto.setMaintainPersonType(SysDictConstant.MAINTENANCE_PERSON_TYPE);
        Integer totalCount = resPersonBasicMapper.selectMaintenancePersonCount(queryDto);
        Integer taskCount = scheduTaskExtendMapper.selectTaskMaintenancePersonCount(queryDto);
        result.setTotalMaintenancePersonCount(totalCount);
        result.setTaskMaintenancePersonCount(taskCount);
        return result;
    }

    /**
     * 运维人员任务完成情况统计表
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    @Override
    public List<MaintenancePerformanceStaticVO> qryMaintenanceList(TaskStaticQueryDTO queryDto) {
        return scheduTaskExtendMapper.selectMaintenanceList(queryDto);
    }
}
