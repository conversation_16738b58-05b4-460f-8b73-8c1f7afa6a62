package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 全局审批记录对象 tb_schedu_approval_record
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduApprovalRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 对应业务记录的唯一标识 */
    @Excel(name = "对应业务记录的唯一标识")
    private String businessId;

    /** 模块类型，如 common_plan,report_plan,warn,plan_rule,task */
    @Excel(name = "模块类型，如 common_plan,report_plan,warn,plan_rule,task")
    private String moduleType;

    /** 审批人名称或账号 */
    @Excel(name = "审批人名称或账号")
    private String approver;

    /** 审批状态: approved-已批准, rejected-已驳回, pending-待审批 */
    @Excel(name = "审批状态: approved-已批准, rejected-已驳回, pending-待审批")
    private String approvalStatus;

    /** 审批意见（可选） */
    @Excel(name = "审批意见", readConverterExp = "可=选")
    private String approvalOpinion;

    /** 审批发生的时间 */
    @Excel(name = "审批发生的时间")
    private String approvalTime;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public String getModuleType() {
        return moduleType;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalOpinion(String approvalOpinion) {
        this.approvalOpinion = approvalOpinion;
    }

    public String getApprovalOpinion() {
        return approvalOpinion;
    }

    public void setApprovalTime(String approvalTime) {
        this.approvalTime = approvalTime;
    }

    public String getApprovalTime() {
        return approvalTime;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("businessId", getBusinessId()).append("moduleType", getModuleType())
            .append("approver", getApprover()).append("approvalStatus", getApprovalStatus())
            .append("approvalOpinion", getApprovalOpinion()).append("approvalTime", getApprovalTime())
            .append("createBy", getCreateBy()).append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("status", getStatus()).append("tenantId", getTenantId())
            .toString();
    }
}
