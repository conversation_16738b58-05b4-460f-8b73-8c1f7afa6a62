package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.dto.QualityTargetPerformanceQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.MaintainPersonCountVO;
import com.mes.smartdispath.domain.vo.QuantityTargetPerformanceVO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQualityTargetVO;
import com.mes.smartdispath.domain.vo.SiteTaskListVO;
import com.mes.smartdispath.domain.vo.TaskStaticVO;

/**
 * 首页Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IHomePageService {

    /**
     * 统计当日的调度任务
     *
     * @param queryDTO 参数
     * @return 任务统计信息
     */
    public TaskStaticVO qryTodayTaskStatic(TaskStaticQueryDTO queryDTO);

    /**
     * 当日的调度任务列表
     *
     * @param queryDTO
     * @return 调度任务列表
     */
    public List<SiteTaskListVO> qryTodayTaskList(TaskStaticQueryDTO queryDTO);

    /**
     * 数量目标完成情况
     *
     * @param businessType
     * @return
     */
    public QuantityTargetPerformanceVO qryQuantityTargetPerformance(String businessType);

    /**
     * 质量目标完成情况
     *
     * @param queryDTO
     * @return
     */
    public List<ScheduMonitorQualityTargetVO> qryQualityTargetPerformance(QualityTargetPerformanceQueryDTO queryDTO);

    /**
     * 运维人员数量统计
     *
     * @return
     */
    public MaintainPersonCountVO qryMaintainPersonStatic(String businessType);
}
