package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 调度计划信息对象 tb_schedu_plan_info
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduPlanInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 计划编码，全局唯一标识符（例如：PLAN20250526001） */
    @Excel(name = "计划编码，全局唯一标识符", readConverterExp = "例=如：PLAN20250526001")
    private String planCode;

    /** 计划名称 */
    @Excel(name = "计划名称")
    private String planName;

    /** 计划调度状态：1-暂存、2-待调度、3-已调度 */
    @Excel(name = "计划调度状态：1-暂存、2-待调度、3-已调度")
    private String scheduStatus;

    /** 计划执行状态：4-执行中，5-已完成，6-已退回，7-已中止 */
    @Excel(name = "计划执行状态：4-执行中，5-已完成，6-已退回，7-已中止")
    private String executeStatus;

    /** 计划来源：1规则生成，2监测预警，3数据审核，4资源管理，5质量管理，6监测活动，7人工填报 */
    @Excel(name = "计划来源：1规则生成，2监测预警，3数据审核，4资源管理，5质量管理，6监测活动，7人工填报")
    private String planSource;

    /** 来源标识，如计划规则ID、或其他外部系统唯一标识 */
    @Excel(name = "来源标识，如计划规则ID、或其他外部系统唯一标识")
    private String sourceId;

    /** 计划优先级：1-紧急，2-重要，3-中等，4-一般 */
    @Excel(name = "计划优先级：1-紧急，2-重要，3-中等，4-一般")
    private String planPriority;

    /** 省名称（如：北京市） */
    @Excel(name = "省名称", readConverterExp = "如=：北京市")
    private String provinceName;

    /** 省行政区域代码 */
    @Excel(name = "省行政区域代码")
    private String provinceCode;

    /** 市名称（如：上海市） */
    @Excel(name = "市名称", readConverterExp = "如=：上海市")
    private String cityName;

    /** 市行政区域代码 */
    @Excel(name = "市行政区域代码")
    private String cityCode;

    /** 业务分类（全部、水、气）,即一级部门分类 */
    @Excel(name = "业务分类", readConverterExp = "全=部、水、气")
    private String businessType;

    /** 站点类型（标准字典如：水断面、水自动站等）二级分类 */
    @Excel(name = "站点类型", readConverterExp = "标=准字典如：水断面、水自动站等")
    private String siteType;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String siteName;

    /** 站点ID */
    @Excel(name = "站点ID")
    private String siteId;

    /** 监测活动大类，三级分类 */
    @Excel(name = "监测活动大类，三级分类")
    private String activityType;

    /** 监测活动子类，四级分类 */
    @Excel(name = "监测活动子类，四级分类")
    private String activitySubtype;

    /** 是否是伴生计划 0-否，1-是 */
    @Excel(name = "是否是伴生计划 0-否，1-是")
    private String isCompanion;

    /** 主计划ID，用于标识该计划所属的主计划 */
    @Excel(name = "主计划ID，用于标识该计划所属的主计划")
    private String mainPlanId;

    /** 计划开始时间 */
    @Excel(name = "计划开始时间")
    private String planStartTime;

    /** 计划结束时间 */
    @Excel(name = "计划结束时间")
    private String planEndTime;

    /** 审批状态: approved-已批准, rejected-已驳回, pending-待审批 */
    @Excel(name = "审批状态: approved-已批准, rejected-已驳回, pending-待审批")
    private String approvalStatus;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 设备型号 */
    @Excel(name = "设备型号")
    private String devcModel;

    /** 工单号 */
    @Excel(name = "工单号")
    private String orderNo;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setScheduStatus(String scheduStatus) {
        this.scheduStatus = scheduStatus;
    }

    public String getScheduStatus() {
        return scheduStatus;
    }

    public void setExecuteStatus(String executeStatus) {
        this.executeStatus = executeStatus;
    }

    public String getExecuteStatus() {
        return executeStatus;
    }

    public void setPlanSource(String planSource) {
        this.planSource = planSource;
    }

    public String getPlanSource() {
        return planSource;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setPlanPriority(String planPriority) {
        this.planPriority = planPriority;
    }

    public String getPlanPriority() {
        return planPriority;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType;
    }

    public String getSiteType() {
        return siteType;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivitySubtype(String activitySubtype) {
        this.activitySubtype = activitySubtype;
    }

    public String getActivitySubtype() {
        return activitySubtype;
    }

    public void setIsCompanion(String isCompanion) {
        this.isCompanion = isCompanion;
    }

    public String getIsCompanion() {
        return isCompanion;
    }

    public void setMainPlanId(String mainPlanId) {
        this.mainPlanId = mainPlanId;
    }

    public String getMainPlanId() {
        return mainPlanId;
    }

    public void setPlanStartTime(String planStartTime) {
        this.planStartTime = planStartTime;
    }

    public String getPlanStartTime() {
        return planStartTime;
    }

    public void setPlanEndTime(String planEndTime) {
        this.planEndTime = planEndTime;
    }

    public String getPlanEndTime() {
        return planEndTime;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setDevcModel(String devcModel) {
        this.devcModel = devcModel;
    }

    public String getDevcModel() {
        return devcModel;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("planCode", getPlanCode()).append("planName", getPlanName())
            .append("scheduStatus", getScheduStatus()).append("executeStatus", getExecuteStatus())
            .append("planSource", getPlanSource()).append("sourceId", getSourceId())
            .append("planPriority", getPlanPriority()).append("provinceName", getProvinceName())
            .append("provinceCode", getProvinceCode()).append("cityName", getCityName())
            .append("cityCode", getCityCode()).append("businessType", getBusinessType())
            .append("siteType", getSiteType()).append("siteName", getSiteName()).append("siteId", getSiteId())
            .append("activityType", getActivityType()).append("activitySubtype", getActivitySubtype())
            .append("isCompanion", getIsCompanion()).append("mainPlanId", getMainPlanId())
            .append("planStartTime", getPlanStartTime()).append("planEndTime", getPlanEndTime())
            .append("remark", getRemark()).append("approvalStatus", getApprovalStatus())
            .append("createTime", getCreateTime()).append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime()).append("updateBy", getUpdateBy()).append("status", getStatus())
            .append("tenantId", getTenantId()).append("devcModel", getDevcModel()).append("orderNo", getOrderNo())
            .toString();
    }
}
