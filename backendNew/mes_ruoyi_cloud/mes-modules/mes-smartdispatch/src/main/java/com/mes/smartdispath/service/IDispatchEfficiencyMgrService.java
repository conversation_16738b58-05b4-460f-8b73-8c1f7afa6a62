package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.MaintenancePerformanceStaticVO;
import com.mes.smartdispath.domain.vo.MaintenancePersonStaticVO;
import com.mes.smartdispath.domain.vo.PlanTypeStaticVO;
import com.mes.smartdispath.domain.vo.TaskStaticVO;
import com.mes.smartdispath.domain.vo.TaskTrendVO;

/**
 * 调度效率评估Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface IDispatchEfficiencyMgrService {
    /**
     * 任务完成情况统计
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    public TaskStaticVO qryTaskStatic(TaskStaticQueryDTO queryDto);

    /**
     * 任务完成情况趋势图
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    public List<TaskTrendVO> qryTaskTrend(TaskStaticQueryDTO queryDto);

    /**
     * 计划类型统计
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    public List<PlanTypeStaticVO> qryPlanTypeStatic(TaskStaticQueryDTO queryDto);

    /**
     * 运维人员数量统计
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    public MaintenancePersonStaticVO qryMaintenanceStatic(TaskStaticQueryDTO queryDto);

    /**
     * 运维人员任务完成情况统计表
     * 
     * @param queryDto 查询参数
     * @return 统计结果
     */
    public List<MaintenancePerformanceStaticVO> qryMaintenanceList(TaskStaticQueryDTO queryDto);
}
