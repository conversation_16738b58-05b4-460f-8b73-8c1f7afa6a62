package com.mes.smartdispath.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mes.smartdispath.constant.CommonConstant;
import com.mes.smartdispath.constant.TaskConstant;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO;
import com.mes.smartdispath.service.IPushTaskService;
import com.mes.smartdispath.service.IScheduTimerService;

/**
 * 定时任务调用接口 Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class ScheduTimerServiceImpl implements IScheduTimerService {
    private static final Logger log = LoggerFactory.getLogger(ScheduTimerServiceImpl.class);

    @Autowired
    private IPushTaskService pushTaskService;

    /**
     * 调度任务定时推送活动模块
     */
    @Override
    public int pushTaskToActivitySys() {
        log.info("调度任务定时推送活动模块");
        ScheduTaskInfoQueryDTO queryDTO = new ScheduTaskInfoQueryDTO();
        queryDTO.setApprovalStatus(CommonConstant.APPROVAL_STATUS_APPROVED);
        queryDTO.setTaskStatus(TaskConstant.TASK_STATUS_WAIT_PUSH);
        queryDTO.setEndDispatchedTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return pushTaskService.pushTaskToActivity(queryDTO, CommonConstant.DEFAULT_SCHEDU_TIMER_USER);
    }
}
