package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 人员基本信息对象 tb_res_person_basic
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public class ResPersonBasic extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 人员ID */
    private String personId;

    /** 用户ID（匹配门户） */
    @Excel(name = "用户ID", readConverterExp = "匹=配门户")
    private Long userId;

    /** 监测要素 */
    @Excel(name = "监测要素")
    private String monitoringElement;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personName;

    /** 性别（M男/F女） */
    @Excel(name = "性别", readConverterExp = "M=男/F女")
    private String sex;

    /** 身份证号码（唯一） */
    @Excel(name = "身份证号码", readConverterExp = "唯=一")
    private String idCard;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phoneNumber;

    /** 人员类型 */
    @Excel(name = "人员类型")
    private String personType;

    /** 组织ID */
    @Excel(name = "组织ID")
    private String deptId;

    /** 人员状态（1在岗/2请假/3离职/0待审批）(字典) */
    @Excel(name = "人员状态", readConverterExp = "1=在岗/2请假/3离职/0待审批")
    private String status;

    /** 是否有驾照（Y/N） */
    @Excel(name = "是否有驾照", readConverterExp = "Y=/N")
    private String hasDrivingLicense;

    /** 能力分级 */
    @Excel(name = "能力分级")
    private String abilityLevel;

    /** 数据来源（0门户，1资源） */
    @Excel(name = "数据来源", readConverterExp = "0=门户，1资源")
    private String dataSource;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatedBy;

    public String getMonitoringElement() {
        return monitoringElement;
    }

    public void setMonitoringElement(String monitoringElement) {
        this.monitoringElement = monitoringElement;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getPersonId() {
        return personId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getPersonName() {
        return personName;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getSex() {
        return sex;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPersonType(String personType) {
        this.personType = personType;
    }

    public String getPersonType() {
        return personType;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setHasDrivingLicense(String hasDrivingLicense) {
        this.hasDrivingLicense = hasDrivingLicense;
    }

    public String getHasDrivingLicense() {
        return hasDrivingLicense;
    }

    public void setAbilityLevel(String abilityLevel) {
        this.abilityLevel = abilityLevel;
    }

    public String getAbilityLevel() {
        return abilityLevel;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("personId", getPersonId())
            .append("userId", getUserId()).append("personName", getPersonName()).append("sex", getSex())
            .append("idCard", getIdCard()).append("phoneNumber", getPhoneNumber()).append("personType", getPersonType())
            .append("deptId", getDeptId()).append("status", getStatus())
            .append("hasDrivingLicense", getHasDrivingLicense()).append("abilityLevel", getAbilityLevel())
            .append("dataSource", getDataSource()).append("tenantId", getTenantId()).append("createdBy", getCreatedBy())
            .append("createTime", getCreateTime()).append("updatedBy", getUpdatedBy())
            .append("updateTime", getUpdateTime()).toString();
    }
}
