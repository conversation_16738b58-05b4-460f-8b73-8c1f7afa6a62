package com.mes.smartdispath.domain;

import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mes.common.core.annotation.Excel;

/**
 * 调度算法规则库对象 tb_schedu_algrule_config
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduAlgruleConfig {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 规则编码，系统生成，全局唯一标识 */
    @Excel(name = "规则编码", sort = 2)
    private String algruleCode;

    /** 规则名称，手动输入 */
    @Excel(name = "规则名称", sort = 1)
    private String algruleName;

    /** 规则大类：基础规则 / 动态规则 */
    private String algruleType;

    /** 规则子类 */
    private String algruleSubtype;

    /** 规则子类对应的取值，josn格式存储 */
    private String algruleExtend;

    /** 状态：1-启用，0-停用 */
    @Excel(name = "状态", readConverterExp = "1=启用,0=停用", sort = 6)
    private String algruleStatus;

    /** 生效区域 */
    @Excel(name = "生效区域", sort = 7)
    private String effectiveRegion;

    /** 算法ID */
    private String algorithmId;

    /** 关联算法名称 */
    @Excel(name = "关联算法", sort = 5)
    private String algorithmName;

    /** 关联算法编码 */
    private String algorithmCode;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @Excel(name = "创建时间", sort = 8)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 软删除标志（Y/N） */
    private String isDeleted;

    /** 租户ID */
    private String tenantId;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setAlgruleCode(String algruleCode) {
        this.algruleCode = algruleCode;
    }

    public String getAlgruleCode() {
        return algruleCode;
    }

    public void setAlgruleName(String algruleName) {
        this.algruleName = algruleName;
    }

    public String getAlgruleName() {
        return algruleName;
    }

    public void setAlgruleType(String algruleType) {
        this.algruleType = algruleType;
    }

    public String getAlgruleType() {
        return algruleType;
    }

    public void setAlgruleSubtype(String algruleSubtype) {
        this.algruleSubtype = algruleSubtype;
    }

    public String getAlgruleSubtype() {
        return algruleSubtype;
    }

    public void setAlgruleExtend(String algruleExtend) {
        this.algruleExtend = algruleExtend;
    }

    public String getAlgruleExtend() {
        return algruleExtend;
    }

    public void setAlgruleStatus(String algruleStatus) {
        this.algruleStatus = algruleStatus;
    }

    public String getAlgruleStatus() {
        return algruleStatus;
    }

    public void setEffectiveRegion(String effectiveRegion) {
        this.effectiveRegion = effectiveRegion;
    }

    public String getEffectiveRegion() {
        return effectiveRegion;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmName(String algorithmName) {
        this.algorithmName = algorithmName;
    }

    public String getAlgorithmName() {
        return algorithmName;
    }

    public void setAlgorithmCode(String algorithmCode) {
        this.algorithmCode = algorithmCode;
    }

    public String getAlgorithmCode() {
        return algorithmCode;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("algruleCode", getAlgruleCode()).append("algruleName", getAlgruleName())
            .append("algruleType", getAlgruleType()).append("algruleSubtype", getAlgruleSubtype())
            .append("algruleExtend", getAlgruleExtend()).append("algruleStatus", getAlgruleStatus())
            .append("effectiveRegion", getEffectiveRegion()).append("algorithmId", getAlgorithmId())
            .append("algorithmName", getAlgorithmName()).append("algorithmCode", getAlgorithmCode())
            .append("createBy", getCreateBy()).append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("isDeleted", getIsDeleted()).append("tenantId", getTenantId())
            .toString();
    }
}
