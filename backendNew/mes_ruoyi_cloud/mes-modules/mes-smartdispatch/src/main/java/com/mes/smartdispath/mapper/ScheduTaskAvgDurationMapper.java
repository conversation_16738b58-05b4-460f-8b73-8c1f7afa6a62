package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduTaskAvgDuration;

/**
 * 活动大类对应的任务平均执行时长Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduTaskAvgDurationMapper {
    /**
     * 查询活动大类对应的任务平均执行时长
     * 
     * @param id 活动大类对应的任务平均执行时长主键
     * @return 活动大类对应的任务平均执行时长
     */
    public ScheduTaskAvgDuration selectScheduTaskAvgDurationById(String id);

    /**
     * 查询活动大类对应的任务平均执行时长列表
     * 
     * @param scheduTaskAvgDuration 活动大类对应的任务平均执行时长
     * @return 活动大类对应的任务平均执行时长集合
     */
    public List<ScheduTaskAvgDuration> selectScheduTaskAvgDurationList(ScheduTaskAvgDuration scheduTaskAvgDuration);

    /**
     * 新增活动大类对应的任务平均执行时长
     * 
     * @param scheduTaskAvgDuration 活动大类对应的任务平均执行时长
     * @return 结果
     */
    public int insertScheduTaskAvgDuration(ScheduTaskAvgDuration scheduTaskAvgDuration);

    /**
     * 修改活动大类对应的任务平均执行时长
     * 
     * @param scheduTaskAvgDuration 活动大类对应的任务平均执行时长
     * @return 结果
     */
    public int updateScheduTaskAvgDuration(ScheduTaskAvgDuration scheduTaskAvgDuration);

    /**
     * 删除活动大类对应的任务平均执行时长
     * 
     * @param id 活动大类对应的任务平均执行时长主键
     * @return 结果
     */
    public int deleteScheduTaskAvgDurationById(String id);

    /**
     * 批量删除活动大类对应的任务平均执行时长
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduTaskAvgDurationByIds(String[] ids);
}
