package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 监测活动类型对象 tb_schedu_monitor_activity_info
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public class ScheduMonitorActivityInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 活动编码 */
    @Excel(name = "活动编码")
    private String activityCode;

    /** 活动类型（如例行工作、点位管理类） */
    @Excel(name = "活动类型", readConverterExp = "如=例行工作、点位管理类")
    private String activityType;

    /** 业务分类（如水、气） */
    @Excel(name = "业务分类", readConverterExp = "如=水、气")
    private String businessType;

    /** 站点类型（如国控点位、区域站、背景站等） */
    @Excel(name = "站点类型", readConverterExp = "如=国控点位、区域站、背景站等")
    private String siteType;

    /** 是否区分自动站，0-非自动站，1-自动站，2-不区分 */
    @Excel(name = "是否区分自动站，0-非自动站，1-自动站，2-不区分")
    private String isAutosite;

    /** 监测活动大类编码 */
    @Excel(name = "监测活动大类编码")
    private String activityTypeCode;

    /** 监测活动大类名称 */
    @Excel(name = "监测活动大类名称")
    private String activityTypeName;

    /** 监测活动子类编码 */
    @Excel(name = "监测活动子类编码")
    private String activitySubtypeCode;

    /** 监测活动子类名称 */
    @Excel(name = "监测活动子类名称")
    private String activitySubtypeName;

    /** 活动描述 */
    @Excel(name = "活动描述")
    private String activityDesc;

    /** 是否有伴生活动，1-有，0-无 */
    @Excel(name = "是否有伴生活动，1-有，0-无")
    private String isAssoactivity;

    /** 伴生活动编码 */
    @Excel(name = "伴生活动编码")
    private String assoactivityCode;

    /** 活动调度时间字典 */
    @Excel(name = "活动调度时间字典")
    private String scheduTime;

    /** 活动调度时间描述 */
    @Excel(name = "活动调度时间描述")
    private String scheduDesc;

    /** 监测项目 */
    @Excel(name = "监测项目")
    private String testItems;

    /** 是否常规的，1是，0否 */
    @Excel(name = "是否常规的，1是，0否")
    private String isRegular;

    /** 是否重要，1是，0否 */
    @Excel(name = "是否重要，1是，0否")
    private String isImportant;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType;
    }

    public String getSiteType() {
        return siteType;
    }

    public void setIsAutosite(String isAutosite) {
        this.isAutosite = isAutosite;
    }

    public String getIsAutosite() {
        return isAutosite;
    }

    public void setActivityTypeCode(String activityTypeCode) {
        this.activityTypeCode = activityTypeCode;
    }

    public String getActivityTypeCode() {
        return activityTypeCode;
    }

    public void setActivityTypeName(String activityTypeName) {
        this.activityTypeName = activityTypeName;
    }

    public String getActivityTypeName() {
        return activityTypeName;
    }

    public void setActivitySubtypeCode(String activitySubtypeCode) {
        this.activitySubtypeCode = activitySubtypeCode;
    }

    public String getActivitySubtypeCode() {
        return activitySubtypeCode;
    }

    public void setActivitySubtypeName(String activitySubtypeName) {
        this.activitySubtypeName = activitySubtypeName;
    }

    public String getActivitySubtypeName() {
        return activitySubtypeName;
    }

    public void setActivityDesc(String activityDesc) {
        this.activityDesc = activityDesc;
    }

    public String getActivityDesc() {
        return activityDesc;
    }

    public void setIsAssoactivity(String isAssoactivity) {
        this.isAssoactivity = isAssoactivity;
    }

    public String getIsAssoactivity() {
        return isAssoactivity;
    }

    public void setAssoactivityCode(String assoactivityCode) {
        this.assoactivityCode = assoactivityCode;
    }

    public String getAssoactivityCode() {
        return assoactivityCode;
    }

    public void setScheduTime(String scheduTime) {
        this.scheduTime = scheduTime;
    }

    public String getScheduTime() {
        return scheduTime;
    }

    public void setScheduDesc(String scheduDesc) {
        this.scheduDesc = scheduDesc;
    }

    public String getScheduDesc() {
        return scheduDesc;
    }

    public void setTestItems(String testItems) {
        this.testItems = testItems;
    }

    public String getTestItems() {
        return testItems;
    }

    public void setIsRegular(String isRegular) {
        this.isRegular = isRegular;
    }

    public String getIsRegular() {
        return isRegular;
    }

    public void setIsImportant(String isImportant) {
        this.isImportant = isImportant;
    }

    public String getIsImportant() {
        return isImportant;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("activityCode", getActivityCode()).append("activityType", getActivityType())
            .append("businessType", getBusinessType()).append("siteType", getSiteType())
            .append("isAutosite", getIsAutosite()).append("activityTypeCode", getActivityTypeCode())
            .append("activityTypeName", getActivityTypeName()).append("activitySubtypeCode", getActivitySubtypeCode())
            .append("activitySubtypeName", getActivitySubtypeName()).append("activityDesc", getActivityDesc())
            .append("isAssoactivity", getIsAssoactivity()).append("assoactivityCode", getAssoactivityCode())
            .append("scheduTime", getScheduTime()).append("scheduDesc", getScheduDesc())
            .append("testItems", getTestItems()).append("isRegular", getIsRegular())
            .append("isImportant", getIsImportant()).append("status", getStatus()).append("createBy", getCreateBy())
            .append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("tenantId", getTenantId()).toString();
    }
}
