package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 调度计划拓展信息对象 tb_schedu_plan_extend
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduPlanExtend extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 计划ID */
    @Excel(name = "计划ID")
    private String planId;

    /** 计划名称 */
    @Excel(name = "计划名称")
    private String planName;

    /** 计划CODE */
    @Excel(name = "计划CODE")
    private String planCode;

    /** 站点ID/测站ID */
    @Excel(name = "站点ID/测站ID")
    private String siteId;

    /** 站点名称/测站名称 */
    @Excel(name = "站点名称/测站名称")
    private String siteName;

    /** 站点属性，如入海口、内陆高盐湖泊、锰三角等，与字典表一致 */
    @Excel(name = "站点属性，如入海口、内陆高盐湖泊、锰三角等，与字典表一致")
    private String siteAttr;

    /** 断面风险等级 */
    @Excel(name = "断面风险等级")
    private String riskLevel;

    /** 风险指标，可变数据，josn方式存储 */
    @Excel(name = "风险指标，可变数据，josn方式存储")
    private String riskPara;

    /** 采集指标，含属性指标，可变数据，josn方式存储 */
    @Excel(name = "采集指标，含属性指标，可变数据，josn方式存储")
    private String collectPara;

    /** 是否加质控（全程序空白，外部平行），1-是，0-否 */
    @Excel(name = "是否加质控", readConverterExp = "全=程序空白，外部平行")
    private String isQualityCtl;

    /** 历史最大采样点数 */
    @Excel(name = "历史最大采样点数")
    private String maxCollPoint;

    /** 是否加标样，1-是，0-否 */
    @Excel(name = "是否加标样，1-是，0-否")
    private String isMark;

    /** 是否加盲样，1-是，0-否 */
    @Excel(name = "是否加盲样，1-是，0-否")
    private String isBlind;

    /** 计划扩展信息 */
    @Excel(name = "计划扩展信息")
    private String planExtend;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteAttr(String siteAttr) {
        this.siteAttr = siteAttr;
    }

    public String getSiteAttr() {
        return siteAttr;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskPara(String riskPara) {
        this.riskPara = riskPara;
    }

    public String getRiskPara() {
        return riskPara;
    }

    public void setCollectPara(String collectPara) {
        this.collectPara = collectPara;
    }

    public String getCollectPara() {
        return collectPara;
    }

    public void setIsQualityCtl(String isQualityCtl) {
        this.isQualityCtl = isQualityCtl;
    }

    public String getIsQualityCtl() {
        return isQualityCtl;
    }

    public void setMaxCollPoint(String maxCollPoint) {
        this.maxCollPoint = maxCollPoint;
    }

    public String getMaxCollPoint() {
        return maxCollPoint;
    }

    public void setIsMark(String isMark) {
        this.isMark = isMark;
    }

    public String getIsMark() {
        return isMark;
    }

    public void setIsBlind(String isBlind) {
        this.isBlind = isBlind;
    }

    public String getIsBlind() {
        return isBlind;
    }

    public void setPlanExtend(String planExtend) {
        this.planExtend = planExtend;
    }

    public String getPlanExtend() {
        return planExtend;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("planId", getPlanId()).append("planName", getPlanName()).append("planCode", getPlanCode())
            .append("siteId", getSiteId()).append("siteName", getSiteName()).append("siteAttr", getSiteAttr())
            .append("riskLevel", getRiskLevel()).append("riskPara", getRiskPara())
            .append("collectPara", getCollectPara()).append("isQualityCtl", getIsQualityCtl())
            .append("maxCollPoint", getMaxCollPoint()).append("isMark", getIsMark()).append("isBlind", getIsBlind())
            .append("planExtend", getPlanExtend()).toString();
    }
}
