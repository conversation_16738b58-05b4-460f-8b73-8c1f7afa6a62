package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 活动大类对应的任务平均执行时长对象 tb_schedu_task_avg_duration
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduTaskAvgDuration extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 站点ID */
    @Excel(name = "站点ID")
    private String siteId;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String siteName;

    /** 监测活动大类编码 */
    @Excel(name = "监测活动大类编码")
    private String activityTypeCode;

    /** 监测活动大类名称 */
    @Excel(name = "监测活动大类名称")
    private String activityTypeName;

    /** 任务平均执行时长 */
    @Excel(name = "任务平均执行时长")
    private String avgDuration;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setActivityTypeCode(String activityTypeCode) {
        this.activityTypeCode = activityTypeCode;
    }

    public String getActivityTypeCode() {
        return activityTypeCode;
    }

    public void setActivityTypeName(String activityTypeName) {
        this.activityTypeName = activityTypeName;
    }

    public String getActivityTypeName() {
        return activityTypeName;
    }

    public void setAvgDuration(String avgDuration) {
        this.avgDuration = avgDuration;
    }

    public String getAvgDuration() {
        return avgDuration;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("siteId", getSiteId()).append("siteName", getSiteName())
            .append("activityTypeCode", getActivityTypeCode()).append("activityTypeName", getActivityTypeName())
            .append("avgDuration", getAvgDuration()).append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime()).toString();
    }
}
