package com.mes.smartdispath;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

import com.mes.common.security.annotation.EnableCustomConfig;
import com.mes.common.security.annotation.EnableRyFeignClients;

/**
 * 智能调度模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication(scanBasePackages = {
    "com.mes"
})
@MapperScan("com.mes.**.mapper")
@EnableFeignClients(basePackages = {
    "com.mes.tower.api", "com.mes.system.api", "com.mes.activity.api"
})
public class MesSmartDispathApplication {
    public static void main(String[] args) {
        SpringApplication.run(MesSmartDispathApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  智能调度模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
            " _____ ______   _______   ________      \n" +
            "|\\   _ \\  _   \\|\\  ___ \\ |\\   ____\\     \n" +
            "\\ \\  \\\\\\__\\ \\  \\ \\   __/|\\ \\  \\___|_    \n" +
            " \\ \\  \\\\|__| \\  \\ \\  \\_|/_\\ \\_____  \\   \n" +
            "  \\ \\  \\    \\ \\  \\ \\  \\_|\\ \\|____|\\  \\  \n" +
            "   \\ \\__\\    \\ \\__\\ \\_______\\____\\_\\  \\ \n" +
            "    \\|__|     \\|__|\\|_______|\\_________\\\n" +
            "                            \\|_________|\n");
    }
}
