package com.mes.smartdispath.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.utils.poi.ExcelUtil;
import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.smartdispath.config.aspect.OperationLogEndpoint;
import com.mes.smartdispath.domain.ScheduAlgorithmInfo;
import com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord;
import com.mes.smartdispath.domain.ScheduAlgruleConfig;
import com.mes.smartdispath.domain.dto.ScheduAlgruleConfigQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduAlgorithmInvokeRecordVO;
import com.mes.smartdispath.domain.vo.ScheduAlgruleConfigVO;
import com.mes.smartdispath.enums.OperationTypeEnum;
import com.mes.smartdispath.service.IDispatchRuleMgrService;

import io.jsonwebtoken.lang.Assert;

/**
 * 调度规则管理 Controller
 *
 * <AUTHOR>
 * @date 2025/7/14
 */
@RestController
@RequestMapping("/dispatchrulemgr")
public class DispatchRuleMgrController extends BaseController {
    @Autowired
    private IDispatchRuleMgrService dispatchRuleMgrService;

    /**
     * 4.1.1、算法列表查询
     */
    @GetMapping("/qryAlgorithmList")
    public TableDataInfo qryAlgorithmList(ScheduAlgorithmInfo scheduAlgorithmInfo) {
        startPage();
        List<ScheduAlgorithmInfo> list = dispatchRuleMgrService.qryAlgorithmList(scheduAlgorithmInfo);
        return getDataTable(list);
    }

    /**
     * 4.1.2、算法新增/编辑
     */
    @PostMapping("/saveAlgorithmInfo")
    public AjaxResult saveAlgorithmInfo(@RequestBody ScheduAlgorithmInfo scheduAlgorithmInfo) {
        return toAjax(dispatchRuleMgrService.saveAlgorithmInfo(scheduAlgorithmInfo));
    }

    /**
     * 4.1.3、算法删除
     */
    @DeleteMapping("/delAlgorithmInfo/{id}")
    public AjaxResult delAlgorithmInfo(@PathVariable String id) {
        Assert.notNull(id, "删除数据的id参数不能为空,请核对请求参数!");
        return toAjax(dispatchRuleMgrService.delAlgorithmInfo(id));
    }

    /**
     * 4.2.1、算法调用情况查询
     */
    @GetMapping("/qryAlgorithmCallList")
    public TableDataInfo qryAlgorithmCallList(ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord) {
        startPage();
        List<ScheduAlgorithmInvokeRecordVO> list = dispatchRuleMgrService
            .qryAlgorithmCallList(scheduAlgorithmInvokeRecord);
        return getDataTable(list);
    }

    /**
     * 4.2.2、算法调用情况导出
     */
    @PostMapping("/exportAlgorithmCall")
    @OperationLogEndpoint(module = "算法调用情况导出", operationType = OperationTypeEnum.EXPORT, operationContent = "算法调用情况导出")
    public void exportAlgorithmCall(HttpServletResponse response,
        ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord) {
        List<ScheduAlgorithmInvokeRecordVO> list = dispatchRuleMgrService
            .qryAlgorithmCallList(scheduAlgorithmInvokeRecord);
        ExcelUtil<ScheduAlgorithmInvokeRecordVO> util = new ExcelUtil<>(ScheduAlgorithmInvokeRecordVO.class);
        util.exportExcel(response, list, "算法调用执行记录", false, false);
    }

    /**
     * 4.2.3、算法调用详细列表查询
     */
    @GetMapping("/qryAlgorithmCallDetailList")
    public TableDataInfo qryAlgorithmCallDetailList(ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord) {
        startPage();
        List<ScheduAlgorithmInvokeRecord> list = dispatchRuleMgrService
            .qryAlgorithmCallDetailList(scheduAlgorithmInvokeRecord);
        return getDataTable(list);
    }

    /**
     * 4.3.1、算法规则库查询
     */
    @GetMapping("/qryAlgorithmLibrary")
    public TableDataInfo qryAlgorithmLibrary(ScheduAlgruleConfigQueryDTO queryDTO) {
        startPage();
        List<ScheduAlgruleConfigVO> list = dispatchRuleMgrService.qryAlgorithmLibrary(queryDTO);
        return getDataTable(list);
    }

    /**
     * 4.3.2、算法规则新增/编辑
     */
    @PostMapping("/saveAlgorithmLibrary")
    public AjaxResult saveAlgorithmLibrary(@RequestBody ScheduAlgruleConfig scheduAlgruleConfig) {
        return toAjax(dispatchRuleMgrService.saveAlgorithmLibrary(scheduAlgruleConfig));
    }

    /**
     * 4.3.3、算法规则删除
     */
    @DeleteMapping("/delAlgorithmLibrary/{id}")
    public AjaxResult delAlgorithmLibrary(@PathVariable String id) {
        Assert.notNull(id, "删除数据的id参数不能为空,请核对请求参数!");
        return toAjax(dispatchRuleMgrService.delAlgorithmLibrary(id));
    }

    /**
     * 4.3.4、算法规则库导出
     */
    @PostMapping("/exportAlgorithmLibrary")
    @OperationLogEndpoint(module = "算法规则库导出", operationType = OperationTypeEnum.EXPORT, operationContent = "算法规则库导出")
    public void exportAlgorithmLibrary(HttpServletResponse response, ScheduAlgruleConfigQueryDTO queryDTO) {
        List<ScheduAlgruleConfigVO> list = dispatchRuleMgrService.qryAlgorithmLibrary(queryDTO);
        ExcelUtil<ScheduAlgruleConfigVO> util = new ExcelUtil<>(ScheduAlgruleConfigVO.class);
        util.exportExcel(response, list, "调度算法规则库", false, false);
    }
}
