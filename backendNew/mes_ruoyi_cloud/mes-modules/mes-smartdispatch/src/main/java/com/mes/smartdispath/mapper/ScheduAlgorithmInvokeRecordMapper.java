package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord;
import com.mes.smartdispath.domain.vo.ScheduAlgorithmInvokeRecordVO;

/**
 * 算法调用执行记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduAlgorithmInvokeRecordMapper {
    /**
     * 查询算法调用执行记录
     * 
     * @param id 算法调用执行记录主键
     * @return 算法调用执行记录
     */
    public ScheduAlgorithmInvokeRecord selectScheduAlgorithmInvokeRecordById(String id);

    /**
     * 查询算法调用执行记录汇总列表
     *
     * @param scheduAlgorithmInvokeRecord 算法调用执行记录
     * @return 算法调用执行记录汇总列表
     */
    public List<ScheduAlgorithmInvokeRecordVO> selectCollectScheduAlgorithmInvokeRecordList(
        ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord);

    /**
     * 查询算法调用执行记录列表
     * 
     * @param scheduAlgorithmInvokeRecord 算法调用执行记录
     * @return 算法调用执行记录集合
     */
    public List<ScheduAlgorithmInvokeRecord> selectScheduAlgorithmInvokeRecordList(
        ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord);

    /**
     * 新增算法调用执行记录
     * 
     * @param scheduAlgorithmInvokeRecord 算法调用执行记录
     * @return 结果
     */
    public int insertScheduAlgorithmInvokeRecord(ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord);

    /**
     * 批量新增算法调用执行记录
     *
     * @param scheduAlgorithmInvokeRecordList 算法调用执行记录List
     * @return 结果
     */
    public int batchInsertScheduAlgorithmInvokeRecord(
        List<ScheduAlgorithmInvokeRecord> scheduAlgorithmInvokeRecordList);

    /**
     * 修改算法调用执行记录
     * 
     * @param scheduAlgorithmInvokeRecord 算法调用执行记录
     * @return 结果
     */
    public int updateScheduAlgorithmInvokeRecord(ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord);

    /**
     * 批量修改算法调用执行记录
     *
     * @param scheduAlgorithmInvokeRecordList 算法调用执行记录List
     * @return 结果
     */
    public int batchUpdateScheduAlgorithmInvokeRecord(
        List<ScheduAlgorithmInvokeRecord> scheduAlgorithmInvokeRecordList);

    /**
     * 删除算法调用执行记录
     * 
     * @param id 算法调用执行记录主键
     * @return 结果
     */
    public int deleteScheduAlgorithmInvokeRecordById(String id);

    /**
     * 批量删除算法调用执行记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduAlgorithmInvokeRecordByIds(String[] ids);
}
