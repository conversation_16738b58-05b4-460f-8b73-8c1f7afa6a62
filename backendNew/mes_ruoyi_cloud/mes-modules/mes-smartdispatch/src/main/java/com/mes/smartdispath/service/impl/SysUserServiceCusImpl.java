package com.mes.smartdispath.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mes.common.datascope.annotation.DataScope;
import com.mes.smartdispath.domain.SysUserCus;
import com.mes.smartdispath.mapper.SysUserMapperCus;
import com.mes.smartdispath.service.ISysUserServiceCus;

/**
 * 用户 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysUserServiceCusImpl implements ISysUserServiceCus {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceCusImpl.class);

    @Autowired
    private SysUserMapperCus userMapperCus;

    /**
     * 根据条件分页查询用户列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUserCus> selectUserList(SysUserCus user) {
        return userMapperCus.selectUserList(user);
    }
}
