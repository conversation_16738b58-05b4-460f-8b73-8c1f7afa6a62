package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduPlanRule;
import com.mes.smartdispath.domain.dto.ScheduPlanRuleQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduPlanRuleVO;

/**
 * 计划规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduPlanRuleMapper {
    /**
     * 查询计划规则
     * 
     * @param id 计划规则主键
     * @return 计划规则
     */
    public ScheduPlanRule selectScheduPlanRuleById(String id);

    /**
     * 查询计划规则列表
     * 
     * @param scheduPlanRule 计划规则
     * @return 计划规则集合
     */
    public List<ScheduPlanRuleVO> selectScheduPlanRuleList(ScheduPlanRuleQueryDTO scheduPlanRule);

    /**
     * 新增计划规则
     * 
     * @param scheduPlanRule 计划规则
     * @return 结果
     */
    public int insertScheduPlanRule(ScheduPlanRule scheduPlanRule);

    /**
     * 修改计划规则
     * 
     * @param scheduPlanRule 计划规则
     * @return 结果
     */
    public int updateScheduPlanRule(ScheduPlanRule scheduPlanRule);

    /**
     * 修改多个计划规则
     *
     * @param scheduPlanRule 计划规则
     * @return 结果
     */
    public int updateScheduPlanRuleByIdArr(ScheduPlanRuleQueryDTO scheduPlanRule);

    /**
     * 删除计划规则
     * 
     * @param id 计划规则主键
     * @return 结果
     */
    public int deleteScheduPlanRuleById(String id);

    /**
     * 批量删除计划规则
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduPlanRuleByIds(String[] ids);
}
