package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.domain.ScheduMonitorQuantityTarget;

/**
 * 年度监测活动数量目标查询参数
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduMonitorQuantityTargetQueryDTO extends ScheduMonitorQuantityTarget {
    /**
     * 区域编码集合
     */
    private List<String> cityCodeArr;

    /**
     * 省份编码集合
     */
    private List<String> provinceCodeArr;

    /**
     * 站点id集合
     */
    private List<String> siteIdArr;

    /**
     * 业务类型字典编码
     */
    private String businessTypeDictClassCode;

    /**
     * 站点类型字典编码
     */
    private String siteTypeDictClassCode;

    /**
     * 是否自动站，1-是 0-否
     */
    private String isAutosite;

    /**
     * 所属包间id
     */
    private String packageId;

    /**
     * 所属包间id数组
     */
    private List<String> packageIdArr;

    /**
     * 是否有自动站(水独有,Y/N)
     */
    private String hasAutomaticStation;

    public List<String> getCityCodeArr() {
        return cityCodeArr;
    }

    public void setCityCodeArr(List<String> cityCodeArr) {
        this.cityCodeArr = cityCodeArr;
    }

    public List<String> getProvinceCodeArr() {
        return provinceCodeArr;
    }

    public void setProvinceCodeArr(List<String> provinceCodeArr) {
        this.provinceCodeArr = provinceCodeArr;
    }

    public List<String> getSiteIdArr() {
        return siteIdArr;
    }

    public void setSiteIdArr(List<String> siteIdArr) {
        this.siteIdArr = siteIdArr;
    }

    public String getBusinessTypeDictClassCode() {
        return businessTypeDictClassCode;
    }

    public void setBusinessTypeDictClassCode(String businessTypeDictClassCode) {
        this.businessTypeDictClassCode = businessTypeDictClassCode;
    }

    public String getSiteTypeDictClassCode() {
        return siteTypeDictClassCode;
    }

    public void setSiteTypeDictClassCode(String siteTypeDictClassCode) {
        this.siteTypeDictClassCode = siteTypeDictClassCode;
    }

    public String getIsAutosite() {
        return isAutosite;
    }

    public void setIsAutosite(String isAutosite) {
        this.isAutosite = isAutosite;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public List<String> getPackageIdArr() {
        return packageIdArr;
    }

    public void setPackageIdArr(List<String> packageIdArr) {
        this.packageIdArr = packageIdArr;
    }

    public String getHasAutomaticStation() {
        return hasAutomaticStation;
    }

    public void setHasAutomaticStation(String hasAutomaticStation) {
        this.hasAutomaticStation = hasAutomaticStation;
    }
}
