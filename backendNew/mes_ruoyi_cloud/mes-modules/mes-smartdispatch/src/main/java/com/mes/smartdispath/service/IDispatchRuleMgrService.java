package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.ScheduAlgorithmInfo;
import com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord;
import com.mes.smartdispath.domain.ScheduAlgruleConfig;
import com.mes.smartdispath.domain.dto.ScheduAlgruleConfigQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduAlgorithmInvokeRecordVO;
import com.mes.smartdispath.domain.vo.ScheduAlgruleConfigVO;

/**
 * 调度规则管理Service
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface IDispatchRuleMgrService {
    /**
     * 算法列表查询
     *
     * @param scheduAlgorithmInfo
     * @return 算法列表
     */
    public List<ScheduAlgorithmInfo> qryAlgorithmList(ScheduAlgorithmInfo scheduAlgorithmInfo);

    /**
     * 算法新增/编辑
     *
     * @param scheduAlgorithmInfo
     * @return
     */
    public int saveAlgorithmInfo(ScheduAlgorithmInfo scheduAlgorithmInfo);

    /**
     * 算法删除
     *
     * @param id
     * @return
     */
    public int delAlgorithmInfo(String id);

    /**
     * 算法调用情况查询
     *
     * @param scheduAlgorithmInvokeRecord
     * @return 算法调用情况列表
     */
    public List<ScheduAlgorithmInvokeRecordVO> qryAlgorithmCallList(
        ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord);

    /**
     * 算法调用详细列表查询
     *
     * @param scheduAlgorithmInvokeRecord
     * @return
     */
    public List<ScheduAlgorithmInvokeRecord> qryAlgorithmCallDetailList(
        ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord);

    /**
     * 算法规则库查询
     *
     * @param queryDTO
     * @return
     */
    public List<ScheduAlgruleConfigVO> qryAlgorithmLibrary(ScheduAlgruleConfigQueryDTO queryDTO);

    /**
     * 算法规则库新增/编辑
     *
     * @param scheduAlgruleConfig
     * @return
     */
    public int saveAlgorithmLibrary(ScheduAlgruleConfig scheduAlgruleConfig);

    /**
     * 算法规则删除
     *
     * @param id
     * @return
     */
    public int delAlgorithmLibrary(String id);
}
