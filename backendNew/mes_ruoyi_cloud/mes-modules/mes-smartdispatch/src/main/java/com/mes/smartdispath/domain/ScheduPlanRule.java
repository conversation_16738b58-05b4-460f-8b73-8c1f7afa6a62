package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 计划规则对象 tb_schedu_plan_rule
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduPlanRule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 规则编码，全局唯一标识 */
    @Excel(name = "规则编码，全局唯一标识")
    private String ruleCode;

    /** 规则名称 */
    @Excel(name = "规则名称")
    private String ruleName;

    /** 业务分类（全部、水、气）,一级分类 */
    @Excel(name = "业务分类", readConverterExp = "全=部、水、气")
    private String businessType;

    /** 站点类型（标准字典如：监测断面、水自动站等）二级分类 */
    @Excel(name = "站点类型", readConverterExp = "标=准字典如：监测断面、水自动站等")
    private String siteType;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String siteName;

    /** 站点ID */
    @Excel(name = "站点ID")
    private String siteId;

    /** 省名称（如：北京市） */
    @Excel(name = "省名称", readConverterExp = "如=：北京市")
    private String provinceName;

    /** 省行政区域代码 */
    @Excel(name = "省行政区域代码")
    private String provinceCode;

    /** 市名称（如：上海市） */
    @Excel(name = "市名称", readConverterExp = "如=：上海市")
    private String cityName;

    /** 市行政区域代码 */
    @Excel(name = "市行政区域代码")
    private String cityCode;

    /** 监测活动大类（三级分类） */
    @Excel(name = "监测活动大类", readConverterExp = "三=级分类")
    private String activityType;

    /** 监测活动子类（四级分类） */
    @Excel(name = "监测活动子类", readConverterExp = "四=级分类")
    private String activitySubtype;

    /** 是否有伴生计划 0-无，1-有 */
    @Excel(name = "是否有伴生计划 0-无，1-有")
    private String isAssoplan;

    /** 计划时间表达式 */
    @Excel(name = "计划时间表达式")
    private String planCronRule;

    /** 计划周期，如T+1天、T+1月、T+1年 */
    @Excel(name = "计划周期，如T+1天、T+1月、T+1年")
    private String planCycle;

    /** 规则开始时间，生效时间，即有效期 */
    @Excel(name = "规则开始时间，生效时间，即有效期")
    private String ruleStartTime;

    /** 规则结束时间，生效时间，即有效期 */
    @Excel(name = "规则结束时间，生效时间，即有效期")
    private String ruleEndTime;

    /** 规则启用状态：0-禁用，1-启用 */
    @Excel(name = "规则启用状态：0-禁用，1-启用")
    private String ruleStatus;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 审批状态: approved-已批准, rejected-已驳回, pending-待审批 */
    @Excel(name = "审批状态: approved-已批准, rejected-已驳回, pending-待审批")
    private String approvalStatus;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType;
    }

    public String getSiteType() {
        return siteType;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivitySubtype(String activitySubtype) {
        this.activitySubtype = activitySubtype;
    }

    public String getActivitySubtype() {
        return activitySubtype;
    }

    public void setIsAssoplan(String isAssoplan) {
        this.isAssoplan = isAssoplan;
    }

    public String getIsAssoplan() {
        return isAssoplan;
    }

    public void setPlanCronRule(String planCronRule) {
        this.planCronRule = planCronRule;
    }

    public String getPlanCronRule() {
        return planCronRule;
    }

    public void setPlanCycle(String planCycle) {
        this.planCycle = planCycle;
    }

    public String getPlanCycle() {
        return planCycle;
    }

    public void setRuleStartTime(String ruleStartTime) {
        this.ruleStartTime = ruleStartTime;
    }

    public String getRuleStartTime() {
        return ruleStartTime;
    }

    public void setRuleEndTime(String ruleEndTime) {
        this.ruleEndTime = ruleEndTime;
    }

    public String getRuleEndTime() {
        return ruleEndTime;
    }

    public void setRuleStatus(String ruleStatus) {
        this.ruleStatus = ruleStatus;
    }

    public String getRuleStatus() {
        return ruleStatus;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("ruleCode", getRuleCode()).append("ruleName", getRuleName())
            .append("businessType", getBusinessType()).append("siteType", getSiteType())
            .append("siteName", getSiteName()).append("siteId", getSiteId()).append("provinceName", getProvinceName())
            .append("provinceCode", getProvinceCode()).append("cityName", getCityName())
            .append("cityCode", getCityCode()).append("activityType", getActivityType())
            .append("activitySubtype", getActivitySubtype()).append("isAssoplan", getIsAssoplan())
            .append("planCronRule", getPlanCronRule()).append("planCycle", getPlanCycle())
            .append("ruleStartTime", getRuleStartTime()).append("ruleEndTime", getRuleEndTime())
            .append("ruleStatus", getRuleStatus()).append("createBy", getCreateBy())
            .append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("status", getStatus()).append("tenantId", getTenantId())
            .append("approvalStatus", getApprovalStatus()).toString();
    }
}
