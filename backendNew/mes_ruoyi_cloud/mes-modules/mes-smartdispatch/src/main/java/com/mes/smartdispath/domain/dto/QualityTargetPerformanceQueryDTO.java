package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.domain.ScheduMonitorQualityTarget;

/**
 * 质量目标完成情况查询DTO
 *
 * @Author: li.haoyang @Date： 2025/7/19
 */
public class QualityTargetPerformanceQueryDTO extends ScheduMonitorQualityTarget {
    /**
     * 运维公司
     */
    private String maintainUnitCode;

    private List<String> maintainUnitCodeArr;

    private List<String> provinceCodeArr;

    public String getMaintainUnitCode() {
        return maintainUnitCode;
    }

    public void setMaintainUnitCode(String maintainUnitCode) {
        this.maintainUnitCode = maintainUnitCode;
    }

    public List<String> getMaintainUnitCodeArr() {
        return maintainUnitCodeArr;
    }

    public void setMaintainUnitCodeArr(List<String> maintainUnitCodeArr) {
        this.maintainUnitCodeArr = maintainUnitCodeArr;
    }

    public List<String> getProvinceCodeArr() {
        return provinceCodeArr;
    }

    public void setProvinceCodeArr(List<String> provinceCodeArr) {
        this.provinceCodeArr = provinceCodeArr;
    }
}
