package com.mes.smartdispath.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.utils.poi.ExcelUtil;
import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.smartdispath.domain.PubLogCallbackAccess;
import com.mes.smartdispath.service.IPubLogCallbackAccessService;

/**
 * 接口访问日志记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/access")
public class PubLogCallbackAccessController extends BaseController {
    @Autowired
    private IPubLogCallbackAccessService pubLogCallbackAccessService;

    /**
     * 查询接口访问日志记录列表 @RequiresPermissions("smartdispath:access:list")
     */
    @GetMapping("/list")
    public TableDataInfo list(PubLogCallbackAccess pubLogCallbackAccess) {
        startPage();
        List<PubLogCallbackAccess> list = pubLogCallbackAccessService
            .selectPubLogCallbackAccessList(pubLogCallbackAccess);
        return getDataTable(list);
    }

    /**
     * 导出接口访问日志记录列表 @RequiresPermissions("smartdispath:access:export")
     * 
     * @Log(title = "接口访问日志记录", businessType = BusinessType.EXPORT)
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, PubLogCallbackAccess pubLogCallbackAccess) {
        List<PubLogCallbackAccess> list = pubLogCallbackAccessService
            .selectPubLogCallbackAccessList(pubLogCallbackAccess);
        ExcelUtil<PubLogCallbackAccess> util = new ExcelUtil<PubLogCallbackAccess>(PubLogCallbackAccess.class);
        util.exportExcel(response, list, "接口访问日志记录数据", false, false);
    }

    /**
     * 获取接口访问日志记录详细信息 @RequiresPermissions("smartdispath:access:query")
     */
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") String logId) {
        return success(pubLogCallbackAccessService.selectPubLogCallbackAccessByLogId(logId));
    }

    /**
     * 新增接口访问日志记录 @RequiresPermissions("smartdispath:access:add")
     */
    // @Log(title = "接口访问日志记录", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody PubLogCallbackAccess pubLogCallbackAccess, HttpServletRequest request) {
        return toAjax(pubLogCallbackAccessService.insertPubLogCallbackAccess(pubLogCallbackAccess, request));
    }
}
