package com.mes.smartdispath.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.smartdispath.domain.SysUserCus;
import com.mes.smartdispath.service.ISysUserServiceCus;
import com.mes.system.api.domain.SysUser;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/userTest")
public class SysUserCusController extends BaseController {
    @Autowired
    private ISysUserServiceCus userServiceCus;

    /**
     * 获取用户列表
     */
    @GetMapping("/listTest")
    public TableDataInfo list(SysUserCus user) {
        startPage();
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        List<SysUserCus> list = userServiceCus.selectUserList(user);
        return getDataTable(list);
    }

}
