package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduMonitorQualityTarget;
import com.mes.smartdispath.domain.dto.QualityTargetPerformanceQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorQualityTargetQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQualityTargetVO;

/**
 * 质量目标管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduMonitorQualityTargetMapper {
    /**
     * 查询符合条件的质量目标数量
     *
     * @param queryDto
     * @return 数量
     */
    public int selectScheduMonitorQualityTargetCount(ScheduMonitorQualityTargetQueryDTO queryDto);

    /**
     * 查询质量目标管理
     * 
     * @param id 质量目标管理主键
     * @return 质量目标管理
     */
    public ScheduMonitorQualityTarget selectScheduMonitorQualityTargetById(String id);

    /**
     * 按年度和监测项汇总-查询质量目标管理列表
     *
     * @param queryDTO 质量目标管理
     * @return 质量目标管理集合
     */
    public List<ScheduMonitorQualityTargetVO> selectStatYearCollectMonitorQualityTargetList(
        QualityTargetPerformanceQueryDTO queryDTO);

    /**
     * 查询质量目标管理列表
     * 
     * @param scheduMonitorQualityTarget 质量目标管理
     * @return 质量目标管理集合
     */
    public List<ScheduMonitorQualityTargetVO> selectScheduMonitorQualityTargetList(
        ScheduMonitorQualityTargetQueryDTO scheduMonitorQualityTarget);

    /**
     * 批量新增质量目标
     *
     * @param targetList 质量目标数组
     * @return 结果
     */
    public int batchInsertScheduMonitorQualityTarget(List<ScheduMonitorQualityTarget> targetList);

    /**
     * 新增质量目标管理
     * 
     * @param scheduMonitorQualityTarget 质量目标管理
     * @return 结果
     */
    public int insertScheduMonitorQualityTarget(ScheduMonitorQualityTarget scheduMonitorQualityTarget);

    /**
     * 修改质量目标管理
     * 
     * @param scheduMonitorQualityTarget 质量目标管理
     * @return 结果
     */
    public int updateScheduMonitorQualityTarget(ScheduMonitorQualityTarget scheduMonitorQualityTarget);

    /**
     * 删除质量目标管理
     * 
     * @param id 质量目标管理主键
     * @return 结果
     */
    public int deleteScheduMonitorQualityTargetById(String id);

    /**
     * 批量删除质量目标管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduMonitorQualityTargetByIds(String[] ids);
}
