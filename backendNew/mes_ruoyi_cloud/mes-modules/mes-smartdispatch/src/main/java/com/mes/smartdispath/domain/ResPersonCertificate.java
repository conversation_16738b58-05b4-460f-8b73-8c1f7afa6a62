package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 人员证书信息对象 tb_res_person_certificate
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public class ResPersonCertificate extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 证书ID（自增主键） */
    private String certificateId;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 相关证书名称 */
    @Excel(name = "相关证书名称")
    private String certificateName;

    /** 证书编号（唯一） */
    @Excel(name = "证书编号", readConverterExp = "唯=一")
    private String certificateNumber;

    /** 发证机构 */
    @Excel(name = "发证机构")
    private String issuingAuthority;

    /** 证书有效期（YYYY-MM-DD） */
    @Excel(name = "证书有效期", readConverterExp = "Y=YYY-MM-DD")
    private String expirationDate;

    /** 证书扫描件URL */
    @Excel(name = "证书扫描件URL")
    private String certificateScanUrl;

    /** 证书状态（1正常、2失效） */
    @Excel(name = "证书状态", readConverterExp = "1=正常、2失效")
    private String certificateStatus;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatedBy;

    public void setCertificateId(String certificateId) {
        this.certificateId = certificateId;
    }

    public String getCertificateId() {
        return certificateId;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setCertificateName(String certificateName) {
        this.certificateName = certificateName;
    }

    public String getCertificateName() {
        return certificateName;
    }

    public void setCertificateNumber(String certificateNumber) {
        this.certificateNumber = certificateNumber;
    }

    public String getCertificateNumber() {
        return certificateNumber;
    }

    public void setIssuingAuthority(String issuingAuthority) {
        this.issuingAuthority = issuingAuthority;
    }

    public String getIssuingAuthority() {
        return issuingAuthority;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setCertificateScanUrl(String certificateScanUrl) {
        this.certificateScanUrl = certificateScanUrl;
    }

    public String getCertificateScanUrl() {
        return certificateScanUrl;
    }

    public void setCertificateStatus(String certificateStatus) {
        this.certificateStatus = certificateStatus;
    }

    public String getCertificateStatus() {
        return certificateStatus;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("certificateId", getCertificateId())
            .append("idCard", getIdCard()).append("certificateName", getCertificateName())
            .append("certificateNumber", getCertificateNumber()).append("issuingAuthority", getIssuingAuthority())
            .append("expirationDate", getExpirationDate()).append("certificateScanUrl", getCertificateScanUrl())
            .append("certificateStatus", getCertificateStatus()).append("createdBy", getCreatedBy())
            .append("createTime", getCreateTime()).append("updatedBy", getUpdatedBy())
            .append("updateTime", getUpdateTime()).toString();
    }
}
