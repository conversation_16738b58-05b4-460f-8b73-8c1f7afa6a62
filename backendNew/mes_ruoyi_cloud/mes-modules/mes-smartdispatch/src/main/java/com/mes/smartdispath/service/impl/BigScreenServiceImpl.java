package com.mes.smartdispath.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mes.common.core.utils.StringUtils;
import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.ScheduMonitorQuantityTarget;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.ChartBasicVO;
import com.mes.smartdispath.domain.vo.CompletePlanStaticVO;
import com.mes.smartdispath.domain.vo.DispatchEfficiencyStaticVO;
import com.mes.smartdispath.domain.vo.MaintainPersonCountVO;
import com.mes.smartdispath.domain.vo.PlanSourceStaticVO;
import com.mes.smartdispath.domain.vo.QuantityTargetCollectPerformanceVO;
import com.mes.smartdispath.domain.vo.SiteTaskListVO;
import com.mes.smartdispath.domain.vo.TaskCountStaticVO;
import com.mes.smartdispath.domain.vo.TaskStaticVO;
import com.mes.smartdispath.enums.PlanSourceEnum;
import com.mes.smartdispath.mapper.ResPersonBasicMapper;
import com.mes.smartdispath.mapper.ScheduMonitorQuantityTargetMapper;
import com.mes.smartdispath.mapper.ScheduPlanInfoMapper;
import com.mes.smartdispath.mapper.ScheduTaskExtendMapper;
import com.mes.smartdispath.mapper.ScheduTaskInfoMapper;
import com.mes.smartdispath.service.IBigScreenService;

/**
 * 大屏 Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
public class BigScreenServiceImpl implements IBigScreenService {
    private static final Logger log = LoggerFactory.getLogger(BigScreenServiceImpl.class);

    @Autowired
    private ScheduTaskInfoMapper scheduTaskInfoMapper;

    @Autowired
    private ScheduMonitorQuantityTargetMapper scheduMonitorQuantityTargetMapper;

    @Autowired
    private ScheduPlanInfoMapper scheduPlanInfoMapper;

    @Autowired
    private ResPersonBasicMapper resPersonBasicMapper;

    @Autowired
    private ScheduTaskExtendMapper scheduTaskExtendMapper;

    /**
     * 汇总的数量目标统计
     *
     * @return
     */
    @Override
    public QuantityTargetCollectPerformanceVO qryCollectQuantityTargetPerformance() {
        QuantityTargetCollectPerformanceVO result = new QuantityTargetCollectPerformanceVO();
        // 今年的年份
        String statYear = String.valueOf(java.time.LocalDate.now().getYear());
        // 本月的第一天和最后一天
        LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
        String firstDayStr = firstDayOfMonth.toString(); // 默认格式为 yyyy-MM-dd
        LocalDate lastDayOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
        String lastDayStr = lastDayOfMonth.toString(); // 默认格式为 yyyy-MM-dd
        // 查询数量目标
        List<ScheduMonitorQuantityTarget> targetList = scheduMonitorQuantityTargetMapper
            .selectBizCollectQuantityTarget(statYear);
        // 查询数量目标的执行情况
        List<CompletePlanStaticVO> annualCompletePlanList = scheduPlanInfoMapper
            .selectCompletePlanCountByTime(statYear + "-01-01", statYear + "-12-31");
        List<CompletePlanStaticVO> curMonthCompletePlanList = scheduPlanInfoMapper
            .selectCompletePlanCountByTime(firstDayStr, lastDayStr);
        // 转成字典
        Map<String, ScheduMonitorQuantityTarget> targetMap = targetList.stream()
            .collect(Collectors.toMap(ScheduMonitorQuantityTarget::getBusinessType, t -> t));
        Map<String, CompletePlanStaticVO> annualCompletePlanMap = annualCompletePlanList.stream()
            .collect(Collectors.toMap(CompletePlanStaticVO::getBusinessType, t -> t));
        Map<String, CompletePlanStaticVO> curMonthCompletePlanMap = curMonthCompletePlanList.stream()
            .collect(Collectors.toMap(CompletePlanStaticVO::getBusinessType, t -> t));
        // 水气业务的年度目标和本月目标
        int annualTotalTarget = 0;
        int curMonthTotalTarget = 0;
        // 水气业务的年度完成量和本月完成量
        int annualTotalPerformance = 0;
        int curMonthTotalPerformance = 0;
        if (null != targetMap.get(SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE)) {
            ScheduMonitorQuantityTarget waterTarget = targetMap.get(SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE);
            int waterTargetValue = Integer.valueOf(waterTarget.getTargetValue());
            result.setAnnualWaterTarget(waterTargetValue);
            result.setCurMonthWaterTarget((int) Math.ceil(waterTargetValue / 12));
            annualTotalTarget = annualTotalTarget + waterTargetValue;
            curMonthTotalTarget = curMonthTotalTarget + result.getCurMonthWaterTarget();
        }
        if (null != targetMap.get(SysDictConstant.BUSINESS_TYPE_AIR_DICT_CODE)) {
            ScheduMonitorQuantityTarget airTarget = targetMap.get(SysDictConstant.BUSINESS_TYPE_AIR_DICT_CODE);
            int airTargetValue = Integer.valueOf(airTarget.getTargetValue());
            result.setAnnualAirTarget(airTargetValue);
            result.setCurMonthAirTarget((int) Math.ceil(airTargetValue / 12));
            annualTotalTarget = annualTotalTarget + airTargetValue;
            curMonthTotalTarget = curMonthTotalTarget + result.getCurMonthAirTarget();
        }
        if (null != annualCompletePlanMap.get(SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE)) {
            CompletePlanStaticVO waterAnnualCompletePlan = annualCompletePlanMap
                .get(SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE);
            result.setAnnualWaterPerformance(waterAnnualCompletePlan.getCompleteCount());
            annualTotalPerformance = annualTotalPerformance + waterAnnualCompletePlan.getCompleteCount();
        }
        if (null != annualCompletePlanMap.get(SysDictConstant.BUSINESS_TYPE_AIR_DICT_CODE)) {
            CompletePlanStaticVO airAnnualCompletePlan = annualCompletePlanMap
                .get(SysDictConstant.BUSINESS_TYPE_AIR_DICT_CODE);
            result.setAnnualAirPerformance(airAnnualCompletePlan.getCompleteCount());
            annualTotalPerformance = annualTotalPerformance + airAnnualCompletePlan.getCompleteCount();
        }
        if (null != curMonthCompletePlanMap.get(SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE)) {
            CompletePlanStaticVO waterCurMonthCompletePlan = curMonthCompletePlanMap
                .get(SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE);
            result.setCurMonthWaterPerformance(waterCurMonthCompletePlan.getCompleteCount());
            curMonthTotalPerformance = curMonthTotalPerformance + waterCurMonthCompletePlan.getCompleteCount();
        }
        if (null != curMonthCompletePlanMap.get(SysDictConstant.BUSINESS_TYPE_AIR_DICT_CODE)) {
            CompletePlanStaticVO airCurMonthCompletePlan = curMonthCompletePlanMap
                .get(SysDictConstant.BUSINESS_TYPE_AIR_DICT_CODE);
            result.setCurMonthAirPerformance(airCurMonthCompletePlan.getCompleteCount());
            curMonthTotalPerformance = curMonthTotalPerformance + airCurMonthCompletePlan.getCompleteCount();
        }
        result.setAnnualTotalTarget(annualTotalTarget);
        result.setCurMonthTotalTarget(curMonthTotalTarget);
        result.setAnnualTotalPerformance(annualTotalPerformance);
        result.setCurMonthTotalPerformance(curMonthTotalPerformance);
        return result;
    }

    /**
     * 调度计划来源统计
     *
     * @param queryDTO 参数
     * @return 统计信息
     */
    @Override
    public List<PlanSourceStaticVO> qryPlanSourceStatic(ScheduPlanInfoQueryDTO queryDTO) {
        List<PlanSourceStaticVO> result = scheduPlanInfoMapper.selectPlanSourceStatic(queryDTO);
        // 补充中文
        for (PlanSourceStaticVO planSourceStaticVO : result) {
            planSourceStaticVO.setPlanSourceName(PlanSourceEnum.toChineseByCode(planSourceStaticVO.getPlanSource()));
        }
        return result;
    }

    /**
     * 调度任务分布统计，按区域
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<TaskCountStaticVO> qryRegionTaskCountStatic(ScheduTaskInfoQueryDTO queryDTO) {
        return scheduTaskInfoMapper.selectRegionTaskCountStatic(queryDTO);
    }

    /**
     * 调度任务分布统计，按运维单位
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<TaskCountStaticVO> qryMaintainUnitTaskCountStatic(ScheduTaskInfoQueryDTO queryDTO) {
        return scheduTaskInfoMapper.selectMaintainUnitTaskCountStatic(queryDTO);
    }

    /**
     * 今日运维人员数量统计
     *
     * @return
     */
    @Override
    public MaintainPersonCountVO qryTodayMaintainPersonStatic(String businessType) {
        MaintainPersonCountVO result = new MaintainPersonCountVO();
        MaintainPersonCountVO allCount = resPersonBasicMapper.selectMaintainPersonStatic(businessType,
            SysDictConstant.MAINTENANCE_PERSON_TYPE, SysDictConstant.MAINTENANCE_PERSON_STATUS_ON_DUTY,
            SysDictConstant.MAINTENANCE_PERSON_STATUS_LEAVE);
        result.setTotalCount(allCount.getTotalCount());
        // 查询今日在执行任务的运维人员数量
        Integer onDutyCount = scheduTaskExtendMapper.selectTodayTaskExecutorCount(businessType);
        result.setOnDutyCount(onDutyCount);
        result.setIdleCount(allCount.getTotalCount() - onDutyCount);
        return result;
    }

    /**
     * 统计调度任务
     *
     * @param queryDTO 参数
     * @return 任务统计信息
     */
    @Override
    public TaskStaticVO qryCurMonthTaskStatic(TaskStaticQueryDTO queryDTO) {
        if (StringUtils.isEmpty(queryDTO.getStartDate()) || StringUtils.isEmpty(queryDTO.getEndDate())) {
            // 本月的第一天和最后一天
            LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
            String firstDayStr = firstDayOfMonth.toString(); // 默认格式为 yyyy-MM-dd
            LocalDate lastDayOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
            String lastDayStr = lastDayOfMonth.toString(); // 默认格式为 yyyy-MM-dd
            queryDTO.setStartDate(firstDayStr);
            queryDTO.setEndDate(lastDayStr);
        }
        return scheduTaskInfoMapper.selectPeriodTaskStatic(queryDTO);
    }

    /**
     * 当月的调度任务列表
     *
     * @param queryDTO
     * @return 调度任务列表
     */
    @Override
    public List<SiteTaskListVO> qryCurMonthTaskList(TaskStaticQueryDTO queryDTO) {
        if (StringUtils.isEmpty(queryDTO.getStartDate()) || StringUtils.isEmpty(queryDTO.getEndDate())) {
            // 本月的第一天和最后一天
            LocalDate firstDayOfMonth = LocalDate.now().withDayOfMonth(1);
            String firstDayStr = firstDayOfMonth.toString(); // 默认格式为 yyyy-MM-dd
            LocalDate lastDayOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
            String lastDayStr = lastDayOfMonth.toString(); // 默认格式为 yyyy-MM-dd
            queryDTO.setStartDate(firstDayStr);
            queryDTO.setEndDate(lastDayStr);
        }
        // 状态处理
        if (StringUtils.isNotEmpty(queryDTO.getTaskStaus())) {
            List<String> taskStatusArr = Arrays.asList(StringUtils.split(queryDTO.getTaskStaus(), ","));
            queryDTO.setTaskStausArr(taskStatusArr);
        }
        return scheduTaskInfoMapper.selectCollectTaskList(queryDTO);
    }

    /**
     * 调度效率统计
     *
     * @return
     */
    @Override
    public DispatchEfficiencyStaticVO qryDispatchEfficiencyStatic() {
        DispatchEfficiencyStaticVO result = new DispatchEfficiencyStaticVO();
        // 生成到上个月为止的月份数组
        List<String> monthArr = getMonthArr();
        // 这个月的月份
        int curMonth = LocalDate.now().getMonthValue();
        String curMonthStr = String.format("%02d", curMonth);
        List<ChartBasicVO> monthPerTaskAvgPlanCountList = scheduTaskInfoMapper.selectMonthPerTaskAvgPlanCount();
        List<ChartBasicVO> monthPersonAvgPlanCountList = scheduTaskInfoMapper.selectMonthPersonAvgPlanCount();
        // 转成字典
        Map<String, ChartBasicVO> monthPerTaskAvgPlanCountMap = monthPerTaskAvgPlanCountList.stream()
            .collect(Collectors.toMap(ChartBasicVO::getDt, Function.identity()));
        Map<String, ChartBasicVO> monthPersonAvgPlanCountMap = monthPersonAvgPlanCountList.stream()
            .collect(Collectors.toMap(ChartBasicVO::getDt, Function.identity()));
        // 组装结果
        List<ChartBasicVO> perTaskAvgPlanCountListResult = new ArrayList<>();
        List<ChartBasicVO> personAvgPlanCountListResult = new ArrayList<>();
        for (String month : monthArr) {
            ChartBasicVO perTaskAvgPlanCount = new ChartBasicVO(month, 0.0);
            if (null != monthPerTaskAvgPlanCountMap.get(month)) {
                perTaskAvgPlanCount.setValue(monthPerTaskAvgPlanCountMap.get(month).getValue());
            }
            ChartBasicVO personAvgPlanCount = new ChartBasicVO(month, 0.0);
            if (null != monthPersonAvgPlanCountMap.get(month)) {
                personAvgPlanCount.setValue(monthPersonAvgPlanCountMap.get(month).getValue());
            }
            perTaskAvgPlanCountListResult.add(perTaskAvgPlanCount);
            personAvgPlanCountListResult.add(personAvgPlanCount);
        }
        result.setTaskAvgPlanCountList(perTaskAvgPlanCountListResult);
        result.setPersonAvgPlanCountList(personAvgPlanCountListResult);
        // 当月的数据
        if (null != monthPerTaskAvgPlanCountMap.get(curMonthStr)) {
            result.setCurMonthTaskAvgPlanCount(monthPerTaskAvgPlanCountMap.get(curMonthStr).getValue());
        }
        if (null != monthPersonAvgPlanCountMap.get(curMonthStr)) {
            result.setCurMonthPersonAvgPlanCount(monthPersonAvgPlanCountMap.get(curMonthStr).getValue());
        }
        result.setAvgTaskAvgPlanCount(getAvgValue(monthPerTaskAvgPlanCountList));
        result.setAvgPersonAvgPlanCount(getAvgValue(monthPersonAvgPlanCountList));

        return result;
    }

    private List<String> getMonthArr() {
        LocalDate now = LocalDate.now();
        int currentMonth = now.getMonthValue();
        List<String> monthList = new ArrayList<>();
        // 从1月到上个月
        for (int i = 1; i < currentMonth; i++) {
            monthList.add(String.format("%02d", i));
        }
        return monthList;
    }

    private Double getAvgValue(List<ChartBasicVO> list) {
        if (list.isEmpty()) {
            return 0.0;
        }
        double avg = list.stream().mapToDouble(ChartBasicVO::getValue).average().orElse(0.0);
        BigDecimal bd = new BigDecimal(avg).setScale(1, BigDecimal.ROUND_HALF_UP);
        double result = bd.doubleValue();
        return result;
    }
}
