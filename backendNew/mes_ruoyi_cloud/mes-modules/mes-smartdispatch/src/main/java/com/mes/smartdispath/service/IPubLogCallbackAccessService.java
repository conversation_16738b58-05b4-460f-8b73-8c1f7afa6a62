package com.mes.smartdispath.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.mes.smartdispath.domain.PubLogCallbackAccess;

/**
 * 接口访问日志记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface IPubLogCallbackAccessService {
    /**
     * 查询接口访问日志记录
     *
     * @param logId 接口访问日志记录主键
     * @return 接口访问日志记录
     */
    public PubLogCallbackAccess selectPubLogCallbackAccessByLogId(String logId);

    /**
     * 查询接口访问日志记录列表
     *
     * @param pubLogCallbackAccess 接口访问日志记录
     * @return 接口访问日志记录集合
     */
    public List<PubLogCallbackAccess> selectPubLogCallbackAccessList(PubLogCallbackAccess pubLogCallbackAccess);

    /**
     * 新增接口访问日志记录
     *
     * @param pubLogCallbackAccess 接口访问日志记录
     * @param request 接口访问日志记录
     * @return 结果
     */
    public int insertPubLogCallbackAccess(PubLogCallbackAccess pubLogCallbackAccess, HttpServletRequest request);
}
