package com.mes.smartdispath.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.mes.common.core.utils.StringUtils;
import com.mes.smartdispath.constant.CommonConstant;
import com.mes.smartdispath.constant.EnumConstant;
import com.mes.smartdispath.constant.PubRegionConstant;
import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.ResAreaInfo;
import com.mes.smartdispath.domain.ResSite;
import com.mes.smartdispath.domain.ResSiteInspectionItem;
import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;
import com.mes.smartdispath.domain.SysDict;
import com.mes.smartdispath.domain.dto.ResSiteQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorActivityInfoQueryDTO;
import com.mes.smartdispath.domain.vo.AreaTreeListResultVO;
import com.mes.smartdispath.domain.vo.MaintenanceUnitVO;
import com.mes.smartdispath.domain.vo.RegionSiteCoutVO;
import com.mes.smartdispath.domain.vo.RegionTreeVO;
import com.mes.smartdispath.domain.vo.ResSiteVO;
import com.mes.smartdispath.domain.vo.SiteTreeVO;
import com.mes.smartdispath.mapper.ResAreaInfoMapper;
import com.mes.smartdispath.mapper.ResDeptMapper;
import com.mes.smartdispath.mapper.ResSiteInspectionItemMapper;
import com.mes.smartdispath.mapper.ResSiteMapper;
import com.mes.smartdispath.mapper.ScheduMonitorActivityInfoMapper;
import com.mes.smartdispath.mapper.SysDictMapper;
import com.mes.smartdispath.service.IGenericMgrService;

/**
 * 通用查询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class GenericMgrServiceImpl implements IGenericMgrService {
    @Autowired
    private ScheduMonitorActivityInfoMapper scheduMonitorActivityInfoMapper;

    @Autowired
    private ResAreaInfoMapper resAreaInfoMapper;

    @Autowired
    private SysDictMapper sysDictMapper;

    @Autowired
    private ResSiteMapper resSiteMapper;

    @Autowired
    private ResSiteInspectionItemMapper resSiteInspectionItemMapper;

    @Autowired
    private ResDeptMapper resDeptMapper;

    /**
     * 断面采样的监测活动大类code
     */
    @Value("${activityType.sectionSampling}")
    private String sectionSamplingActivityType;

    /**
     * 现场监督的监测活动大类code
     */
    @Value("${activityType.sceneSupervision}")
    private String sceneSupervisionActivityType;

    /**
     * 盲样考核的监测活动大类code
     */
    @Value("${activityType.blinkCheck}")
    private String blinkCheckActivityType;

    /**
     * 行政区划tree【无全国节点】
     *
     * @return 行政区划列表
     */
    @Override
    public List<RegionTreeVO> getSimAreaTreeInfo() {
        AreaTreeListResultVO areaTreeListResultVO = getAreaTreeList();
        return areaTreeListResultVO.getTreeList();
    }

    /**
     * 获取行政区划tree
     *
     * @return 行政区划tree(全国根节点)
     */
    @Override
    public RegionTreeVO getAreaTreeInfo() {
        RegionTreeVO treeVO = new RegionTreeVO();
        AreaTreeListResultVO areaTreeListResultVO = getAreaTreeList();
        treeVO.setAreaCode(CommonConstant.NATIONWIDE_REGION_CODE);
        treeVO.setAreaName(CommonConstant.NATIONWIDE_REGION_NAME);
        treeVO.setNum(areaTreeListResultVO.getCountMap().get(CommonConstant.NATIONWIDE_REGION_CODE));
        treeVO.setChildren(areaTreeListResultVO.getTreeList());
        return treeVO;
    }

    /**
     * 获取监测活动大类
     *
     * @param queryDTO 监测活动类型
     * @return 监测活动类型集合
     */
    @Override
    public List<ScheduMonitorActivityInfo> getActivityParentType(ScheduMonitorActivityInfoQueryDTO queryDTO) {
        if (StringUtils.isNotEmpty(queryDTO.getIsAutosite())) {
            List<String> isAutositeArr = new ArrayList<>();
            isAutositeArr.add(queryDTO.getIsAutosite());
            isAutositeArr.add(EnumConstant.IS_AUTOSITE_ALL);
            queryDTO.setIsAutositeArr(isAutositeArr);
        }
        return scheduMonitorActivityInfoMapper.selectActivityTypeList(queryDTO);
    }

    /**
     * 获取监测活动小类
     *
     * @param queryDTO 监测活动类型
     * @return 监测活动类型集合
     */
    @Override
    public List<ScheduMonitorActivityInfo> getActivityType(ScheduMonitorActivityInfoQueryDTO queryDTO) {
        if (StringUtils.isNotEmpty(queryDTO.getIsAutosite())) {
            List<String> isAutositeArr = new ArrayList<>();
            isAutositeArr.add(queryDTO.getIsAutosite());
            isAutositeArr.add(EnumConstant.IS_AUTOSITE_ALL);
            queryDTO.setIsAutositeArr(isAutositeArr);
        }
        return scheduMonitorActivityInfoMapper.selectActivitySubtypeList(queryDTO);
    }

    /**
     * 获取行政区划-省
     *
     * @param resAreaInfo 行政区划表
     * @return 行政区划表集合
     */
    @Override
    public List<ResAreaInfo> getProvinceInfo(ResAreaInfo resAreaInfo) {
        resAreaInfo.setAreaType(PubRegionConstant.REGION_LEVEL_PROVINCE);
        return resAreaInfoMapper.selectResAreaInfoList(resAreaInfo);
    }

    /**
     * 获取行政区划-市
     *
     * @param resAreaInfo 行政区划表
     * @return 行政区划表集合
     */
    @Override
    public List<ResAreaInfo> getCityInfo(ResAreaInfo resAreaInfo) {
        resAreaInfo.setAreaType(PubRegionConstant.REGION_LEVEL_CITY);
        return resAreaInfoMapper.selectResAreaInfoList(resAreaInfo);
    }

    /**
     * 业务分类字典列表
     *
     * @param sysDict 字典
     * @return 字典集合
     */
    @Override
    public List<SysDict> getBusinessType(SysDict sysDict) {
        sysDict.setClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        return sysDictMapper.selectSysDictList(sysDict);
    }

    /**
     * 站点类型字典列表
     *
     * @param sysDict 字典
     * @return 字典集合
     */
    @Override
    public List<SysDict> getSiteType(SysDict sysDict) {
        sysDict.setClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        return sysDictMapper.selectSysDictList(sysDict);
    }

    /**
     * 计划类别字典列表
     *
     * @param sysDict 字典
     * @return 字典集合
     */
    @Override
    public List<SysDict> getPlanCategory(SysDict sysDict) {
        sysDict.setClassCode(SysDictConstant.PLAN_CATEGORY_CLASS_CODE);
        return sysDictMapper.selectSysDictList(sysDict);
    }

    /**
     * 站点信息
     *
     * @param queryDto 站点
     * @return 站点集合
     */
    @Override
    public List<ResSiteVO> getSiteInfo(ResSiteQueryDTO queryDto) {
        if (SysDictConstant.BUSINESS_TYPE_WATER_DICT_CODE.equals(queryDto.getBusinessType())
            && StringUtils.isNotEmpty(queryDto.getIsAutosite())) {
            queryDto.setHasAutomaticStation(
                EnumConstant.IS_AUTOSITE_YES.equals(queryDto.getIsAutosite()) ? EnumConstant.HAS_AUTOMATIC_STATION_YES
                    : EnumConstant.HAS_AUTOMATIC_STATION_NO);
        }
        queryDto.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        return resSiteMapper.selectResSiteInfoList(queryDto);
    }

    /**
     * 片区站点树
     *
     * @return 片区站点树
     */
    @Override
    public List<SiteTreeVO> getPackageTreeInfo() {
        List<ResSiteVO> siteInfos = resSiteMapper.selectPackageSiteInfos();
        List<SiteTreeVO> tree = new ArrayList<>();
        // 按 packageId 分组
        Map<String, List<ResSiteVO>> packageGroups = siteInfos.stream()
            .collect(Collectors.groupingBy(site -> site.getPackageId()));
        for (Map.Entry<String, List<ResSiteVO>> pkgEntry : packageGroups.entrySet()) {
            String pkgId = pkgEntry.getKey();
            List<ResSiteVO> pkgSites = pkgEntry.getValue();
            String packageName = pkgSites.get(0).getPackageName();
            SiteTreeVO packageNode = new SiteTreeVO(pkgId, pkgId, packageName, "package");
            int packageNodeSiteNum = 0;
            // 按 province 分组
            Map<String, List<ResSiteVO>> provinceGroups = pkgSites.stream()
                .collect(Collectors.groupingBy(site -> site.getProvince()));
            for (Map.Entry<String, List<ResSiteVO>> provinceEntry : provinceGroups.entrySet()) {
                String province = provinceEntry.getKey();
                List<ResSiteVO> provinceSites = provinceEntry.getValue();
                String provinceNodeCode = pkgId + "-" + province;
                String provinceName = provinceSites.get(0).getProvinceName();
                SiteTreeVO provinceNode = new SiteTreeVO(province, provinceNodeCode, provinceName, "province");
                int provinceNodeSiteNum = 0;
                // 按 city 分组
                Map<String, List<ResSiteVO>> cityGroups = provinceSites.stream()
                    .collect(Collectors.groupingBy(site -> site.getCity()));
                for (Map.Entry<String, List<ResSiteVO>> cityEntry : cityGroups.entrySet()) {
                    String city = cityEntry.getKey();
                    List<ResSiteVO> citySites = cityEntry.getValue();
                    String cityNodeCode = provinceNodeCode + "-" + city;
                    String cityName = citySites.get(0).getCityName();
                    SiteTreeVO cityNode = new SiteTreeVO(city, cityNodeCode, cityName, "city");
                    cityNode.num = citySites.size();
                    // 站点列表
                    for (ResSiteVO site : citySites) {
                        String siteNodeCode = cityNodeCode + "-" + site.getSiteId();
                        SiteTreeVO siteNode = new SiteTreeVO(site.getSiteId(), siteNodeCode, site.getSiteName(),
                            "site");
                        cityNode.children.add(siteNode);
                    }
                    provinceNode.children.add(cityNode);
                    provinceNodeSiteNum += citySites.size();
                }
                provinceNode.num = provinceNodeSiteNum;
                packageNode.children.add(provinceNode);
                packageNodeSiteNum += provinceNodeSiteNum;
            }
            packageNode.num = packageNodeSiteNum;
            tree.add(packageNode);
        }
        return tree;
    }

    /**
     * 行政区划站点树
     *
     * @return 行政区划站点树
     */
    @Override
    public SiteTreeVO getRegionTreeInfo() {
        List<ResSiteVO> siteInfos = resSiteMapper.selectRegionSiteInfos();
        // 1. 按省分组
        Map<String, List<ResSiteVO>> provinceGroups = siteInfos.stream()
            .collect(Collectors.groupingBy(site -> site.getProvince()));
        List<SiteTreeVO> provinceNodes = new ArrayList<>();
        for (Map.Entry<String, List<ResSiteVO>> provinceEntry : provinceGroups.entrySet()) {
            String province = provinceEntry.getKey();
            List<ResSiteVO> provinceSites = provinceEntry.getValue();
            String provinceName = provinceSites.get(0).getProvinceName();
            String provinceNodeCode = province;
            SiteTreeVO provinceNode = new SiteTreeVO(province, provinceNodeCode, provinceName, "province");
            // 2. 按市分组
            Map<String, List<ResSiteVO>> cityGroups = provinceSites.stream()
                .collect(Collectors.groupingBy(site -> site.getCity()));
            int provinceSiteNum = 0;
            for (Map.Entry<String, List<ResSiteVO>> cityEntry : cityGroups.entrySet()) {
                String city = cityEntry.getKey();
                List<ResSiteVO> citySites = cityEntry.getValue();
                String cityName = citySites.get(0).getCityName();
                String cityNodeCode = provinceNodeCode + "-" + city;
                SiteTreeVO cityNode = new SiteTreeVO(city, cityNodeCode, cityName, "city");
                cityNode.num = citySites.size();
                // 3. 站点列表
                for (ResSiteVO site : citySites) {
                    String siteId = site.getSiteId();
                    String siteName = site.getSiteName();
                    String siteNodeCode = cityNodeCode + "-" + siteId;
                    SiteTreeVO siteNode = new SiteTreeVO(siteId, siteNodeCode, siteName, "site");
                    siteNode.setSiteCode(site.getSiteCode());
                    cityNode.children.add(siteNode);
                }
                provinceNode.children.add(cityNode);
                provinceSiteNum += citySites.size();
            }
            provinceNode.num = provinceSiteNum;
            provinceNodes.add(provinceNode);
        }
        // 4. 构建根节点
        SiteTreeVO root = new SiteTreeVO(CommonConstant.NATIONWIDE_REGION_CODE, CommonConstant.NATIONWIDE_REGION_CODE,
            CommonConstant.NATIONWIDE_REGION_NAME, "root");
        root.num = siteInfos.size();
        root.children = provinceNodes;
        return root;
    }

    /**
     * 片区省份树
     *
     * @return 片区省份树
     */
    @Override
    public List<SiteTreeVO> getPackageProvinceTreeInfo() {
        List<ResSiteVO> siteInfos = resSiteMapper.selectPackageSiteInfos();
        List<SiteTreeVO> tree = new ArrayList<>();
        // 按 packageId 分组
        Map<String, List<ResSiteVO>> packageGroups = siteInfos.stream()
            .collect(Collectors.groupingBy(site -> site.getPackageId()));
        for (Map.Entry<String, List<ResSiteVO>> pkgEntry : packageGroups.entrySet()) {
            String pkgId = pkgEntry.getKey();
            List<ResSiteVO> pkgSites = pkgEntry.getValue();
            String packageName = pkgSites.get(0).getPackageName();
            SiteTreeVO packageNode = new SiteTreeVO(pkgId, pkgId, packageName, "package");
            // 按 province 分组
            Map<String, List<ResSiteVO>> provinceGroups = pkgSites.stream()
                .collect(Collectors.groupingBy(site -> site.getProvince()));
            for (Map.Entry<String, List<ResSiteVO>> provinceEntry : provinceGroups.entrySet()) {
                String province = provinceEntry.getKey();
                List<ResSiteVO> provinceSites = provinceEntry.getValue();
                String provinceNodeCode = pkgId + "-" + province;
                String provinceName = provinceSites.get(0).getProvinceName();
                SiteTreeVO provinceNode = new SiteTreeVO(province, provinceNodeCode, provinceName, "province");
                packageNode.children.add(provinceNode);
            }
            tree.add(packageNode);
        }
        return tree;
    }

    /**
     * 行政区划省份树
     *
     * @return 行政区划省份树
     */
    @Override
    public SiteTreeVO getRegionProvinceTreeInfo() {
        List<ResSiteVO> siteInfos = resSiteMapper.selectRegionSiteInfos();
        // 1. 按省分组
        Map<String, List<ResSiteVO>> provinceGroups = siteInfos.stream()
            .collect(Collectors.groupingBy(site -> site.getProvince()));
        List<SiteTreeVO> provinceNodes = new ArrayList<>();
        for (Map.Entry<String, List<ResSiteVO>> provinceEntry : provinceGroups.entrySet()) {
            String province = provinceEntry.getKey();
            List<ResSiteVO> provinceSites = provinceEntry.getValue();
            String provinceName = provinceSites.get(0).getProvinceName();
            String provinceNodeCode = province;
            SiteTreeVO provinceNode = new SiteTreeVO(province, provinceNodeCode, provinceName, "province");
            provinceNodes.add(provinceNode);
        }
        // 4. 构建根节点
        SiteTreeVO root = new SiteTreeVO(CommonConstant.NATIONWIDE_REGION_CODE, CommonConstant.NATIONWIDE_REGION_CODE,
            CommonConstant.NATIONWIDE_REGION_NAME, "root");
        root.children = provinceNodes;
        return root;
    }

    /**
     * 行政区划运维公司树
     *
     * @return 行政区划运维公司树
     */
    @Override
    public SiteTreeVO getRegionMaintainUnitTreeInfo() {
        List<ResSiteVO> siteInfos = resSiteMapper.selectRegionSiteMaintainUnitInfos();
        // 1. 按省分组
        Map<String, List<ResSiteVO>> provinceGroups = siteInfos.stream()
            .collect(Collectors.groupingBy(site -> site.getProvince()));
        List<SiteTreeVO> provinceNodes = new ArrayList<>();
        for (Map.Entry<String, List<ResSiteVO>> provinceEntry : provinceGroups.entrySet()) {
            String province = provinceEntry.getKey();
            List<ResSiteVO> provinceSites = provinceEntry.getValue();
            String provinceName = provinceSites.get(0).getProvinceName();
            String provinceNodeCode = province;
            SiteTreeVO provinceNode = new SiteTreeVO(province, provinceNodeCode, provinceName, "province");
            // 2. 按市分组
            Map<String, List<ResSiteVO>> cityGroups = provinceSites.stream()
                .collect(Collectors.groupingBy(site -> site.getCity()));
            int provinceSiteNum = 0;
            for (Map.Entry<String, List<ResSiteVO>> cityEntry : cityGroups.entrySet()) {
                String city = cityEntry.getKey();
                List<ResSiteVO> citySites = cityEntry.getValue();
                String cityName = citySites.get(0).getCityName();
                String cityNodeCode = provinceNodeCode + "-" + city;
                SiteTreeVO cityNode = new SiteTreeVO(city, cityNodeCode, cityName, "city");
                cityNode.num = citySites.size();
                // 3. 运维公司列表
                for (ResSiteVO site : citySites) {
                    String unitId = site.getOperationUnit();
                    String unitName = site.getOperationUnitName();
                    String siteNodeCode = cityNodeCode + "-" + unitId;
                    SiteTreeVO siteNode = new SiteTreeVO(unitId, siteNodeCode, unitName, "unit");
                    cityNode.children.add(siteNode);
                }
                provinceNode.children.add(cityNode);
                provinceSiteNum += citySites.size();
            }
            provinceNode.num = provinceSiteNum;
            provinceNodes.add(provinceNode);
        }
        // 4. 构建根节点
        SiteTreeVO root = new SiteTreeVO(CommonConstant.NATIONWIDE_REGION_CODE, CommonConstant.NATIONWIDE_REGION_CODE,
            CommonConstant.NATIONWIDE_REGION_NAME, "root");
        root.num = siteInfos.size();
        root.children = provinceNodes;
        return root;
    }

    /**
     * 运维公司查询
     *
     * @param businessType
     * @return
     */
    @Override
    public List<MaintenanceUnitVO> getMaintainUnitInfo(String businessType) {
        return resDeptMapper.selectMaintenanceUnitList(businessType, SysDictConstant.MAINTENANCE_UNIT_ROLE_CODE);
    }

    /**
     * 站点是否是高风险
     *
     * @param siteId
     * @return
     */
    @Override
    public Boolean getSiteHighRisk(String siteId) {
        boolean highRisk = false;
        ResSite siteInfo = resSiteMapper.selectResSiteById(siteId);
        ResSiteInspectionItem inspectionItem = resSiteInspectionItemMapper.selectInspectionItemBySiteId(siteId);
        if (null != siteInfo && null != inspectionItem
            && Integer.parseInt(inspectionItem.getRiskLevel()) < Integer.parseInt(siteInfo.getWaterQualityTarget())) {
            highRisk = true;
        }
        return highRisk;
    }

    /**
     * 获取有效的伴生活动列表
     *
     * @param activityType
     * @param activitySubtype
     * @return
     */
    @Override
    public List<ScheduMonitorActivityInfo> getEffectiveActivityList(String siteId, String activityType,
        String activitySubtype) {
        ScheduMonitorActivityInfo activityInfo = scheduMonitorActivityInfoMapper
            .selectScheduMonitorActivityInfoByCode(activityType, activitySubtype);
        return getEffectiveActivityListByActivityInfo(siteId, activityInfo);
    }

    /**
     * 获取有效的伴生活动列表(入参活动信息)
     *
     * @param siteId
     * @param activityInfo
     * @return
     */
    @Override
    public List<ScheduMonitorActivityInfo> getEffectiveActivityListByActivityInfo(String siteId,
        ScheduMonitorActivityInfo activityInfo) {
        List<ScheduMonitorActivityInfo> result = new ArrayList<>();
        // 判断活动是否需要生成伴生计划
        if (null != activityInfo && CommonConstant.IS_ASSO_ACTIVITY_FLAG_1.equals(activityInfo.getIsAssoactivity())) {
            // 查询关联的伴生活动
            List<String> activityCodeArr = Arrays.asList(StringUtils.split(activityInfo.getAssoactivityCode(), ","));
            List<ScheduMonitorActivityInfo> assoactivityList = scheduMonitorActivityInfoMapper
                .selectScheduMonitorActivityInfoList(new ScheduMonitorActivityInfoQueryDTO() {
                    {
                        setActivityCodeArr(activityCodeArr);
                    }
                });
            // 站点是否是高风险
            boolean highRisk = getSiteHighRisk(siteId);
            // 非高风险断面的断面采样活动去掉现场监督和盲样考核伴生活动
            if (activityInfo.getActivityTypeCode().equals(sectionSamplingActivityType) && !highRisk) {
                result = assoactivityList.stream()
                    .filter(assoactivity -> !sceneSupervisionActivityType.equals(assoactivity.getActivityTypeCode())
                        && !blinkCheckActivityType.equals(assoactivity.getActivityTypeCode()))
                    .collect(Collectors.toList());
            }
            else {
                return assoactivityList;
            }
        }
        return result;
    }

    private AreaTreeListResultVO getAreaTreeList() {
        // 获取所有省、市区域数据
        List<ResAreaInfo> regions = resAreaInfoMapper.selectProvinceCityRegionList();
        // 获取区域的站点数量数据
        List<RegionSiteCoutVO> countResult = resSiteMapper
            .selectRegionSiteCoutList(CommonConstant.NATIONWIDE_REGION_CODE);
        // 转成数量字典，key-regionCode value-count
        Map<String, Integer> countMap = countResult.stream().collect(
            Collectors.toMap(RegionSiteCoutVO::getRegionCode, RegionSiteCoutVO::getCount, (count1, count2) -> count2));
        // 转成树字典，key-regionCode value-data
        Map<String, RegionTreeVO> regionMap = new HashMap<>();
        List<RegionTreeVO> formatRegions = new ArrayList<>();
        regions.stream().forEach(region -> {
            RegionTreeVO regionTreeVO = new RegionTreeVO();
            regionTreeVO.setId(region.getId());
            regionTreeVO.setAreaCode(region.getAreaCode());
            regionTreeVO.setParentCode(region.getParentCode());
            regionTreeVO.setAreaName(region.getAreaName());
            regionTreeVO.setAreaType(region.getAreaType());
            if (countMap.containsKey(region.getAreaCode())) {
                Integer count = countMap.get(region.getAreaCode());
                regionTreeVO.setNum(count);
            }
            else {
                regionTreeVO.setNum(0);
            }
            regionMap.put(region.getAreaCode(), regionTreeVO);
            formatRegions.add(regionTreeVO);
        });
        List<RegionTreeVO> treeList = new ArrayList<>();
        for (RegionTreeVO region : formatRegions) {
            if (null != region.getParentCode() && !region.getParentCode().isEmpty()) {
                RegionTreeVO parent = regionMap.get(region.getParentCode());
                if (parent != null) {
                    parent.getChildren().add(region);
                }
            }
            else {
                treeList.add(region);
            }
        }
        AreaTreeListResultVO result = new AreaTreeListResultVO();
        result.setTreeList(treeList);
        result.setCountMap(countMap);
        return result;
    }
}
