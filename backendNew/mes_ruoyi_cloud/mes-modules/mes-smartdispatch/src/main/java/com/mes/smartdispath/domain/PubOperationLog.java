package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 操作日志对象 tb_pub_operation_log
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class PubOperationLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    private String id;

    /** 操作人 */
    @Excel(name = "操作人")
    private String name;

    /** ip */
    @Excel(name = "ip")
    private String ip;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 模块 */
    @Excel(name = "模块")
    private String module;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 状态：A - 有效、X - 无效 */
    @Excel(name = "状态：A - 有效、X - 无效")
    private String state;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIp() {
        return ip;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getModule() {
        return module;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState() {
        return state;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("name", getName())
            .append("ip", getIp()).append("type", getType()).append("module", getModule())
            .append("content", getContent()).append("state", getState()).append("createTime", getCreateTime())
            .append("createBy", getCreateBy()).append("updateTime", getUpdateTime()).append("updateBy", getUpdateBy())
            .toString();
    }
}
