package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.SysAttachInfo;

/**
 * 通用附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface SysAttachInfoMapper {
    /**
     * 查询通用附件
     * 
     * @param id 通用附件主键
     * @return 通用附件
     */
    public SysAttachInfo selectSysAttachInfoById(String id);

    /**
     * 查询通用附件列表
     * 
     * @param sysAttachInfo 通用附件
     * @return 通用附件集合
     */
    public List<SysAttachInfo> selectSysAttachInfoList(SysAttachInfo sysAttachInfo);

    /**
     * 新增通用附件
     * 
     * @param sysAttachInfo 通用附件
     * @return 结果
     */
    public int insertSysAttachInfo(SysAttachInfo sysAttachInfo);

    /**
     * 修改通用附件
     * 
     * @param sysAttachInfo 通用附件
     * @return 结果
     */
    public int updateSysAttachInfo(SysAttachInfo sysAttachInfo);

    /**
     * 删除通用附件
     * 
     * @param id 通用附件主键
     * @return 结果
     */
    public int deleteSysAttachInfoById(String id);

    /**
     * 批量删除通用附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysAttachInfoByIds(String[] ids);
}
