package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 质量目标管理对象 tb_schedu_monitor_quality_target
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduMonitorQualityTarget extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 省行政区域代码 */
    @Excel(name = "省行政区域代码")
    private String provinceCode;

    /** 省名称（如：北京市） */
    @Excel(name = "省名称", readConverterExp = "如=：北京市")
    private String provinceName;

    /** 市行政区域代码 */
    @Excel(name = "市行政区域代码")
    private String cityCode;

    /** 市名称（如：上海市） */
    @Excel(name = "市名称", readConverterExp = "如=：上海市")
    private String cityName;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String siteName;

    /** 站点ID */
    @Excel(name = "站点ID")
    private String siteId;

    /** 站点类型 */
    @Excel(name = "站点类型")
    private String siteType;

    /** 业务分类 */
    @Excel(name = "业务分类")
    private String businessType;

    /** 监测参数 */
    @Excel(name = "监测参数")
    private String monitIndex;

    /** 准确度 */
    @Excel(name = "准确度")
    private String accuracy;

    /** 精密度 */
    @Excel(name = "精密度")
    private String precision;

    /** 有效率 */
    @Excel(name = "有效率")
    private String effectivenessRate;

    /** 获取率 */
    @Excel(name = "获取率")
    private String captureRate;

    /** 质控合格率（水） */
    @Excel(name = "质控合格率（水）")
    private String quactrlPassRate;

    /** 统计年度 */
    @Excel(name = "统计年度")
    private String statYear;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType;
    }

    public String getSiteType() {
        return siteType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setMonitIndex(String monitIndex) {
        this.monitIndex = monitIndex;
    }

    public String getMonitIndex() {
        return monitIndex;
    }

    public void setAccuracy(String accuracy) {
        this.accuracy = accuracy;
    }

    public String getAccuracy() {
        return accuracy;
    }

    public void setPrecision(String precision) {
        this.precision = precision;
    }

    public String getPrecision() {
        return precision;
    }

    public void setEffectivenessRate(String effectivenessRate) {
        this.effectivenessRate = effectivenessRate;
    }

    public String getEffectivenessRate() {
        return effectivenessRate;
    }

    public void setCaptureRate(String captureRate) {
        this.captureRate = captureRate;
    }

    public String getCaptureRate() {
        return captureRate;
    }

    public String getQuactrlPassRate() {
        return quactrlPassRate;
    }

    public void setQuactrlPassRate(String quactrlPassRate) {
        this.quactrlPassRate = quactrlPassRate;
    }

    public void setStatYear(String statYear) {
        this.statYear = statYear;
    }

    public String getStatYear() {
        return statYear;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("provinceCode", getProvinceCode()).append("provinceName", getProvinceName())
            .append("cityCode", getCityCode()).append("cityName", getCityName()).append("siteName", getSiteName())
            .append("siteId", getSiteId()).append("siteType", getSiteType()).append("businessType", getBusinessType())
            .append("monitIndex", getMonitIndex()).append("accuracy", getAccuracy()).append("precision", getPrecision())
            .append("effectivenessRate", getEffectivenessRate()).append("captureRate", getCaptureRate())
            .append("quactrlPassRate", getQuactrlPassRate()).append("statYear", getStatYear())
            .append("createBy", getCreateBy()).append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("status", getStatus()).append("tenantId", getTenantId())
            .toString();
    }
}
