package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 接口访问日志记录对象 tb_pub_log_callback_access
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public class PubLogCallbackAccess extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String logId;

    /** 日志名称 */
    @Excel(name = "日志名称")
    private String logName;

    /** 请求IP */
    @Excel(name = "请求IP")
    private String reqIp;

    /** 请求URI包含&连接的参数 */
    @Excel(name = "请求URI包含&连接的参数")
    private String reqUri;

    /** 请求方法 */
    @Excel(name = "请求方法")
    private String reqMethod;

    /** 请求内容 */
    @Excel(name = "请求内容")
    private String reqContent;

    /** 返回内容 */
    @Excel(name = "返回内容")
    private String resContent;

    /** 访问时间 */
    @Excel(name = "访问时间")
    private String accessTime;

    /** 返回时间 */
    @Excel(name = "返回时间")
    private String resTime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String source;

    public void setLogId(String logId) {
        this.logId = logId;
    }

    public String getLogId() {
        return logId;
    }

    public void setLogName(String logName) {
        this.logName = logName;
    }

    public String getLogName() {
        return logName;
    }

    public void setReqIp(String reqIp) {
        this.reqIp = reqIp;
    }

    public String getReqIp() {
        return reqIp;
    }

    public void setReqUri(String reqUri) {
        this.reqUri = reqUri;
    }

    public String getReqUri() {
        return reqUri;
    }

    public void setReqMethod(String reqMethod) {
        this.reqMethod = reqMethod;
    }

    public String getReqMethod() {
        return reqMethod;
    }

    public void setReqContent(String reqContent) {
        this.reqContent = reqContent;
    }

    public String getReqContent() {
        return reqContent;
    }

    public void setResContent(String resContent) {
        this.resContent = resContent;
    }

    public String getResContent() {
        return resContent;
    }

    public void setAccessTime(String accessTime) {
        this.accessTime = accessTime;
    }

    public String getAccessTime() {
        return accessTime;
    }

    public void setResTime(String resTime) {
        this.resTime = resTime;
    }

    public String getResTime() {
        return resTime;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("logId", getLogId())
            .append("logName", getLogName()).append("reqIp", getReqIp()).append("reqUri", getReqUri())
            .append("reqMethod", getReqMethod()).append("reqContent", getReqContent())
            .append("resContent", getResContent()).append("accessTime", getAccessTime()).append("resTime", getResTime())
            .append("createTime", getCreateTime()).append("source", getSource()).toString();
    }
}
