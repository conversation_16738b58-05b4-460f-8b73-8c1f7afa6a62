package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;
import com.mes.smartdispath.domain.dto.ScheduMonitorActivityInfoQueryDTO;

/**
 * 监测活动类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduMonitorActivityInfoMapper {
    /**
     * 查询监测活动类型
     * 
     * @param id 监测活动类型主键
     * @return 监测活动类型
     */
    public ScheduMonitorActivityInfo selectScheduMonitorActivityInfoById(String id);

    /**
     * 根据活动大类和活动小类查询监测活动类型
     *
     * @param activityTypeCode 监测活动大类code
     * @param activitySubtypeCode 监测活动小类code
     * @return 监测活动类型
     */
    public ScheduMonitorActivityInfo selectScheduMonitorActivityInfoByCode(
        @Param("activityTypeCode") String activityTypeCode, @Param("activitySubtypeCode") String activitySubtypeCode);

    /**
     * 获取监测活动大类
     *
     * @param queryDTO 监测活动类型
     * @return 监测活动类型集合
     */
    public List<ScheduMonitorActivityInfo> selectActivityTypeList(ScheduMonitorActivityInfoQueryDTO queryDTO);

    /**
     * 获取监测活动小类
     *
     * @param queryDTO 监测活动类型
     * @return 监测活动类型集合
     */
    public List<ScheduMonitorActivityInfo> selectActivitySubtypeList(ScheduMonitorActivityInfoQueryDTO queryDTO);

    /**
     * 查询监测活动类型列表
     * 
     * @param scheduMonitorActivityInfo 监测活动类型
     * @return 监测活动类型集合
     */
    public List<ScheduMonitorActivityInfo> selectScheduMonitorActivityInfoList(
        ScheduMonitorActivityInfoQueryDTO scheduMonitorActivityInfo);

    /**
     * 新增监测活动类型
     * 
     * @param scheduMonitorActivityInfo 监测活动类型
     * @return 结果
     */
    public int insertScheduMonitorActivityInfo(ScheduMonitorActivityInfo scheduMonitorActivityInfo);

    /**
     * 批量新增监测活动类型
     *
     * @param scheduMonitorActivityInfoList 监测活动类型List
     * @return 结果
     */
    public int batchInsertScheduMonitorActivityInfo(List<ScheduMonitorActivityInfo> scheduMonitorActivityInfoList);

    /**
     * 修改监测活动类型
     * 
     * @param scheduMonitorActivityInfo 监测活动类型
     * @return 结果
     */
    public int updateScheduMonitorActivityInfo(ScheduMonitorActivityInfo scheduMonitorActivityInfo);

    /**
     * 批量修改监测活动类型
     *
     * @param scheduMonitorActivityInfoList 监测活动类型List
     * @return 结果
     */
    public int batchUpdateScheduMonitorActivityInfo(List<ScheduMonitorActivityInfo> scheduMonitorActivityInfoList);

    /**
     * 删除监测活动类型
     * 
     * @param id 监测活动类型主键
     * @return 结果
     */
    public int deleteScheduMonitorActivityInfoById(String id);

    /**
     * 批量删除监测活动类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduMonitorActivityInfoByIds(String[] ids);
}
