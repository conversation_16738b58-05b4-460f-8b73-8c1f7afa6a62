package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ResDevice;
import com.mes.smartdispath.domain.dto.ResDeviceQueryDTO;

/**
 * 设备Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface ResDeviceMapper {
    /**
     * 查询设备
     * 
     * @param id 设备主键
     * @return 设备
     */
    public ResDevice selectResDeviceById(String id);

    /**
     * 查询站点关联的设备列表
     *
     * @param resDevice 设备
     * @return 设备集合
     */
    public List<ResDevice> selectSiteLinkDevice(ResDeviceQueryDTO resDevice);

    /**
     * 查询设备列表
     * 
     * @param resDevice 设备
     * @return 设备集合
     */
    public List<ResDevice> selectResDeviceList(ResDevice resDevice);

    /**
     * 新增设备
     * 
     * @param resDevice 设备
     * @return 结果
     */
    public int insertResDevice(ResDevice resDevice);

    /**
     * 修改设备
     * 
     * @param resDevice 设备
     * @return 结果
     */
    public int updateResDevice(ResDevice resDevice);

    /**
     * 删除设备
     * 
     * @param id 设备主键
     * @return 结果
     */
    public int deleteResDeviceById(String id);

    /**
     * 批量删除设备
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteResDeviceByIds(String[] ids);
}
