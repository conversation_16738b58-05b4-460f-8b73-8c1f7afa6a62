package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ResPersonCertificate;

/**
 * 人员证书信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface ResPersonCertificateMapper {
    /**
     * 查询人员证书信息
     *
     * @param certificateId 人员证书信息主键
     * @return 人员证书信息
     */
    public ResPersonCertificate selectResPersonCertificateByCertificateId(String certificateId);

    /**
     * 查询人员证书信息列表
     *
     * @param resPersonCertificate 人员证书信息
     * @return 人员证书信息集合
     */
    public List<ResPersonCertificate> selectResPersonCertificateList(ResPersonCertificate resPersonCertificate);

    /**
     * 根据人员身份证号查询人员证书信息列表
     * 
     * @param idCardArr
     * @return
     */
    public List<ResPersonCertificate> selectPersonCertificateListByIdCards(@Param("idCardArr") List<String> idCardArr);

    /**
     * 新增人员证书信息
     *
     * @param resPersonCertificate 人员证书信息
     * @return 结果
     */
    public int insertResPersonCertificate(ResPersonCertificate resPersonCertificate);

    /**
     * 批量新增人员证书信息
     *
     * @param resPersonCertificateList 人员证书信息List
     * @return 结果
     */
    public int batchInsertResPersonCertificate(List<ResPersonCertificate> resPersonCertificateList);

    /**
     * 修改人员证书信息
     *
     * @param resPersonCertificate 人员证书信息
     * @return 结果
     */
    public int updateResPersonCertificate(ResPersonCertificate resPersonCertificate);

    /**
     * 批量修改人员证书信息
     *
     * @param resPersonCertificateList 人员证书信息List
     * @return 结果
     */
    public int batchUpdateResPersonCertificate(List<ResPersonCertificate> resPersonCertificateList);

    /**
     * 删除人员证书信息
     *
     * @param certificateId 人员证书信息主键
     * @return 结果
     */
    public int deleteResPersonCertificateByCertificateId(String certificateId);

    /**
     * 批量删除人员证书信息
     *
     * @param certificateIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteResPersonCertificateByCertificateIds(String[] certificateIds);
}
