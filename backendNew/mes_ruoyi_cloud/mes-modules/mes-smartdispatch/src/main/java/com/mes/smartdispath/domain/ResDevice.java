package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 设备对象 tb_res_device
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
public class ResDevice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 设备ID */
    private String id;

    /** 绑定省份ID */
    @Excel(name = "绑定省份ID")
    private String bindProvinceId;

    /** 绑定城市ID(原机必填，备机非必填) */
    @Excel(name = "绑定城市ID(原机必填，备机非必填)")
    private String bindCityId;

    /** 运维单位ID */
    @Excel(name = "运维单位ID")
    private String operationUnitId;

    /** 设备编码(唯一) */
    @Excel(name = "设备编码(唯一)")
    private String deviceCode;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备品牌ID */
    @Excel(name = "设备品牌ID")
    private String deviceBrandId;

    /** 设备型号ID */
    @Excel(name = "设备型号ID")
    private String deviceModelId;

    /** 设备协议ID */
    @Excel(name = "设备协议ID")
    private String deviceProtocolId;

    /** 设备状态(PENDING_ACCEPTANCE/ACCEPTED/STARTED/STOPPED) */
    @Excel(name = "设备状态(PENDING_ACCEPTANCE/ACCEPTED/STARTED/STOPPED)")
    private String deviceStatus;

    /** 原/备机(Y:原机,N:备机) */
    @Excel(name = "原/备机(Y:原机,N:备机)")
    private String isPrimaryDevice;

    /** 监测要素(水运管/气运管) */
    @Excel(name = "监测要素(水运管/气运管)")
    private String monitoringElement;

    /** 一级设备类型 */
    @Excel(name = "一级设备类型")
    private String deviceTypeLevel1;

    /** 二级设备类型 */
    @Excel(name = "二级设备类型")
    private String deviceTypeLevel2;

    /** 三级设备类型 */
    @Excel(name = "三级设备类型")
    private String deviceTypeLevel3;

    /** 是否为计量器具(Y/N) */
    @Excel(name = "是否为计量器具(Y/N)")
    private String isMeasuringInstrument;

    /** 设备SN码(计量器具为是时必填) */
    @Excel(name = "设备SN码(计量器具为是时必填)")
    private String deviceSn;

    /** 来源 */
    @Excel(name = "来源")
    private String source;

    /** 出入库状态(NOT_OUTBOUND/OUTBOUND) */
    @Excel(name = "出入库状态(NOT_OUTBOUND/OUTBOUND)")
    private String inventoryStatus;

    /** 所属仓库ID */
    @Excel(name = "所属仓库ID")
    private String warehouseId;

    /** 设备试剂 */
    @Excel(name = "设备试剂")
    private String deviceReagent;

    /** 设备检出限 */
    @Excel(name = "设备检出限")
    private String detectionLimit;

    /** 分析方法 */
    @Excel(name = "分析方法")
    private String analysisMethod;

    /** 测试项目(JSON数组) */
    @Excel(name = "测试项目(JSON数组)")
    private String testItems;

    /** 使用年限(年) */
    @Excel(name = "使用年限(年)")
    private String serviceYears;

    /** 备机时限(备机时显示) */
    @Excel(name = "备机时限(备机时显示)")
    private String spareDevicePeriod;

    /** 备机验收类型(备机时显示) */
    @Excel(name = "备机验收类型(备机时显示)")
    private String spareAcceptanceType;

    /** 是否为热备(备机时必填,Y/N) */
    @Excel(name = "是否为热备(备机时必填,Y/N)")
    private String isHotSpare;

    /** 备机库名称 */
    @Excel(name = "备机库名称")
    private String spareWarehouseName;

    /** 设备SN码照片URL */
    @Excel(name = "设备SN码照片URL")
    private String snPhotoUrl;

    /** 购置日期 */
    @Excel(name = "购置日期")
    private String purchaseDate;

    /** 是否是黑名单设备(Y/N) */
    @Excel(name = "是否是黑名单设备(Y/N)")
    private String isBlacklist;

    /** 固件版本 */
    @Excel(name = "固件版本")
    private String firmwareVersion;

    /** 软件版本 */
    @Excel(name = "软件版本")
    private String softwareVersion;

    /** 测量原理 */
    @Excel(name = "测量原理")
    private String measurementPrinciple;

    /** 加热方式(PM2.5/PM10专属) */
    @Excel(name = "加热方式(PM2.5/PM10专属)")
    private String heatingMethod;

    /** 最高加热温度(PM2.5/PM10专属) */
    @Excel(name = "最高加热温度(PM2.5/PM10专属)")
    private String maxHeatingTemp;

    /** 加热相对湿度目标值 */
    @Excel(name = "加热相对湿度目标值")
    private String targetHumidity;

    /** 最近上架时间 */
    @Excel(name = "最近上架时间")
    private String lastInstallTime;

    /** 已运行天数 */
    @Excel(name = "已运行天数")
    private String runningDays;

    /** 首次上架时间 */
    @Excel(name = "首次上架时间")
    private String firstInstallTime;

    /** 省份ID(验收后可填) */
    @Excel(name = "省份ID(验收后可填)")
    private String currentProvinceId;

    /** 城市/区域ID(验收后可填) */
    @Excel(name = "城市/区域ID(验收后可填)")
    private String currentCityId;

    /** 所属站点ID(验收后可填) */
    @Excel(name = "所属站点ID(验收后可填)")
    private String currentSiteId;

    /** 绑定站点ID(原机必填) */
    @Excel(name = "绑定站点ID(原机必填)")
    private String bindingSiteId;

    /** 通讯协议 */
    @Excel(name = "通讯协议")
    private String communicationProtocol;

    /** 配置串口号 */
    @Excel(name = "配置串口号")
    private String serialPort;

    /** 波特率 */
    @Excel(name = "波特率")
    private String baudRate;

    /** 奇偶校验 */
    @Excel(name = "奇偶校验")
    private String parityCheck;

    /** 数据位 */
    @Excel(name = "数据位")
    private String dataBits;

    /** 停止位 */
    @Excel(name = "停止位")
    private String stopBits;

    /** 目标IP地址 */
    @Excel(name = "目标IP地址")
    private String targetIp;

    /** 目标IP端口 */
    @Excel(name = "目标IP端口")
    private String targetPort;

    /** 仪器通讯地址 */
    @Excel(name = "仪器通讯地址")
    private String instrumentAddress;

    /** 是否连接串口服务器(Y/N) */
    @Excel(name = "是否连接串口服务器(Y/N)")
    private String isConnectedToSerialServer;

    /** 串口服务器IP */
    @Excel(name = "串口服务器IP")
    private String serialServerIp;

    /** 连接的串口服务器端口号(1-8) */
    @Excel(name = "连接的串口服务器端口号(1-8)")
    private String serialServerPort;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatedBy;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setBindProvinceId(String bindProvinceId) {
        this.bindProvinceId = bindProvinceId;
    }

    public String getBindProvinceId() {
        return bindProvinceId;
    }

    public void setBindCityId(String bindCityId) {
        this.bindCityId = bindCityId;
    }

    public String getBindCityId() {
        return bindCityId;
    }

    public void setOperationUnitId(String operationUnitId) {
        this.operationUnitId = operationUnitId;
    }

    public String getOperationUnitId() {
        return operationUnitId;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceBrandId(String deviceBrandId) {
        this.deviceBrandId = deviceBrandId;
    }

    public String getDeviceBrandId() {
        return deviceBrandId;
    }

    public void setDeviceModelId(String deviceModelId) {
        this.deviceModelId = deviceModelId;
    }

    public String getDeviceModelId() {
        return deviceModelId;
    }

    public void setDeviceProtocolId(String deviceProtocolId) {
        this.deviceProtocolId = deviceProtocolId;
    }

    public String getDeviceProtocolId() {
        return deviceProtocolId;
    }

    public void setDeviceStatus(String deviceStatus) {
        this.deviceStatus = deviceStatus;
    }

    public String getDeviceStatus() {
        return deviceStatus;
    }

    public void setIsPrimaryDevice(String isPrimaryDevice) {
        this.isPrimaryDevice = isPrimaryDevice;
    }

    public String getIsPrimaryDevice() {
        return isPrimaryDevice;
    }

    public void setMonitoringElement(String monitoringElement) {
        this.monitoringElement = monitoringElement;
    }

    public String getMonitoringElement() {
        return monitoringElement;
    }

    public void setDeviceTypeLevel1(String deviceTypeLevel1) {
        this.deviceTypeLevel1 = deviceTypeLevel1;
    }

    public String getDeviceTypeLevel1() {
        return deviceTypeLevel1;
    }

    public void setDeviceTypeLevel2(String deviceTypeLevel2) {
        this.deviceTypeLevel2 = deviceTypeLevel2;
    }

    public String getDeviceTypeLevel2() {
        return deviceTypeLevel2;
    }

    public void setDeviceTypeLevel3(String deviceTypeLevel3) {
        this.deviceTypeLevel3 = deviceTypeLevel3;
    }

    public String getDeviceTypeLevel3() {
        return deviceTypeLevel3;
    }

    public void setIsMeasuringInstrument(String isMeasuringInstrument) {
        this.isMeasuringInstrument = isMeasuringInstrument;
    }

    public String getIsMeasuringInstrument() {
        return isMeasuringInstrument;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public void setInventoryStatus(String inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public String getInventoryStatus() {
        return inventoryStatus;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setDeviceReagent(String deviceReagent) {
        this.deviceReagent = deviceReagent;
    }

    public String getDeviceReagent() {
        return deviceReagent;
    }

    public void setDetectionLimit(String detectionLimit) {
        this.detectionLimit = detectionLimit;
    }

    public String getDetectionLimit() {
        return detectionLimit;
    }

    public void setAnalysisMethod(String analysisMethod) {
        this.analysisMethod = analysisMethod;
    }

    public String getAnalysisMethod() {
        return analysisMethod;
    }

    public void setTestItems(String testItems) {
        this.testItems = testItems;
    }

    public String getTestItems() {
        return testItems;
    }

    public void setServiceYears(String serviceYears) {
        this.serviceYears = serviceYears;
    }

    public String getServiceYears() {
        return serviceYears;
    }

    public void setSpareDevicePeriod(String spareDevicePeriod) {
        this.spareDevicePeriod = spareDevicePeriod;
    }

    public String getSpareDevicePeriod() {
        return spareDevicePeriod;
    }

    public void setSpareAcceptanceType(String spareAcceptanceType) {
        this.spareAcceptanceType = spareAcceptanceType;
    }

    public String getSpareAcceptanceType() {
        return spareAcceptanceType;
    }

    public void setIsHotSpare(String isHotSpare) {
        this.isHotSpare = isHotSpare;
    }

    public String getIsHotSpare() {
        return isHotSpare;
    }

    public void setSpareWarehouseName(String spareWarehouseName) {
        this.spareWarehouseName = spareWarehouseName;
    }

    public String getSpareWarehouseName() {
        return spareWarehouseName;
    }

    public void setSnPhotoUrl(String snPhotoUrl) {
        this.snPhotoUrl = snPhotoUrl;
    }

    public String getSnPhotoUrl() {
        return snPhotoUrl;
    }

    public void setPurchaseDate(String purchaseDate) {
        this.purchaseDate = purchaseDate;
    }

    public String getPurchaseDate() {
        return purchaseDate;
    }

    public void setIsBlacklist(String isBlacklist) {
        this.isBlacklist = isBlacklist;
    }

    public String getIsBlacklist() {
        return isBlacklist;
    }

    public void setFirmwareVersion(String firmwareVersion) {
        this.firmwareVersion = firmwareVersion;
    }

    public String getFirmwareVersion() {
        return firmwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public String getSoftwareVersion() {
        return softwareVersion;
    }

    public void setMeasurementPrinciple(String measurementPrinciple) {
        this.measurementPrinciple = measurementPrinciple;
    }

    public String getMeasurementPrinciple() {
        return measurementPrinciple;
    }

    public void setHeatingMethod(String heatingMethod) {
        this.heatingMethod = heatingMethod;
    }

    public String getHeatingMethod() {
        return heatingMethod;
    }

    public void setMaxHeatingTemp(String maxHeatingTemp) {
        this.maxHeatingTemp = maxHeatingTemp;
    }

    public String getMaxHeatingTemp() {
        return maxHeatingTemp;
    }

    public void setTargetHumidity(String targetHumidity) {
        this.targetHumidity = targetHumidity;
    }

    public String getTargetHumidity() {
        return targetHumidity;
    }

    public void setLastInstallTime(String lastInstallTime) {
        this.lastInstallTime = lastInstallTime;
    }

    public String getLastInstallTime() {
        return lastInstallTime;
    }

    public void setRunningDays(String runningDays) {
        this.runningDays = runningDays;
    }

    public String getRunningDays() {
        return runningDays;
    }

    public void setFirstInstallTime(String firstInstallTime) {
        this.firstInstallTime = firstInstallTime;
    }

    public String getFirstInstallTime() {
        return firstInstallTime;
    }

    public void setCurrentProvinceId(String currentProvinceId) {
        this.currentProvinceId = currentProvinceId;
    }

    public String getCurrentProvinceId() {
        return currentProvinceId;
    }

    public void setCurrentCityId(String currentCityId) {
        this.currentCityId = currentCityId;
    }

    public String getCurrentCityId() {
        return currentCityId;
    }

    public void setCurrentSiteId(String currentSiteId) {
        this.currentSiteId = currentSiteId;
    }

    public String getCurrentSiteId() {
        return currentSiteId;
    }

    public void setBindingSiteId(String bindingSiteId) {
        this.bindingSiteId = bindingSiteId;
    }

    public String getBindingSiteId() {
        return bindingSiteId;
    }

    public void setCommunicationProtocol(String communicationProtocol) {
        this.communicationProtocol = communicationProtocol;
    }

    public String getCommunicationProtocol() {
        return communicationProtocol;
    }

    public void setSerialPort(String serialPort) {
        this.serialPort = serialPort;
    }

    public String getSerialPort() {
        return serialPort;
    }

    public void setBaudRate(String baudRate) {
        this.baudRate = baudRate;
    }

    public String getBaudRate() {
        return baudRate;
    }

    public void setParityCheck(String parityCheck) {
        this.parityCheck = parityCheck;
    }

    public String getParityCheck() {
        return parityCheck;
    }

    public void setDataBits(String dataBits) {
        this.dataBits = dataBits;
    }

    public String getDataBits() {
        return dataBits;
    }

    public void setStopBits(String stopBits) {
        this.stopBits = stopBits;
    }

    public String getStopBits() {
        return stopBits;
    }

    public void setTargetIp(String targetIp) {
        this.targetIp = targetIp;
    }

    public String getTargetIp() {
        return targetIp;
    }

    public void setTargetPort(String targetPort) {
        this.targetPort = targetPort;
    }

    public String getTargetPort() {
        return targetPort;
    }

    public void setInstrumentAddress(String instrumentAddress) {
        this.instrumentAddress = instrumentAddress;
    }

    public String getInstrumentAddress() {
        return instrumentAddress;
    }

    public void setIsConnectedToSerialServer(String isConnectedToSerialServer) {
        this.isConnectedToSerialServer = isConnectedToSerialServer;
    }

    public String getIsConnectedToSerialServer() {
        return isConnectedToSerialServer;
    }

    public void setSerialServerIp(String serialServerIp) {
        this.serialServerIp = serialServerIp;
    }

    public String getSerialServerIp() {
        return serialServerIp;
    }

    public void setSerialServerPort(String serialServerPort) {
        this.serialServerPort = serialServerPort;
    }

    public String getSerialServerPort() {
        return serialServerPort;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("bindProvinceId", getBindProvinceId()).append("bindCityId", getBindCityId())
            .append("operationUnitId", getOperationUnitId()).append("deviceCode", getDeviceCode())
            .append("deviceName", getDeviceName()).append("deviceBrandId", getDeviceBrandId())
            .append("deviceModelId", getDeviceModelId()).append("deviceProtocolId", getDeviceProtocolId())
            .append("deviceStatus", getDeviceStatus()).append("isPrimaryDevice", getIsPrimaryDevice())
            .append("monitoringElement", getMonitoringElement()).append("deviceTypeLevel1", getDeviceTypeLevel1())
            .append("deviceTypeLevel2", getDeviceTypeLevel2()).append("deviceTypeLevel3", getDeviceTypeLevel3())
            .append("isMeasuringInstrument", getIsMeasuringInstrument()).append("deviceSn", getDeviceSn())
            .append("source", getSource()).append("inventoryStatus", getInventoryStatus())
            .append("warehouseId", getWarehouseId()).append("deviceReagent", getDeviceReagent())
            .append("detectionLimit", getDetectionLimit()).append("analysisMethod", getAnalysisMethod())
            .append("testItems", getTestItems()).append("serviceYears", getServiceYears())
            .append("spareDevicePeriod", getSpareDevicePeriod()).append("spareAcceptanceType", getSpareAcceptanceType())
            .append("isHotSpare", getIsHotSpare()).append("spareWarehouseName", getSpareWarehouseName())
            .append("snPhotoUrl", getSnPhotoUrl()).append("purchaseDate", getPurchaseDate())
            .append("isBlacklist", getIsBlacklist()).append("firmwareVersion", getFirmwareVersion())
            .append("softwareVersion", getSoftwareVersion()).append("measurementPrinciple", getMeasurementPrinciple())
            .append("heatingMethod", getHeatingMethod()).append("maxHeatingTemp", getMaxHeatingTemp())
            .append("targetHumidity", getTargetHumidity()).append("lastInstallTime", getLastInstallTime())
            .append("runningDays", getRunningDays()).append("firstInstallTime", getFirstInstallTime())
            .append("currentProvinceId", getCurrentProvinceId()).append("currentCityId", getCurrentCityId())
            .append("currentSiteId", getCurrentSiteId()).append("bindingSiteId", getBindingSiteId())
            .append("communicationProtocol", getCommunicationProtocol()).append("serialPort", getSerialPort())
            .append("baudRate", getBaudRate()).append("parityCheck", getParityCheck()).append("dataBits", getDataBits())
            .append("stopBits", getStopBits()).append("targetIp", getTargetIp()).append("targetPort", getTargetPort())
            .append("instrumentAddress", getInstrumentAddress())
            .append("isConnectedToSerialServer", getIsConnectedToSerialServer())
            .append("serialServerIp", getSerialServerIp()).append("serialServerPort", getSerialServerPort())
            .append("createdBy", getCreatedBy()).append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy()).append("updateTime", getUpdateTime()).toString();
    }
}
