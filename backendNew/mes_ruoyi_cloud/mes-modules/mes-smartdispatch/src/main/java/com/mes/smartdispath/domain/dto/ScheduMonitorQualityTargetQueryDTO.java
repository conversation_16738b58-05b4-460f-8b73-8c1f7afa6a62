package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.domain.ScheduMonitorQualityTarget;

/**
 * @Author: li.haoyang
 * @Description：质量目标管理对象查询DTO @Date： 2025/7/4
 */
public class ScheduMonitorQualityTargetQueryDTO extends ScheduMonitorQualityTarget {
    /**
     * 站点id数组
     */
    private List<String> siteIdArr;

    /**
     * 城市id数组
     */
    private List<String> cityCodeArr;

    /**
     * 业务类型字典编码
     */
    private String businessTypeDictClassCode;

    /**
     * 站点类型字典编码
     */
    private String siteTypeDictClassCode;

    /**
     * 是否自动站，1-是 0-否
     */
    private String isAutosite;

    /**
     * 所属包间id
     */
    private String packageId;

    /**
     * 所属包间id数组
     */
    private List<String> packageIdArr;

    /**
     * 是否有自动站(水独有,Y/N)
     */
    private String hasAutomaticStation;

    private String startTime;

    private String endTime;

    public List<String> getSiteIdArr() {
        return siteIdArr;
    }

    public void setSiteIdArr(List<String> siteIdArr) {
        this.siteIdArr = siteIdArr;
    }

    public List<String> getCityCodeArr() {
        return cityCodeArr;
    }

    public void setCityCodeArr(List<String> cityCodeArr) {
        this.cityCodeArr = cityCodeArr;
    }

    public String getBusinessTypeDictClassCode() {
        return businessTypeDictClassCode;
    }

    public void setBusinessTypeDictClassCode(String businessTypeDictClassCode) {
        this.businessTypeDictClassCode = businessTypeDictClassCode;
    }

    public String getSiteTypeDictClassCode() {
        return siteTypeDictClassCode;
    }

    public void setSiteTypeDictClassCode(String siteTypeDictClassCode) {
        this.siteTypeDictClassCode = siteTypeDictClassCode;
    }

    public String getIsAutosite() {
        return isAutosite;
    }

    public void setIsAutosite(String isAutosite) {
        this.isAutosite = isAutosite;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public List<String> getPackageIdArr() {
        return packageIdArr;
    }

    public void setPackageIdArr(List<String> packageIdArr) {
        this.packageIdArr = packageIdArr;
    }

    public String getHasAutomaticStation() {
        return hasAutomaticStation;
    }

    public void setHasAutomaticStation(String hasAutomaticStation) {
        this.hasAutomaticStation = hasAutomaticStation;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
