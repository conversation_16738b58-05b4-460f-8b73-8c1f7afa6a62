package com.mes.smartdispath.constant;

/**
 * @Author: li.haoyang
 * @Description：任务相关常量 @Date： 2025/7/9
 */
public class TaskConstant {
    /**
     * 待推送任务状态
     */
    public static final String TASK_STATUS_WAIT_PUSH = "1";

    /**
     * 已推送任务状态
     */
    public static final String TASK_STATUS_PUSHED = "2";

    /**
     * 进行中任务状态
     */
    public static final String TASK_STATUS_IN_PROGRESS = "3";

    /**
     * 已完成任务状态
     */
    public static final String TASK_STATUS_FINISHED = "4";

    /**
     * 任务创建方式，1-定时调度（算法创建的方式）
     */
    public static final String TASK_CREATE_TYPE_SCHEDULE = "1";

    /**
     * 任务创建方式，2-及时调度
     */
    public static final String TASK_CREATE_TYPE_IMMEDIATE = "2";

    /**
     * 任务创建方式，3-人工创建
     */
    public static final String TASK_CREATE_TYPE_MANUAL = "3";
}
