package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 清华调度算法基础信息对象 tb_schedu_algorithm_info
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduAlgorithmInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 调度算法编码 */
    @Excel(name = "调度算法编码")
    private String algorithmCode;

    /** 调度算法名称 */
    @Excel(name = "调度算法名称")
    private String algorithmName;

    /** 算法执行时间表达式 */
    @Excel(name = "算法执行时间表达式")
    private String algCronRule;

    /** 算法描述 */
    @Excel(name = "算法描述")
    private String algDesc;

    /** 调度算法启用状态：0-禁用，1-启用 */
    @Excel(name = "调度算法启用状态：0-禁用，1-启用")
    private String algStatus;

    /** 软删除标志：0-否，1-是 */
    @Excel(name = "软删除标志：0-否，1-是")
    private String isDelete;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setAlgorithmCode(String algorithmCode) {
        this.algorithmCode = algorithmCode;
    }

    public String getAlgorithmCode() {
        return algorithmCode;
    }

    public void setAlgorithmName(String algorithmName) {
        this.algorithmName = algorithmName;
    }

    public String getAlgorithmName() {
        return algorithmName;
    }

    public void setAlgCronRule(String algCronRule) {
        this.algCronRule = algCronRule;
    }

    public String getAlgCronRule() {
        return algCronRule;
    }

    public void setAlgDesc(String algDesc) {
        this.algDesc = algDesc;
    }

    public String getAlgDesc() {
        return algDesc;
    }

    public void setAlgStatus(String algStatus) {
        this.algStatus = algStatus;
    }

    public String getAlgStatus() {
        return algStatus;
    }

    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

    public String getIsDelete() {
        return isDelete;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("algorithmCode", getAlgorithmCode()).append("algorithmName", getAlgorithmName())
            .append("algCronRule", getAlgCronRule()).append("algDesc", getAlgDesc()).append("algStatus", getAlgStatus())
            .append("isDelete", getIsDelete()).append("createBy", getCreateBy()).append("createTime", getCreateTime())
            .append("tenantId", getTenantId()).toString();
    }
}
