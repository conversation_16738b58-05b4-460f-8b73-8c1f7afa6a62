package com.mes.smartdispath.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mes.common.core.utils.DateUtils;
import com.mes.smartdispath.domain.PubOperationLog;
import com.mes.smartdispath.mapper.PubOperationLogMapper;
import com.mes.smartdispath.service.IPubOperationLogService;

/**
 * 操作日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class PubOperationLogServiceImpl implements IPubOperationLogService {
    @Autowired
    private PubOperationLogMapper pubOperationLogMapper;

    /**
     * 查询操作日志
     * 
     * @param id 操作日志主键
     * @return 操作日志
     */
    @Override
    public PubOperationLog selectPubOperationLogById(String id) {
        return pubOperationLogMapper.selectPubOperationLogById(id);
    }

    /**
     * 查询操作日志列表
     * 
     * @param pubOperationLog 操作日志
     * @return 操作日志
     */
    @Override
    public List<PubOperationLog> selectPubOperationLogList(PubOperationLog pubOperationLog) {
        return pubOperationLogMapper.selectPubOperationLogList(pubOperationLog);
    }

    /**
     * 新增操作日志
     * 
     * @param pubOperationLog 操作日志
     * @return 结果
     */
    @Override
    public int insertPubOperationLog(PubOperationLog pubOperationLog) {
        pubOperationLog.setCreateTime(DateUtils.getNowDate());
        return pubOperationLogMapper.insertPubOperationLog(pubOperationLog);
    }

    /**
     * 修改操作日志
     * 
     * @param pubOperationLog 操作日志
     * @return 结果
     */
    @Override
    public int updatePubOperationLog(PubOperationLog pubOperationLog) {
        pubOperationLog.setUpdateTime(DateUtils.getNowDate());
        return pubOperationLogMapper.updatePubOperationLog(pubOperationLog);
    }

    /**
     * 批量删除操作日志
     * 
     * @param ids 需要删除的操作日志主键
     * @return 结果
     */
    @Override
    public int deletePubOperationLogByIds(String[] ids) {
        return pubOperationLogMapper.deletePubOperationLogByIds(ids);
    }

    /**
     * 删除操作日志信息
     * 
     * @param id 操作日志主键
     * @return 结果
     */
    @Override
    public int deletePubOperationLogById(String id) {
        return pubOperationLogMapper.deletePubOperationLogById(id);
    }
}
