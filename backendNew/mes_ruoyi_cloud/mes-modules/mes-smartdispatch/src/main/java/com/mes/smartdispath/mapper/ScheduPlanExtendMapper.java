package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ScheduPlanExtend;

/**
 * 调度计划拓展信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduPlanExtendMapper {
    /**
     * 查询调度计划拓展信息（通过主计划id数组）
     *
     * @param planIds 计划信息主键
     * @return 调度计划拓展信息
     */
    public List<ScheduPlanExtend> selectPlanExtendByPlanIds(@Param("planIds") List<String> planIds);

    /**
     * 查询调度计划拓展信息
     * 
     * @param id 调度计划拓展信息主键
     * @return 调度计划拓展信息
     */
    public ScheduPlanExtend selectScheduPlanExtendById(String id);

    /**
     * 查询调度计划拓展信息（通过主计划id）
     *
     * @param planId 计划信息主键
     * @return 调度计划拓展信息
     */
    public ScheduPlanExtend selectScheduPlanExtendByPlanId(String planId);

    /**
     * 查询调度计划拓展信息列表
     * 
     * @param scheduPlanExtend 调度计划拓展信息
     * @return 调度计划拓展信息集合
     */
    public List<ScheduPlanExtend> selectScheduPlanExtendList(ScheduPlanExtend scheduPlanExtend);

    /**
     * 新增调度计划拓展信息
     * 
     * @param scheduPlanExtend 调度计划拓展信息
     * @return 结果
     */
    public int insertScheduPlanExtend(ScheduPlanExtend scheduPlanExtend);

    /**
     * 批量新增调度计划拓展信息
     *
     * @param scheduPlanExtendList 调度计划拓展信息List
     * @return 结果
     */
    public int batchInsertScheduPlanExtend(List<ScheduPlanExtend> scheduPlanExtendList);

    /**
     * 修改调度计划拓展信息
     * 
     * @param scheduPlanExtend 调度计划拓展信息
     * @return 结果
     */
    public int updateScheduPlanExtend(ScheduPlanExtend scheduPlanExtend);

    /**
     * 批量修改调度计划拓展信息
     *
     * @param scheduPlanExtendList 调度计划拓展信息List
     * @return 结果
     */
    public int batchUpdateScheduPlanExtend(List<ScheduPlanExtend> scheduPlanExtendList);

    /**
     * 删除调度计划拓展信息
     * 
     * @param id 调度计划拓展信息主键
     * @return 结果
     */
    public int deleteScheduPlanExtendById(String id);

    /**
     * 批量删除调度计划拓展信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduPlanExtendByIds(String[] ids);
}
