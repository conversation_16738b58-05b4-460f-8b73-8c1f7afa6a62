package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 调度任务拓展信息对象 tb_schedu_task_extend
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduTaskExtend extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 任务ID */
    @Excel(name = "任务ID")
    private String taskId;

    /** 任务编号，全局唯一标识 */
    @Excel(name = "任务编号，全局唯一标识")
    private String taskCode;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 任务执行人ID */
    @Excel(name = "任务执行人ID")
    private String executorId;

    /** 任务执行人姓名 */
    @Excel(name = "任务执行人姓名")
    private String executorName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 运维单位编码 */
    @Excel(name = "运维单位编码")
    private String maintainUnitCode;

    /** 运维单位名称 */
    @Excel(name = "运维单位名称")
    private String maintainUnitName;

    /** 实验室地址 */
    @Excel(name = "实验室地址")
    private String laboratoryAddress;

    /** 实验室名称 */
    @Excel(name = "实验室名称")
    private String laboratoryName;

    /** 实验室code */
    @Excel(name = "实验室code")
    private String laboratoryCode;

    /** 任务交割地址，如采样与送样任务 */
    @Excel(name = "任务交割地址，如采样与送样任务")
    private String deliveryAddress;

    /** 软删除标志（Y/N） */
    @Excel(name = "软删除标志", readConverterExp = "Y=/N")
    private String isDeleted;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setExecutorId(String executorId) {
        this.executorId = executorId;
    }

    public String getExecutorId() {
        return executorId;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setMaintainUnitCode(String maintainUnitCode) {
        this.maintainUnitCode = maintainUnitCode;
    }

    public String getMaintainUnitCode() {
        return maintainUnitCode;
    }

    public void setMaintainUnitName(String maintainUnitName) {
        this.maintainUnitName = maintainUnitName;
    }

    public String getMaintainUnitName() {
        return maintainUnitName;
    }

    public void setLaboratoryAddress(String laboratoryAddress) {
        this.laboratoryAddress = laboratoryAddress;
    }

    public String getLaboratoryAddress() {
        return laboratoryAddress;
    }

    public void setLaboratoryName(String laboratoryName) {
        this.laboratoryName = laboratoryName;
    }

    public String getLaboratoryName() {
        return laboratoryName;
    }

    public void setLaboratoryCode(String laboratoryCode) {
        this.laboratoryCode = laboratoryCode;
    }

    public String getLaboratoryCode() {
        return laboratoryCode;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("taskId", getTaskId()).append("taskCode", getTaskCode()).append("taskName", getTaskName())
            .append("executorId", getExecutorId()).append("executorName", getExecutorName()).append("phone", getPhone())
            .append("maintainUnitCode", getMaintainUnitCode()).append("maintainUnitName", getMaintainUnitName())
            .append("laboratoryAddress", getLaboratoryAddress()).append("laboratoryName", getLaboratoryName())
            .append("laboratoryCode", getLaboratoryCode()).append("deliveryAddress", getDeliveryAddress())
            .append("createTime", getCreateTime()).append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime()).append("updateBy", getUpdateBy()).append("isDeleted", getIsDeleted())
            .toString();
    }
}
