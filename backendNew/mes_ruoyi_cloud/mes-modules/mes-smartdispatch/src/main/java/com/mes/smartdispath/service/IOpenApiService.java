package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.ScheduAlgorithmInfo;
import com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord;
import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.openApi.AddMonitorQualityTargetApiDTO;
import com.mes.smartdispath.domain.openApi.AddPlanInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.AddTaskInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.ScheduPlanInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.ScheduTaskInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.UpdatePlanExtendApiDTO;
import com.mes.smartdispath.domain.vo.ScheduAlgruleConfigVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;

/**
 * 开放接口Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IOpenApiService {
    /**
     * 任务状态更新接口
     *
     * @param taskInfo
     * @return
     */
    public Boolean updateTaskStatus(ScheduTaskInfoOpenApiDTO taskInfo);

    /**
     * 计划状态更新接口
     *
     * @param planInfo
     * @return
     */
    public Boolean updatePlanStatus(ScheduPlanInfoOpenApiDTO planInfo);

    /**
     * 监测活动查询接口
     * 
     * @param businessType
     * @param siteType
     * @return
     */
    public List<ScheduMonitorActivityInfo> getActivityInfoList(String businessType, String siteType);

    /**
     * 调度计划创建接口
     *
     * @param dto
     * @return
     */
    public Boolean addPlanInfo(AddPlanInfoOpenApiDTO dto);

    /**
     * 计划信息查询
     *
     * @param queryDto
     * @return 计划信息列表
     */
    public List<ScheduPlanInfoVO> getPlanInfoList(ScheduPlanInfoQueryDTO queryDto);

    /**
     * 质量目标新增接口
     *
     * @param dto
     * @return 计划信息列表
     */
    public Boolean batchAddQualityTarget(AddMonitorQualityTargetApiDTO dto);

    /**
     * 调度任务创建接口
     *
     * @param dto
     * @return
     */
    public Boolean addTaskInfo(AddTaskInfoOpenApiDTO dto);

    /**
     * 批量更新计划扩展信息
     *
     * @param dto
     * @return
     */
    public Boolean batchUpdatePlanExtend(UpdatePlanExtendApiDTO dto);

    /**
     * 查询算法基础信息列表
     *
     * @return
     */
    public List<ScheduAlgorithmInfo> getAlgorithmInfoList();

    /**
     * 查询算法规则列表
     *
     * @return
     */
    public List<ScheduAlgruleConfigVO> getAlgruleConfigList();

    /**
     * 批量新增算法运行记录信息
     *
     * @param dto
     * @return
     */
    public Boolean batchAddAlgorithmInvokeRecord(List<ScheduAlgorithmInvokeRecord> dto);
}
