package com.mes.smartdispath.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.smartdispath.domain.dto.ScheduMonitorQualityTargetQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorQuantityTargetQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTargetFileDTO;
import com.mes.smartdispath.domain.dto.ScheduTargetFileQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQualityTargetVO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQuantityTargetVO;
import com.mes.smartdispath.domain.vo.ScheduTargetFileVO;
import com.mes.smartdispath.service.ITargetMgrService;

import cn.hutool.core.lang.Assert;

/**
 * 目标管理 Controller
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/targetmgr")
public class TargetMgrController extends BaseController {
    @Autowired
    private ITargetMgrService targetMgrService;

    /**
     * 2.1.1、数量目标查询
     */
    @GetMapping("/qryNumTargetList")
    public TableDataInfo qryNumTargetList(ScheduMonitorQuantityTargetQueryDTO queryDto) {
        startPage();
        List<ScheduMonitorQuantityTargetVO> list = targetMgrService.qryNumTargetList(queryDto);
        return getDataTable(list);
    }

    /**
     * 2.1.1、数量目标汇总查询
     */
    @GetMapping("/qryCollectNumTargetList")
    public TableDataInfo qryCollectNumTargetList(ScheduMonitorQuantityTargetQueryDTO queryDto) {
        startPage();
        List<ScheduMonitorQuantityTargetVO> list = targetMgrService.qryCollectNumTargetList(queryDto);
        return getDataTable(list);
    }

    /**
     * 2.1.2、数量目标新增/编辑
     */
    @PostMapping("/saveNumTarget")
    public AjaxResult saveNumTarget(@RequestBody ScheduMonitorQuantityTargetQueryDTO scheduMonitorQuantityTarget) {
        return toAjax(targetMgrService.saveNumTarget(scheduMonitorQuantityTarget));
    }

    /**
     * 2.1.3、数量目标删除
     */
    @DeleteMapping("/delNumTarget/{id}")
    public AjaxResult delNumTarget(@PathVariable String id) {
        return toAjax(targetMgrService.delNumTarget(id));
    }

    /**
     * 2.1.4、数量目标文件列表
     */
    @GetMapping("/qryNumTargetFileList")
    public TableDataInfo qryNumTargetFileList(ScheduTargetFileQueryDTO queryParam) {
        startPage();
        List<ScheduTargetFileVO> list = targetMgrService.qryNumTargetFileList(queryParam);
        return getDataTable(list);
    }

    /**
     * 2.1.5、数量目标文件新增/编辑
     */
    @PostMapping("/saveNumTargetFile")
    public AjaxResult saveNumTargetFile(@RequestBody ScheduTargetFileDTO scheduTargetFileDTO) {
        return toAjax(targetMgrService.saveNumTargetFile(scheduTargetFileDTO));
    }

    /**
     * 2.1.6、数量目标文件删除
     */
    @DeleteMapping("/delNumTargetFile/{id}")
    public AjaxResult delNumTargetFile(@PathVariable String id) {
        return toAjax(targetMgrService.delNumTargetFile(id));
    }

    /**
     * 2.1.7、预览数量目标文件查询
     */
    @GetMapping("/qryTargetFileAttachInfo")
    public AjaxResult qryTargetFileAttachInfo(String id) {
        Assert.notNull(id, "查询数据的id参数不能为空,请核对请求参数!");
        return success(targetMgrService.qryTargetFileAttachInfo(id));
    }

    /**
     * 2.2.1、质量目标查询
     */
    @GetMapping("/qryQualityTargetList")
    public TableDataInfo qryQualityTargetList(ScheduMonitorQualityTargetQueryDTO queryParam) {
        startPage();
        List<ScheduMonitorQualityTargetVO> list = targetMgrService.qryQualityTargetList(queryParam);
        return getDataTable(list);
    }

    /**
     * 2.2.2、质量目标新增/编辑
     */
    @PostMapping("/saveQualityTarget")
    public AjaxResult saveQualityTarget(@RequestBody ScheduMonitorQualityTargetQueryDTO scheduMonitorQualityTarget) {
        return toAjax(targetMgrService.saveQualityTarget(scheduMonitorQualityTarget));
    }

    /**
     * 2.2.3、质量目标删除
     */
    @DeleteMapping("/delQualityTarget/{id}")
    public AjaxResult delQualityTarget(@PathVariable String id) {
        return toAjax(targetMgrService.delQualityTarget(id));
    }
}
