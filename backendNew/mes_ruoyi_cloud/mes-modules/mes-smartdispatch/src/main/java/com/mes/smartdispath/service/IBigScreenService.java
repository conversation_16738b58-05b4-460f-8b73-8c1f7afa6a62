package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.DispatchEfficiencyStaticVO;
import com.mes.smartdispath.domain.vo.MaintainPersonCountVO;
import com.mes.smartdispath.domain.vo.PlanSourceStaticVO;
import com.mes.smartdispath.domain.vo.QuantityTargetCollectPerformanceVO;
import com.mes.smartdispath.domain.vo.SiteTaskListVO;
import com.mes.smartdispath.domain.vo.TaskCountStaticVO;
import com.mes.smartdispath.domain.vo.TaskStaticVO;

/**
 * 大屏 Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
public interface IBigScreenService {
    /**
     * 汇总的数量目标统计
     *
     * @return
     */
    public QuantityTargetCollectPerformanceVO qryCollectQuantityTargetPerformance();

    /**
     * 调度计划来源统计
     *
     * @param queryDTO 参数
     * @return 统计信息
     */
    public List<PlanSourceStaticVO> qryPlanSourceStatic(ScheduPlanInfoQueryDTO queryDTO);

    /**
     * 调度任务分布统计，按区域
     *
     * @param queryDTO
     * @return
     */
    public List<TaskCountStaticVO> qryRegionTaskCountStatic(ScheduTaskInfoQueryDTO queryDTO);

    /**
     * 调度任务分布统计，按运维单位
     *
     * @param queryDTO
     * @return
     */
    public List<TaskCountStaticVO> qryMaintainUnitTaskCountStatic(ScheduTaskInfoQueryDTO queryDTO);

    /**
     * 今日运维人员数量统计
     *
     * @return
     */
    public MaintainPersonCountVO qryTodayMaintainPersonStatic(String businessType);

    /**
     * 统计调度任务
     *
     * @param queryDTO 参数
     * @return 任务统计信息
     */
    public TaskStaticVO qryCurMonthTaskStatic(TaskStaticQueryDTO queryDTO);

    /**
     * 当月的调度任务列表
     *
     * @param queryDTO
     * @return 调度任务列表
     */
    public List<SiteTaskListVO> qryCurMonthTaskList(TaskStaticQueryDTO queryDTO);

    /**
     * 调度效率统计
     *
     * @return
     */
    public DispatchEfficiencyStaticVO qryDispatchEfficiencyStatic();
}
