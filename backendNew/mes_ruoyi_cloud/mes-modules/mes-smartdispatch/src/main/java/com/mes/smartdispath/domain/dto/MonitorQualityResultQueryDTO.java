package com.mes.smartdispath.domain.dto;

import java.util.List;

import com.mes.smartdispath.domain.ScheduMonitorQualityResult;

/**
 * 质量结果查询DTO
 *
 * @Author: li.haoyang @Date： 2025/7/19
 */
public class MonitorQualityResultQueryDTO extends ScheduMonitorQualityResult {
    /**
     * 统计年数组
     */
    private List<String> statYearArr;

    /**
     * 省份编码数组
     */
    private List<String> provinceCodeArr;

    /**
     * 城市编码数组
     */
    private List<String> cityCodeArr;

    /**
     * 站点ID数组
     */
    private List<String> siteIdArr;

    /**
     * 监测参数数组
     */
    private List<String> monitIndexArr;

    /**
     * 运维单位编码数组
     */
    private List<String> maintainUnitCodeArr;

    public List<String> getStatYearArr() {
        return statYearArr;
    }

    public void setStatYearArr(List<String> statYearArr) {
        this.statYearArr = statYearArr;
    }

    public List<String> getProvinceCodeArr() {
        return provinceCodeArr;
    }

    public void setProvinceCodeArr(List<String> provinceCodeArr) {
        this.provinceCodeArr = provinceCodeArr;
    }

    public List<String> getCityCodeArr() {
        return cityCodeArr;
    }

    public void setCityCodeArr(List<String> cityCodeArr) {
        this.cityCodeArr = cityCodeArr;
    }

    public List<String> getSiteIdArr() {
        return siteIdArr;
    }

    public void setSiteIdArr(List<String> siteIdArr) {
        this.siteIdArr = siteIdArr;
    }

    public List<String> getMonitIndexArr() {
        return monitIndexArr;
    }

    public void setMonitIndexArr(List<String> monitIndexArr) {
        this.monitIndexArr = monitIndexArr;
    }

    public List<String> getMaintainUnitCodeArr() {
        return maintainUnitCodeArr;
    }

    public void setMaintainUnitCodeArr(List<String> maintainUnitCodeArr) {
        this.maintainUnitCodeArr = maintainUnitCodeArr;
    }
}
