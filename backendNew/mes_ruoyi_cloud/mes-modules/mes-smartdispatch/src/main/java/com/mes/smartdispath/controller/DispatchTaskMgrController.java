package com.mes.smartdispath.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.smartdispath.domain.dto.ResPersonBasicQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.ResPersonBasicVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.domain.vo.ScheduTaskInfoVO;
import com.mes.smartdispath.domain.vo.SiteTaskListVO;
import com.mes.smartdispath.service.IDispatchTaskMgrService;

import cn.hutool.core.lang.Assert;

/**
 * 调度任务管理 Controller
 *
 * @author: li.haoyang @date： 2025/7/9
 */
@RestController
@RequestMapping("/dispatchtaskmgr")
public class DispatchTaskMgrController extends BaseController {
    @Autowired
    private IDispatchTaskMgrService dispatchTaskMgrService;

    /**
     * 5.1.1、【调度计划分配】查询计划列表
     */
    @GetMapping("/qryDispatchtaskPlanList")
    public TableDataInfo qryDispatchtaskPlanList(ScheduPlanInfoQueryDTO queryDto) {
        startPage();
        List<ScheduPlanInfoVO> list = dispatchTaskMgrService.qryDispatchtaskPlanList(queryDto);
        return getDataTable(list);
    }

    /**
     * 5.1.2、【调度计划分配】手工分配任务
     */
    @PostMapping("/manualDispatchPlan")
    public AjaxResult manualDispatchPlan(@RequestBody ScheduTaskInfoDTO dto) {
        return toAjax(dispatchTaskMgrService.manualDispatchPlan(dto));
    }

    /**
     * 5.1.3、【调度计划分配】立刻调用算法分配任务
     */
    @GetMapping("/callDispatchPlan")
    public AjaxResult callDispatchPlan(String id) {
        Assert.notNull(id, "立刻调用算法分配任务的id参数不能为空,请核对请求参数!");
        return success(dispatchTaskMgrService.callDispatchPlan(id));
    }

    /**
     * 5.1.4、【调度计划分配】查询手工分配任务的资源【人员】列表
     */
    @GetMapping("/qryManualDispatchResList")
    public TableDataInfo qryManualDispatchResList(ResPersonBasicQueryDTO queryDto) {
        // Assert.notNull(queryDto.getSiteId(), "查询数据的站点编码参数不能为空,请核对请求参数!");
        startPage();
        List<ResPersonBasicVO> list = dispatchTaskMgrService.qryManualDispatchResList(queryDto);
        return getDataTable(list);
    }

    /**
     * 5.2.1、【任务调整与下发】任务列表
     */
    @GetMapping("/qryTaskList")
    public TableDataInfo qryTaskList(ScheduTaskInfoQueryDTO queryDto) {
        startPage();
        List<ScheduTaskInfoVO> list = dispatchTaskMgrService.qryTaskList(queryDto);
        return getDataTable(list);
    }

    /**
     * 5.2.1、【任务调整与下发】任务详情
     */
    @GetMapping("/qryTaskDetail")
    public AjaxResult qryTaskDetail(String id) {
        Assert.notNull(id, "查询任务详情数据的id参数不能为空,请核对请求参数!");
        return success(dispatchTaskMgrService.qryTaskDetail(id));
    }

    /**
     * 5.2.2、【任务调整与下发】任务调整
     */
    @PostMapping("/updateTaskInfo")
    public AjaxResult updateTaskInfo(@RequestBody ScheduTaskInfoDTO dto) {
        return toAjax(dispatchTaskMgrService.updateTaskInfo(dto));
    }

    /**
     * 5.2.3、【任务调整与下发】任务推送活动系统【支持批量】
     */
    @GetMapping("/pushActivity")
    public AjaxResult pushActivity(String id) {
        Assert.notNull(id, "任务id参数不能为空,请核对请求参数!");
        return success(dispatchTaskMgrService.pushActivity(id));
    }

    /**
     * 5.3.1、【重要任务审核】重要任务任务列表
     */
    @GetMapping("/qryImportantTaskList")
    public TableDataInfo qryImportantTaskList(ScheduTaskInfoQueryDTO queryDto) {
        startPage();
        List<ScheduTaskInfoVO> list = new ArrayList<>();
        return getDataTable(list);
    }

    /**
     * 5.5.1、【可视化】站点任务统计总览信息 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/qryTaskStaticBySite")
    public AjaxResult qryTaskStaticBySite(String siteId) {
        Assert.notNull(siteId, "查询数据的站点id参数不能为空,请核对请求参数!");
        return success(dispatchTaskMgrService.qryTaskStaticBySite(siteId));
    }

    /**
     * 5.5.2、【可视化】站点任务列表（进行中和未开始任务） @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/qryTaskListBySite")
    public TableDataInfo qryTaskListBySite(String siteId) {
        Assert.notNull(siteId, "查询数据的站点id参数不能为空,请核对请求参数!");
        startPage();
        List<SiteTaskListVO> list = dispatchTaskMgrService.qryTaskListBySite(siteId);
        return getDataTable(list);
    }

    /**
     * 5.5.3、【可视化】站点某段时间内的任务统计信息 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/qryPeriodTaskStaticBySite")
    public AjaxResult qryPeriodTaskStaticBySite(TaskStaticQueryDTO queryDTO) {
        return success(dispatchTaskMgrService.qryPeriodTaskStaticBySite(queryDTO));
    }

    /**
     * 5.5.4、【可视化】站点运维甘特图数据 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/qryCompanyWorkGanttData")
    public AjaxResult qryCompanyWorkGanttData(TaskStaticQueryDTO queryDTO) {
        return success(dispatchTaskMgrService.qryCompanyWorkGanttData(queryDTO));
    }
}
