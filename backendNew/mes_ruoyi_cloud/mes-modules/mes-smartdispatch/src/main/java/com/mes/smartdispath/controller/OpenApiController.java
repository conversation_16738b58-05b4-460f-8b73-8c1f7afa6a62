package com.mes.smartdispath.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.smartdispath.config.aspect.OperationLogEndpoint;
import com.mes.smartdispath.domain.ScheduAlgorithmInfo;
import com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord;
import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.openApi.AddMonitorQualityTargetApiDTO;
import com.mes.smartdispath.domain.openApi.AddPlanInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.AddTaskInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.ScheduPlanInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.ScheduTaskInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.UpdatePlanExtendApiDTO;
import com.mes.smartdispath.domain.vo.ScheduAlgruleConfigVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.enums.OperationTypeEnum;
import com.mes.smartdispath.service.IOpenApiService;

/**
 * 开放接口 Controller
 *
 * <AUTHOR>
 * @Date 2025/7/9
 */
@RestController
@RequestMapping("/openapi")
public class OpenApiController extends BaseController {
    @Autowired
    private IOpenApiService openApiService;

    /**
     * 9.1.1、任务状态更新接口
     */
    @PostMapping("/updateTaskStatus")
    @OperationLogEndpoint(module = "任务状态更新接口", operationType = OperationTypeEnum.UPDATE, operationContent = "任务状态更新接口")
    public Boolean updateTaskStatus(@RequestBody ScheduTaskInfoOpenApiDTO taskInfo) {
        return openApiService.updateTaskStatus(taskInfo);
    }

    /**
     * 9.1.2、计划状态更新接口
     */
    @PostMapping("/updatePlanStatus")
    @OperationLogEndpoint(module = "计划状态更新接口", operationType = OperationTypeEnum.UPDATE, operationContent = "计划状态更新接口")
    public Boolean updatePlanStatus(@RequestBody ScheduPlanInfoOpenApiDTO planInfo) {
        return openApiService.updatePlanStatus(planInfo);
    }

    /**
     * 9.1.3、监测活动查询接口
     */
    @GetMapping("/getActivityInfoList")
    @OperationLogEndpoint(module = "监测活动查询接口", operationType = OperationTypeEnum.QUERY, operationContent = "监测活动查询接口")
    public List<ScheduMonitorActivityInfo> getActivityInfoList(
        @RequestParam(name = "businessType", required = false) String businessType,
        @RequestParam(name = "siteType", required = false) String siteType) {
        return openApiService.getActivityInfoList(businessType, siteType);
    }

    /**
     * 9.1.4、调度计划创建接口
     */
    @PostMapping("/addPlanInfo")
    @OperationLogEndpoint(module = "调度计划创建接口", operationType = OperationTypeEnum.CREATE, operationContent = "计划状态更新接口")
    public Boolean addPlanInfo(@RequestBody AddPlanInfoOpenApiDTO dto) {
        return openApiService.addPlanInfo(dto);
    }

    /**
     * 9.1.5、调度计划查询接口
     */
    @GetMapping("/getPlanInfoList")
    @OperationLogEndpoint(module = "调度计划查询接口", operationType = OperationTypeEnum.QUERY, operationContent = "调度计划查询接口")
    public List<ScheduPlanInfoVO> getPlanInfoList(ScheduPlanInfoQueryDTO queryDto) {
        return openApiService.getPlanInfoList(queryDto);
    }

    /**
     * 9.1.6、天气告警中止计划
     */
    @GetMapping("/suspendPlanByWeather")
    @OperationLogEndpoint(module = "天气告警中止计划", operationType = OperationTypeEnum.UPDATE, operationContent = "天气告警中止计划")
    public Boolean suspendPlanByWeather() {
        return false;
    }

    /**
     * 9.1.7、质量目标新增接口
     */
    @PostMapping("/batchAddQualityTarget")
    @OperationLogEndpoint(module = "质量目标新增接口", operationType = OperationTypeEnum.CREATE, operationContent = "质量目标新增接口")
    public Boolean batchAddQualityTarget(@RequestBody AddMonitorQualityTargetApiDTO dto) {
        return openApiService.batchAddQualityTarget(dto);
    }

    /**
     * 9.1.8、调度任务创建接口
     */
    @PostMapping("/addTaskInfo")
    @OperationLogEndpoint(module = "调度任务创建接口", operationType = OperationTypeEnum.CREATE, operationContent = "调度任务创建接口")
    public Boolean addTaskInfo(@RequestBody AddTaskInfoOpenApiDTO dto) {
        return openApiService.addTaskInfo(dto);
    }

    /**
     * 9.1.9、批量更新计划扩展信息
     */
    @PostMapping("/batchUpdatePlanExtend")
    @OperationLogEndpoint(module = "批量更新计划扩展信息", operationType = OperationTypeEnum.UPDATE,
        operationContent = "调度任务创建接口")
    public Boolean batchUpdatePlanExtend(@RequestBody UpdatePlanExtendApiDTO dto) {
        return openApiService.batchUpdatePlanExtend(dto);
    }

    /**
     * 9.1.10、查询算法基础信息列表
     */
    @GetMapping("/getAlgorithmInfoList")
    @OperationLogEndpoint(module = "查询算法基础信息列表", operationType = OperationTypeEnum.QUERY,
        operationContent = "查询算法基础信息列表")
    public List<ScheduAlgorithmInfo> getAlgorithmInfoList() {
        return openApiService.getAlgorithmInfoList();
    }

    /**
     * 9.1.11、查询算法规则列表
     */
    @GetMapping("/getAlgruleConfigList")
    @OperationLogEndpoint(module = "查询算法规则列表", operationType = OperationTypeEnum.QUERY, operationContent = "查询算法规则列表")
    public List<ScheduAlgruleConfigVO> getAlgruleConfigList() {
        return openApiService.getAlgruleConfigList();
    }

    /**
     * 9.1.12、批量新增算法运行记录信息
     */
    @PostMapping("/batchAddAlgorithmInvokeRecord")
    @OperationLogEndpoint(module = "批量新增算法运行记录信息", operationType = OperationTypeEnum.CREATE,
        operationContent = "批量新增算法运行记录信息")
    public Boolean batchAddAlgorithmInvokeRecord(@RequestBody List<ScheduAlgorithmInvokeRecord> dto) {
        return openApiService.batchAddAlgorithmInvokeRecord(dto);
    }
}
