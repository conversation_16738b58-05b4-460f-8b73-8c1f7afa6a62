package com.mes.smartdispath.domain.utils;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.mes.smartdispath.domain.TaskDynamicDO;

import cn.hutool.core.lang.Singleton;
import lombok.extern.slf4j.Slf4j;
import tech.powerjob.client.PowerJobClient;
import tech.powerjob.common.enums.ExecuteType;
import tech.powerjob.common.enums.ProcessorType;
import tech.powerjob.common.enums.TimeExpressionType;
import tech.powerjob.common.model.LifeCycle;
import tech.powerjob.common.request.http.SaveJobInfoRequest;
import tech.powerjob.common.response.ResultDTO;

@Slf4j
@Component
public class DynamicTaskUtils {
    @Value("${powerjobclient.address:}")
    private String address;

    @Value("${powerjobclient.appName:}")
    private String appName;

    @Value("${powerjobclient.appSecret:}")
    private String appSecret;

    public PowerJobClient newPowerJobClient() {
        return Singleton.get(PowerJobClient.class, address, appName, appSecret);
    }

    public Long saveJob(TaskDynamicDO data) {
        PowerJobClient client = newPowerJobClient();

        SaveJobInfoRequest request = new SaveJobInfoRequest();
        request.setId(data.getJobId());
        request.setJobName(data.getJobName());
        request.setJobDescription(data.getJobName());
        request.setJobParams(data.getJobParams());
        request.setTimeExpressionType(TimeExpressionType.valueOf(data.getTimeExpressionType()));
        request.setProcessorInfo("tech.powerjob.official.processors.impl.HttpProcessor");
        request.setLifeCycle(LifeCycle.parse(data.getLifeCycle()));
        request.setTimeExpression(data.getTimeExpression());
        request.setEnable(data.getJobEnable() == 1);

        request.setProcessorType(ProcessorType.BUILT_IN);
        request.setExecuteType(ExecuteType.STANDALONE);

        log.info("request params jobId: {}", data.getJobId());
        ResultDTO<Long> resultDTO = client.saveJob(request);

        return resultDTO.getData();
    }

    public String newJobParams(String route, TaskDynamicDO task) {
        Map<String, String> headers = new HashMap<>();
        headers.put("access_token", "test");

        Map<String, Object> data = new HashMap<>();
        data.put("headers", headers);
        data.put("method", "GET");
        data.put("url",
            String.format(route + "?bizCode=%s&bizId=%s&id=%d", task.getBizCode(), task.getBizId(), task.getId()));
        return JSON.toJSONString(data);
    }

    @Transactional(rollbackFor = Exception.class)
    public TaskDynamicDO createSingleJob(TaskDynamicDO task) {
        task.setState("A");

        // 生成 job参数
        // task.setJobParams(newJobParams(task.getJobParams(), task));
        // 保存到 PowerJob
        Long jobId = saveJob(task);

        // 回写更新
        task.setJobId(jobId);

        return task;
    }

    public ResultDTO deleteJob(Long jobId) {
        PowerJobClient client = newPowerJobClient();
        ResultDTO<Void> result = client.deleteJob(jobId);
        return result;
    }

    public ResultDTO enableJob(Long jobId) {
        PowerJobClient client = newPowerJobClient();
        ResultDTO<Void> result = client.enableJob(jobId);

        return result;
    }

    public ResultDTO disableJob(Long jobId) {
        PowerJobClient client = newPowerJobClient();
        ResultDTO<Void> result = client.disableJob(jobId);

        return result;
    }
}
