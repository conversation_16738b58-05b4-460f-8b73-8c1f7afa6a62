package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ScheduMonitorQuantityTarget;
import com.mes.smartdispath.domain.dto.ScheduMonitorQuantityTargetQueryDTO;
import com.mes.smartdispath.domain.vo.QuantityTargetYearCollectVO;
import com.mes.smartdispath.domain.vo.ScheduMonitorQuantityTargetVO;

/**
 * 年度监测活动数量目标Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduMonitorQuantityTargetMapper {
    /**
     * 按业务分类汇总的目标值
     *
     * @param statYear
     * @return
     */
    public List<ScheduMonitorQuantityTarget> selectBizCollectQuantityTarget(@Param("statYear") String statYear);

    /**
     * 查询年度监测活动数量汇总目标列表
     *
     * @param
     * @return
     */
    public List<QuantityTargetYearCollectVO> selectQuantityTargetYearCollect(@Param("businessType") String businessType,
        @Param("monthlyActivityType") String monthlyActivityType,
        @Param("weeklyActivityType") String weeklyActivityType, @Param("curMonthYear") int curMonthYear,
        @Param("lastMonthYear") int lastMonthYear);

    /**
     * 查询符合条件的年度监测活动数量目标数量
     *
     * @param queryDto 年度监测活动数量目标
     * @return 数量
     */
    public int selectScheduMonitorQuantityTargetCount(ScheduMonitorQuantityTargetQueryDTO queryDto);

    /**
     * 查询年度监测活动数量目标
     * 
     * @param id 年度监测活动数量目标主键
     * @return 年度监测活动数量目标
     */
    public ScheduMonitorQuantityTarget selectScheduMonitorQuantityTargetById(String id);

    /**
     * 批量新增年度监测活动数量目标
     *
     * @param targetList 年度监测活动数量目标数组
     * @return 结果
     */
    public int batchInsertScheduMonitorQuantityTarget(List<ScheduMonitorQuantityTarget> targetList);

    /**
     * 查询年度监测活动数量目标列表
     * 
     * @param queryDto 年度监测活动数量目标
     * @return 年度监测活动数量目标集合
     */
    public List<ScheduMonitorQuantityTargetVO> selectScheduMonitorQuantityTargetList(
        ScheduMonitorQuantityTargetQueryDTO queryDto);

    /**
     * 查询汇总的年度监测活动数量目标列表-全国
     *
     * @param queryDto 年度监测活动数量目标
     * @return 年度监测活动数量目标集合
     */
    public List<ScheduMonitorQuantityTargetVO> selectNationwideCollectScheduMonitorQuantityTargetList(
        ScheduMonitorQuantityTargetQueryDTO queryDto);

    /**
     * 查询汇总的年度监测活动数量目标列表-省份
     *
     * @param queryDto 年度监测活动数量目标
     * @return 年度监测活动数量目标集合
     */
    public List<ScheduMonitorQuantityTargetVO> selectProvinceCollectScheduMonitorQuantityTargetList(
        ScheduMonitorQuantityTargetQueryDTO queryDto);

    /**
     * 新增年度监测活动数量目标
     * 
     * @param scheduMonitorQuantityTarget 年度监测活动数量目标
     * @return 结果
     */
    public int insertScheduMonitorQuantityTarget(ScheduMonitorQuantityTarget scheduMonitorQuantityTarget);

    /**
     * 修改年度监测活动数量目标
     * 
     * @param scheduMonitorQuantityTarget 年度监测活动数量目标
     * @return 结果
     */
    public int updateScheduMonitorQuantityTarget(ScheduMonitorQuantityTarget scheduMonitorQuantityTarget);

    /**
     * 删除年度监测活动数量目标
     * 
     * @param id 年度监测活动数量目标主键
     * @return 结果
     */
    public int deleteScheduMonitorQuantityTargetById(String id);

    /**
     * 批量删除年度监测活动数量目标
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduMonitorQuantityTargetByIds(String[] ids);
}
