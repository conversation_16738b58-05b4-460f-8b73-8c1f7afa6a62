package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 中国省市区域代码对象 tb_res_area_info
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public class ResAreaInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 区域代码（6位） */
    @Excel(name = "区域代码", readConverterExp = "6=位")
    private String areaCode;

    /** 区域名称 */
    @Excel(name = "区域名称")
    private String areaName;

    /** 父级区域代码 */
    @Excel(name = "父级区域代码")
    private String parentCode;

    /** 区域类型（1:省/直辖市/自治区，2:市/地区/自治州） */
    @Excel(name = "区域类型", readConverterExp = "1=:省/直辖市/自治区，2:市/地区/自治州")
    private String areaType;

    /** 状态（1:有效，0:无效） */
    @Excel(name = "状态", readConverterExp = "1=:有效，0:无效")
    private String status;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setAreaType(String areaType) {
        this.areaType = areaType;
    }

    public String getAreaType() {
        return areaType;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("areaCode", getAreaCode()).append("areaName", getAreaName()).append("parentCode", getParentCode())
            .append("areaType", getAreaType()).append("status", getStatus()).append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime()).toString();
    }
}
