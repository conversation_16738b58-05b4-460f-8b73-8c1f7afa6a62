package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduPlanAssorule;
import com.mes.smartdispath.domain.dto.ScheduPlanAssoruleQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduPlanAssoruleVO;

/**
 * 伴生计划规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduPlanAssoruleMapper {
    /**
     * 查询伴生计划规则列表
     *
     * @param queryDTO 查询参数
     * @return 伴生计划规则集合
     */
    public List<ScheduPlanAssoruleVO> selectScheduPlanAssoruleListByMainRuleId(ScheduPlanAssoruleQueryDTO queryDTO);

    /**
     * 查询伴生计划规则
     * 
     * @param id 伴生计划规则主键
     * @return 伴生计划规则
     */
    public ScheduPlanAssorule selectScheduPlanAssoruleById(String id);

    /**
     * 查询伴生计划规则列表
     * 
     * @param scheduPlanAssorule 伴生计划规则
     * @return 伴生计划规则集合
     */
    public List<ScheduPlanAssorule> selectScheduPlanAssoruleList(ScheduPlanAssorule scheduPlanAssorule);

    /**
     * 新增伴生计划规则
     * 
     * @param scheduPlanAssorule 伴生计划规则
     * @return 结果
     */
    public int insertScheduPlanAssorule(ScheduPlanAssorule scheduPlanAssorule);

    /**
     * 批量新增伴生计划规则
     *
     * @param scheduPlanAssoruleList 伴生计划规则List
     * @return 结果
     */
    public int batchInsertScheduPlanAssorule(List<ScheduPlanAssorule> scheduPlanAssoruleList);

    /**
     * 修改伴生计划规则
     * 
     * @param scheduPlanAssorule 伴生计划规则
     * @return 结果
     */
    public int updateScheduPlanAssorule(ScheduPlanAssorule scheduPlanAssorule);

    /**
     * 根据主规则id修改伴生计划规则
     *
     * @param scheduPlanAssorule 计划规则
     * @return 结果
     */
    public int updateByMainRuleId(ScheduPlanAssorule scheduPlanAssorule);

    /**
     * 批量修改伴生计划规则
     *
     * @param scheduPlanAssoruleList 伴生计划规则List
     * @return 结果
     */
    public int batchUpdateScheduPlanAssorule(List<ScheduPlanAssorule> scheduPlanAssoruleList);

    /**
     * 删除伴生计划规则
     * 
     * @param id 伴生计划规则主键
     * @return 结果
     */
    public int deleteScheduPlanAssoruleById(String id);

    /**
     * 批量删除伴生计划规则
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduPlanAssoruleByIds(String[] ids);
}
