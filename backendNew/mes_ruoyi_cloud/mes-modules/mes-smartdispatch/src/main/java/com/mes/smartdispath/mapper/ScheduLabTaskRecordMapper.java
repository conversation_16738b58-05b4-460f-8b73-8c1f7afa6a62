package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduLabTaskRecord;

/**
 * 发送实验室的任务信息记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduLabTaskRecordMapper {
    /**
     * 查询发送实验室的任务信息记录
     * 
     * @param id 发送实验室的任务信息记录主键
     * @return 发送实验室的任务信息记录
     */
    public ScheduLabTaskRecord selectScheduLabTaskRecordById(String id);

    /**
     * 查询发送实验室的任务信息记录列表
     * 
     * @param scheduLabTaskRecord 发送实验室的任务信息记录
     * @return 发送实验室的任务信息记录集合
     */
    public List<ScheduLabTaskRecord> selectScheduLabTaskRecordList(ScheduLabTaskRecord scheduLabTaskRecord);

    /**
     * 新增发送实验室的任务信息记录
     * 
     * @param scheduLabTaskRecord 发送实验室的任务信息记录
     * @return 结果
     */
    public int insertScheduLabTaskRecord(ScheduLabTaskRecord scheduLabTaskRecord);

    /**
     * 修改发送实验室的任务信息记录
     * 
     * @param scheduLabTaskRecord 发送实验室的任务信息记录
     * @return 结果
     */
    public int updateScheduLabTaskRecord(ScheduLabTaskRecord scheduLabTaskRecord);

    /**
     * 删除发送实验室的任务信息记录
     * 
     * @param id 发送实验室的任务信息记录主键
     * @return 结果
     */
    public int deleteScheduLabTaskRecordById(String id);

    /**
     * 批量删除发送实验室的任务信息记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduLabTaskRecordByIds(String[] ids);
}
