package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ResSiteInspectionItem;

/**
 * 点位检测项(风险检测)Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface ResSiteInspectionItemMapper {
    /**
     * 查询最新的一条点位检测项(风险检测)
     *
     * @param siteId 站点Id
     * @return 点位检测项(风险检测)
     */
    public ResSiteInspectionItem selectInspectionItemBySiteId(String siteId);

    /**
     * 查询点位检测项(风险检测)
     *
     * @param id 点位检测项(风险检测)主键
     * @return 点位检测项(风险检测)
     */
    public ResSiteInspectionItem selectResSiteInspectionItemById(String id);

    /**
     * 查询点位检测项(风险检测)列表
     *
     * @param resSiteInspectionItem 点位检测项(风险检测)
     * @return 点位检测项(风险检测)集合
     */
    public List<ResSiteInspectionItem> selectResSiteInspectionItemList(ResSiteInspectionItem resSiteInspectionItem);

    /**
     * 新增点位检测项(风险检测)
     *
     * @param resSiteInspectionItem 点位检测项(风险检测)
     * @return 结果
     */
    public int insertResSiteInspectionItem(ResSiteInspectionItem resSiteInspectionItem);

    /**
     * 批量新增点位检测项(风险检测)
     *
     * @param resSiteInspectionItemList 点位检测项(风险检测)List
     * @return 结果
     */
    public int batchInsertResSiteInspectionItem(List<ResSiteInspectionItem> resSiteInspectionItemList);

    /**
     * 修改点位检测项(风险检测)
     *
     * @param resSiteInspectionItem 点位检测项(风险检测)
     * @return 结果
     */
    public int updateResSiteInspectionItem(ResSiteInspectionItem resSiteInspectionItem);

    /**
     * 批量修改点位检测项(风险检测)
     *
     * @param resSiteInspectionItemList 点位检测项(风险检测)List
     * @return 结果
     */
    public int batchUpdateResSiteInspectionItem(List<ResSiteInspectionItem> resSiteInspectionItemList);

    /**
     * 删除点位检测项(风险检测)
     *
     * @param id 点位检测项(风险检测)主键
     * @return 结果
     */
    public int deleteResSiteInspectionItemById(String id);

    /**
     * 批量删除点位检测项(风险检测)
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteResSiteInspectionItemByIds(String[] ids);
}
