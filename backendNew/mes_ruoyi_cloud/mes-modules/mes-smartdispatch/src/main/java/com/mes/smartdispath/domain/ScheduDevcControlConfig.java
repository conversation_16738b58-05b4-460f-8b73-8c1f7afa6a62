package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 设备反控指令配置对象 tb_schedu_devc_control_config
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduDevcControlConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 设备编码 */
    @Excel(name = "设备编码")
    private String devcCode;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String devcName;

    /** 业务分类（水/气） */
    @Excel(name = "业务分类", readConverterExp = "水=/气")
    private String businessType;

    /** 站点ID */
    @Excel(name = "站点ID")
    private String siteId;

    /** 站点名称（如：断面、气自动站、水自动站） */
    @Excel(name = "站点名称", readConverterExp = "如=：断面、气自动站、水自动站")
    private String siteName;

    /** 省名称 */
    @Excel(name = "省名称")
    private String provinceName;

    /** 省行政区划编码 */
    @Excel(name = "省行政区划编码")
    private String provinceCode;

    /** 市名称 */
    @Excel(name = "市名称")
    private String cityName;

    /** 市行政区划编码 */
    @Excel(name = "市行政区划编码")
    private String cityCode;

    /** 反控指令内容（如JSON格式、协议字符串等） */
    @Excel(name = "反控指令内容", readConverterExp = "如=JSON格式、协议字符串等")
    private String controlCommand;

    /** 下发类型：立刻发 / 延时发 / 定时发 */
    @Excel(name = "下发类型：立刻发 / 延时发 / 定时发")
    private String dispatchType;

    /** 运行频率：1h/次 2h/次 3h/次 4h/次 */
    @Excel(name = "运行频率：1h/次 2h/次 3h/次 4h/次")
    private String frequency;

    /** 启用状态：启用 / 停用 */
    @Excel(name = "启用状态：启用 / 停用")
    private String status;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setDevcCode(String devcCode) {
        this.devcCode = devcCode;
    }

    public String getDevcCode() {
        return devcCode;
    }

    public void setDevcName(String devcName) {
        this.devcName = devcName;
    }

    public String getDevcName() {
        return devcName;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setControlCommand(String controlCommand) {
        this.controlCommand = controlCommand;
    }

    public String getControlCommand() {
        return controlCommand;
    }

    public void setDispatchType(String dispatchType) {
        this.dispatchType = dispatchType;
    }

    public String getDispatchType() {
        return dispatchType;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("devcCode", getDevcCode()).append("devcName", getDevcName())
            .append("businessType", getBusinessType()).append("siteId", getSiteId()).append("siteName", getSiteName())
            .append("provinceName", getProvinceName()).append("provinceCode", getProvinceCode())
            .append("cityName", getCityName()).append("cityCode", getCityCode())
            .append("controlCommand", getControlCommand()).append("dispatchType", getDispatchType())
            .append("frequency", getFrequency()).append("status", getStatus()).append("createBy", getCreateBy())
            .append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("tenantId", getTenantId()).toString();
    }
}
