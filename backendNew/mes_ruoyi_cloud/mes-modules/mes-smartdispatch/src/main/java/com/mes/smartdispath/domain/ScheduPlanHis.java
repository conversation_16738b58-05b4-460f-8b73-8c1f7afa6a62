package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 调度计划信息历史对象 tb_schedu_plan_his
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduPlanHis extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 计划ID */
    @Excel(name = "计划ID")
    private String planId;

    /** 计划code，全局唯一标识符（例如：PLAN20250526001） */
    @Excel(name = "计划code，全局唯一标识符", readConverterExp = "例=如：PLAN20250526001")
    private String planCode;

    /** 计划名称 */
    @Excel(name = "计划名称")
    private String planName;

    /** 计划调度状态：1-暂存、2-待调度、3-已调度 */
    @Excel(name = "计划调度状态：1-暂存、2-待调度、3-已调度")
    private String scheduStatus;

    /** 计划执行状态：4-执行中，5-已完成，6-已退回，7-已中止 */
    @Excel(name = "计划执行状态：4-执行中，5-已完成，6-已退回，7-已中止")
    private String executeStatus;

    /** 计划开始时间 */
    @Excel(name = "计划开始时间")
    private String planStartTime;

    /** 计划结束时间 */
    @Excel(name = "计划结束时间")
    private String planEndTime;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 设备型号 */
    @Excel(name = "设备型号")
    private String devcModel;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getPlanId() {
        return planId;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setScheduStatus(String scheduStatus) {
        this.scheduStatus = scheduStatus;
    }

    public String getScheduStatus() {
        return scheduStatus;
    }

    public void setExecuteStatus(String executeStatus) {
        this.executeStatus = executeStatus;
    }

    public String getExecuteStatus() {
        return executeStatus;
    }

    public void setPlanStartTime(String planStartTime) {
        this.planStartTime = planStartTime;
    }

    public String getPlanStartTime() {
        return planStartTime;
    }

    public void setPlanEndTime(String planEndTime) {
        this.planEndTime = planEndTime;
    }

    public String getPlanEndTime() {
        return planEndTime;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setDevcModel(String devcModel) {
        this.devcModel = devcModel;
    }

    public String getDevcModel() {
        return devcModel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("planId", getPlanId()).append("planCode", getPlanCode()).append("planName", getPlanName())
            .append("scheduStatus", getScheduStatus()).append("executeStatus", getExecuteStatus())
            .append("planStartTime", getPlanStartTime()).append("planEndTime", getPlanEndTime())
            .append("remark", getRemark()).append("createTime", getCreateTime()).append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime()).append("updateBy", getUpdateBy()).append("status", getStatus())
            .append("tenantId", getTenantId()).append("devcModel", getDevcModel()).toString();
    }
}
