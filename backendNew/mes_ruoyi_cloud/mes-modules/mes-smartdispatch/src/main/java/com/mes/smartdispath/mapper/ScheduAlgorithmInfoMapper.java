package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduAlgorithmInfo;

/**
 * 清华调度算法基础信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduAlgorithmInfoMapper {
    /**
     * 查询清华调度算法基础信息
     * 
     * @param id 清华调度算法基础信息主键
     * @return 清华调度算法基础信息
     */
    public ScheduAlgorithmInfo selectScheduAlgorithmInfoById(String id);

    /**
     * 查询清华调度算法基础信息列表
     * 
     * @param scheduAlgorithmInfo 清华调度算法基础信息
     * @return 清华调度算法基础信息集合
     */
    public List<ScheduAlgorithmInfo> selectScheduAlgorithmInfoList(ScheduAlgorithmInfo scheduAlgorithmInfo);

    /**
     * 新增清华调度算法基础信息
     * 
     * @param scheduAlgorithmInfo 清华调度算法基础信息
     * @return 结果
     */
    public int insertScheduAlgorithmInfo(ScheduAlgorithmInfo scheduAlgorithmInfo);

    /**
     * 修改清华调度算法基础信息
     * 
     * @param scheduAlgorithmInfo 清华调度算法基础信息
     * @return 结果
     */
    public int updateScheduAlgorithmInfo(ScheduAlgorithmInfo scheduAlgorithmInfo);

    /**
     * 删除清华调度算法基础信息
     * 
     * @param id 清华调度算法基础信息主键
     * @return 结果
     */
    public int deleteScheduAlgorithmInfoById(String id);

    /**
     * 批量删除清华调度算法基础信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduAlgorithmInfoByIds(String[] ids);
}
