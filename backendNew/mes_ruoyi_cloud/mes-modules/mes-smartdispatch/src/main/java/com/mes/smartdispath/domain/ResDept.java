package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 组织对象 tb_res_dept
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public class ResDept extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 部门id */
    private String deptId;

    /** 监测要素(字典) */
    @Excel(name = "监测要素(字典)")
    private String monitoringElement;

    /** 父部门id */
    @Excel(name = "父部门id")
    private String parentId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 省市环保填区域编码，第三方填社会信用代码 */
    @Excel(name = "省市环保填区域编码，第三方填社会信用代码")
    private String deptCode;

    /** 部门类型（1总站2省环保3市环保4第三方） */
    @Excel(name = "部门类型", readConverterExp = "1=总站2省环保3市环保4第三方")
    private String deptType;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private String orderNum;

    /** 负责人 */
    @Excel(name = "负责人")
    private String leader;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 部门状态（0正常 1停用） */
    @Excel(name = "部门状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 祖级列表 */
    @Excel(name = "祖级列表")
    private String ancestors;

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setMonitoringElement(String monitoringElement) {
        this.monitoringElement = monitoringElement;
    }

    public String getMonitoringElement() {
        return monitoringElement;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptType(String deptType) {
        this.deptType = deptType;
    }

    public String getDeptType() {
        return deptType;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setLeader(String leader) {
        this.leader = leader;
    }

    public String getLeader() {
        return leader;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail() {
        return email;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getAncestors() {
        return ancestors;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("deptId", getDeptId())
            .append("monitoringElement", getMonitoringElement()).append("parentId", getParentId())
            .append("deptName", getDeptName()).append("deptCode", getDeptCode()).append("deptType", getDeptType())
            .append("orderNum", getOrderNum()).append("leader", getLeader()).append("phone", getPhone())
            .append("email", getEmail()).append("status", getStatus()).append("createBy", getCreateBy())
            .append("createTime", getCreateTime()).append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime()).append("delFlag", getDelFlag()).append("ancestors", getAncestors())
            .toString();
    }
}
