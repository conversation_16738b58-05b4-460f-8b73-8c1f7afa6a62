package com.mes.smartdispath.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.service.IBigScreenService;

/**
 * 大屏 Controller
 *
 * <AUTHOR>
 * @date 2025/7/21
 */
@RestController
@RequestMapping("/homepage")
public class BigScreenController extends BaseController {
    @Autowired
    private IBigScreenService bigScreenService;

    /**
     * 汇总的数量目标统计
     */
    @GetMapping("/qryCollectQuantityTargetPerformance")
    public AjaxResult qryCollectQuantityTargetPerformance() {
        return success(bigScreenService.qryCollectQuantityTargetPerformance());
    }

    /**
     * 调度计划来源统计
     */
    @GetMapping("/qryPlanSourceStatic")
    public AjaxResult qryTodayTaskStatic(ScheduPlanInfoQueryDTO queryDTO) {
        return success(bigScreenService.qryPlanSourceStatic(queryDTO));
    }

    /**
     * 调度任务分布统计，按区域
     */
    @GetMapping("/qryRegionTaskCountStatic")
    public AjaxResult qryRegionTaskCountStatic(ScheduTaskInfoQueryDTO queryDTO) {
        return success(bigScreenService.qryRegionTaskCountStatic(queryDTO));
    }

    /**
     * 调度任务分布统计，按运维单位
     */
    @GetMapping("/qryMaintainUnitTaskCountStatic")
    public AjaxResult qryMaintainUnitTaskCountStatic(ScheduTaskInfoQueryDTO queryDTO) {
        return success(bigScreenService.qryMaintainUnitTaskCountStatic(queryDTO));
    }

    /**
     * 今日运维人员数量统计
     */
    @GetMapping("/qryTodayMaintainPersonStatic")
    public AjaxResult qryTodayMaintainPersonStatic(String businessType) {
        return success(bigScreenService.qryTodayMaintainPersonStatic(businessType));
    }

    /**
     * 统计当月的调度任务
     */
    @GetMapping("/qryCurMonthTaskStatic")
    public AjaxResult qryCurMonthTaskStatic(TaskStaticQueryDTO queryDTO) {
        return success(bigScreenService.qryCurMonthTaskStatic(queryDTO));
    }

    /**
     * 当月的调度任务列表
     */
    @GetMapping("/qryCurMonthTaskList")
    public AjaxResult qryCurMonthTaskList(TaskStaticQueryDTO queryDTO) {
        return success(bigScreenService.qryCurMonthTaskList(queryDTO));
    }

    /**
     * 调度效率统计
     */
    @GetMapping("/qryDispatchEfficiencyStatic")
    public AjaxResult qryDispatchEfficiencyStatic() {
        return success(bigScreenService.qryDispatchEfficiencyStatic());
    }
}
