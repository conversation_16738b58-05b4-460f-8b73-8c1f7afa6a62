package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 伴生计划规则对象 tb_schedu_plan_assorule
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduPlanAssorule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 伴生规则ID */
    @Excel(name = "伴生规则ID")
    private String ruleCode;

    /** 伴生规则名称 */
    @Excel(name = "伴生规则名称")
    private String ruleName;

    /** 主计划规则ID */
    @Excel(name = "主计划规则ID")
    private String mainRuleId;

    /** 业务分类（全部、水、气） */
    @Excel(name = "业务分类", readConverterExp = "全=部、水、气")
    private String businessType;

    /** 站点类型（标准字典如：监测断面、水自动站等） */
    @Excel(name = "站点类型", readConverterExp = "标=准字典如：监测断面、水自动站等")
    private String siteType;

    /** 监测活动大类（三级分类） */
    @Excel(name = "监测活动大类", readConverterExp = "三=级分类")
    private String activityType;

    /** 监测活动子类（四级分类） */
    @Excel(name = "监测活动子类", readConverterExp = "四=级分类")
    private String activitySubtype;

    /** 状态 (A有效，X无效) */
    @Excel(name = "状态 (A有效，X无效)")
    private String status;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setRuleCode(String ruleCode) {
        this.ruleCode = ruleCode;
    }

    public String getRuleCode() {
        return ruleCode;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setMainRuleId(String mainRuleId) {
        this.mainRuleId = mainRuleId;
    }

    public String getMainRuleId() {
        return mainRuleId;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType;
    }

    public String getSiteType() {
        return siteType;
    }

    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }

    public String getActivityType() {
        return activityType;
    }

    public void setActivitySubtype(String activitySubtype) {
        this.activitySubtype = activitySubtype;
    }

    public String getActivitySubtype() {
        return activitySubtype;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("ruleCode", getRuleCode()).append("ruleName", getRuleName()).append("mainRuleId", getMainRuleId())
            .append("businessType", getBusinessType()).append("siteType", getSiteType())
            .append("activityType", getActivityType()).append("activitySubtype", getActivitySubtype())
            .append("createTime", getCreateTime()).append("updateTime", getUpdateTime()).append("status", getStatus())
            .toString();
    }
}
