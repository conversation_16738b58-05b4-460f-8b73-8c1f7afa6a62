package com.mes.smartdispath.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <p>
 * Title:TaskDynamic
 * </p>
 * <p>
 * Description:描述：动态任务表 关联任务调度引擎【powerJob】 对象
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-10-12
 **/
@Data
@TableName("illp_task_dynamic")
public class TaskDynamicDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * ENERGY_RULE = 诊断规则 ENERGY_MODEL = 能耗模型 ENERGY_PLAN = 定额计划
     */
    private String bizCode;

    /**
     * 业务ID
     */
    private Integer bizId;

    /**
     * 任务ID
     */
    private Long jobId;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务参数
     */
    private String jobParams;

    /**
     * 业务参数
     */
    private String bizParams;

    /**
     * 时间表达式类型 CRON -> timeExpression填写 CRON 表达式 固定频率 FIXED_RATE -> timeExpression填写整数，单位毫秒 固定延迟 FIXED_DELAY->
     * timeExpression填写整数，单位毫秒 每日固定间隔 DAILY_TIME_INTERVAL timeExpression填写
     * {\"interval\":\"604800\",\"startTimeOfDay\":\"12:00:00\",\"intervalUnit\":\"SECONDS\",\"daysOfWeek\":[2],\"endTimeOfDay\":\"12:30:00\"}
     */
    private String timeExpressionType;

    /**
     * 时间表达式
     */
    private String timeExpression;

    /**
     * 任务有效期，例: {\"start\":1686499200000,\"end\":1688054400000}
     */
    private String lifeCycle;

    /**
     * 是否启用该任务 1 = 启用 0 不启用
     */
    private Integer jobEnable;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 状态 A - 可用 X - 不可用
     */
    private String state;

    /**
     * 备注
     */
    private String remark;
}