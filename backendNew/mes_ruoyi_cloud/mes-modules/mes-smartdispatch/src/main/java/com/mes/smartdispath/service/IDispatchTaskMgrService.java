package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.dto.ResPersonBasicQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoDTO;
import com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.MaintenanceWorkCalendarVO;
import com.mes.smartdispath.domain.vo.ResPersonBasicVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.domain.vo.ScheduTaskInfoVO;
import com.mes.smartdispath.domain.vo.SiteTaskListVO;
import com.mes.smartdispath.domain.vo.SiteTaskStaticVO;
import com.mes.smartdispath.domain.vo.TaskStaticVO;

/**
 * 调度任务管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IDispatchTaskMgrService {
    /**
     * 查询计划列表
     *
     * @param queryDto
     * @return
     */
    public List<ScheduPlanInfoVO> qryDispatchtaskPlanList(ScheduPlanInfoQueryDTO queryDto);

    /**
     * 手工分配任务
     *
     * @param dto
     * @return
     */
    public int manualDispatchPlan(ScheduTaskInfoDTO dto);

    /**
     * 立刻调用算法分配任务
     *
     * @param id
     * @return
     */
    public int callDispatchPlan(String id);

    /**
     * 查询手工分配任务的资源【人员】列表
     *
     * @param queryDto
     * @return
     */
    public List<ResPersonBasicVO> qryManualDispatchResList(ResPersonBasicQueryDTO queryDto);

    /**
     * 任务列表
     *
     * @param queryDto
     * @return
     */
    public List<ScheduTaskInfoVO> qryTaskList(ScheduTaskInfoQueryDTO queryDto);

    /**
     * 查询任务详情
     *
     * @param id
     * @return
     */
    public ScheduTaskInfoVO qryTaskDetail(String id);

    /**
     * 任务调整
     *
     * @param dto
     * @return
     */
    public int updateTaskInfo(ScheduTaskInfoDTO dto);

    /**
     * 任务推送活动系统
     *
     * @param id
     * @return
     */
    public int pushActivity(String id);

    /**
     * 站点任务统计信息
     * 
     * @param siteId 站点ID
     * @return 站点任务统计信息
     */
    public SiteTaskStaticVO qryTaskStaticBySite(String siteId);

    /**
     * 站点任务列表
     *
     * @param siteId 站点ID
     * @return 站点任务列表
     */
    public List<SiteTaskListVO> qryTaskListBySite(String siteId);

    /**
     * 站点某段时间内的任务统计信息
     *
     * @param queryDTO 参数
     * @return 站点任务统计信息
     */
    public TaskStaticVO qryPeriodTaskStaticBySite(TaskStaticQueryDTO queryDTO);

    /**
     * 站点运维甘特图数据
     *
     * @param queryDTO 参数
     * @return 站点运维甘特图数据
     */
    public List<MaintenanceWorkCalendarVO> qryCompanyWorkGanttData(TaskStaticQueryDTO queryDTO);
}
