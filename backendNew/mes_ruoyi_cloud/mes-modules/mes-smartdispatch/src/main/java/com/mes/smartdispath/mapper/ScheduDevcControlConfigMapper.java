package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduDevcControlConfig;

/**
 * 设备反控指令配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduDevcControlConfigMapper {
    /**
     * 查询设备反控指令配置
     * 
     * @param id 设备反控指令配置主键
     * @return 设备反控指令配置
     */
    public ScheduDevcControlConfig selectScheduDevcControlConfigById(String id);

    /**
     * 查询设备反控指令配置列表
     * 
     * @param scheduDevcControlConfig 设备反控指令配置
     * @return 设备反控指令配置集合
     */
    public List<ScheduDevcControlConfig> selectScheduDevcControlConfigList(
        ScheduDevcControlConfig scheduDevcControlConfig);

    /**
     * 新增设备反控指令配置
     * 
     * @param scheduDevcControlConfig 设备反控指令配置
     * @return 结果
     */
    public int insertScheduDevcControlConfig(ScheduDevcControlConfig scheduDevcControlConfig);

    /**
     * 修改设备反控指令配置
     * 
     * @param scheduDevcControlConfig 设备反控指令配置
     * @return 结果
     */
    public int updateScheduDevcControlConfig(ScheduDevcControlConfig scheduDevcControlConfig);

    /**
     * 删除设备反控指令配置
     * 
     * @param id 设备反控指令配置主键
     * @return 结果
     */
    public int deleteScheduDevcControlConfigById(String id);

    /**
     * 批量删除设备反控指令配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduDevcControlConfigByIds(String[] ids);
}
