package com.mes.smartdispath.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mes.common.core.utils.StringUtils;
import com.mes.smartdispath.constant.CommonConstant;
import com.mes.smartdispath.constant.PlanConstant;
import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.constant.TaskConstant;
import com.mes.smartdispath.domain.ScheduAlgorithmInfo;
import com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord;
import com.mes.smartdispath.domain.ScheduApprovalRecord;
import com.mes.smartdispath.domain.ScheduMonitorActivityInfo;
import com.mes.smartdispath.domain.ScheduMonitorQualityTarget;
import com.mes.smartdispath.domain.ScheduPlanExtend;
import com.mes.smartdispath.domain.ScheduPlanHis;
import com.mes.smartdispath.domain.ScheduPlanInfo;
import com.mes.smartdispath.domain.ScheduTaskExtend;
import com.mes.smartdispath.domain.ScheduTaskHis;
import com.mes.smartdispath.domain.ScheduTaskInfo;
import com.mes.smartdispath.domain.ScheduTaskPlanRel;
import com.mes.smartdispath.domain.dto.ScheduAlgruleConfigQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorActivityInfoQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduPlanInfoQueryDTO;
import com.mes.smartdispath.domain.openApi.AddMonitorQualityTargetApiDTO;
import com.mes.smartdispath.domain.openApi.AddPlanInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.AddTaskInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.ScheduPlanInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.ScheduTaskInfoOpenApiDTO;
import com.mes.smartdispath.domain.openApi.UpdatePlanExtendApiDTO;
import com.mes.smartdispath.domain.vo.ScheduAlgruleConfigVO;
import com.mes.smartdispath.domain.vo.ScheduPlanInfoVO;
import com.mes.smartdispath.domain.vo.ScheduTaskInfoDetailVO;
import com.mes.smartdispath.domain.vo.ScheduTaskInfoVO;
import com.mes.smartdispath.mapper.ScheduAlgorithmInfoMapper;
import com.mes.smartdispath.mapper.ScheduAlgorithmInvokeRecordMapper;
import com.mes.smartdispath.mapper.ScheduAlgruleConfigMapper;
import com.mes.smartdispath.mapper.ScheduApprovalRecordMapper;
import com.mes.smartdispath.mapper.ScheduMonitorActivityInfoMapper;
import com.mes.smartdispath.mapper.ScheduMonitorQualityTargetMapper;
import com.mes.smartdispath.mapper.ScheduPlanExtendMapper;
import com.mes.smartdispath.mapper.ScheduPlanHisMapper;
import com.mes.smartdispath.mapper.ScheduPlanInfoMapper;
import com.mes.smartdispath.mapper.ScheduTaskExtendMapper;
import com.mes.smartdispath.mapper.ScheduTaskHisMapper;
import com.mes.smartdispath.mapper.ScheduTaskInfoMapper;
import com.mes.smartdispath.mapper.ScheduTaskPlanRelMapper;
import com.mes.smartdispath.service.IOpenApiService;
import com.mes.smartdispath.service.ISavePlanService;

/**
 * 计划管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class OpenApiServiceImpl implements IOpenApiService {
    private static final Logger log = LoggerFactory.getLogger(OpenApiServiceImpl.class);

    @Autowired
    private ScheduPlanInfoMapper scheduPlanInfoMapper;

    @Autowired
    private ScheduPlanHisMapper scheduPlanHisMapper;

    @Autowired
    private ScheduTaskInfoMapper scheduTaskInfoMapper;

    @Autowired
    private ScheduTaskHisMapper scheduTaskHisMapper;

    @Autowired
    private ScheduTaskExtendMapper scheduTaskExtendMapper;

    @Autowired
    private ScheduTaskPlanRelMapper scheduTaskPlanRelMapper;

    @Autowired
    private ScheduMonitorActivityInfoMapper scheduMonitorActivityInfoMapper;

    @Autowired
    private ScheduApprovalRecordMapper scheduApprovalRecordMapper;

    @Autowired
    private ScheduMonitorQualityTargetMapper scheduMonitorQualityTargetMapper;

    @Autowired
    private ScheduPlanExtendMapper scheduPlanExtendMapper;

    @Autowired
    private ScheduAlgorithmInfoMapper scheduAlgorithmInfoMapper;

    @Autowired
    private ScheduAlgruleConfigMapper scheduAlgruleConfigMapper;

    @Autowired
    private ScheduAlgorithmInvokeRecordMapper scheduAlgorithmInvokeRecordMapper;

    @Autowired
    private ISavePlanService savePlanService;

    /**
     * 任务状态更新接口
     *
     * @param taskInfo
     * @return
     */
    @Override
    public Boolean updateTaskStatus(ScheduTaskInfoOpenApiDTO taskInfo) {
        log.info("任务状态更新，sysId:{}，operUser:{}, taskCode: {}", taskInfo.getSysId(), taskInfo.getOperUser(),
            taskInfo.getTaskCode());
        ScheduTaskInfo scheduTaskInfo = new ScheduTaskInfo();
        scheduTaskInfo.setTaskCode(taskInfo.getTaskCode());
        scheduTaskInfo.setTaskStatus(taskInfo.getTaskStatus());
        scheduTaskInfo.setUpdateBy(taskInfo.getOperUser());
        int count = scheduTaskInfoMapper.updateScheduTaskInfoByTaskCode(scheduTaskInfo);
        // 更新任务状态后，需要存一条历史数据
        ScheduTaskInfoVO taskInfoVO = scheduTaskInfoMapper.selectScheduTaskInfoByTaskCode(taskInfo.getTaskCode());
        if (null != taskInfoVO) {
            ScheduTaskHis scheduTaskHis = new ScheduTaskHis();
            BeanUtils.copyProperties(taskInfoVO, scheduTaskHis);
            scheduTaskHis.setTaskId(taskInfoVO.getId());
            int hisCount = scheduTaskHisMapper.insertScheduTaskHis(scheduTaskHis);
            log.info("保存任务历史信息条数:{}", hisCount);
        }
        return count > 0;
    }

    /**
     * 计划状态更新接口
     *
     * @param planInfo
     * @return
     */
    @Override
    public Boolean updatePlanStatus(ScheduPlanInfoOpenApiDTO planInfo) {
        log.info("计划状态更新，sysId:{}，operUser:{}, planCode: {}", planInfo.getSysId(), planInfo.getOperUser(),
            planInfo.getPlanCode());
        ScheduPlanInfo scheduPlanInfo = new ScheduPlanInfo();
        scheduPlanInfo.setPlanCode(planInfo.getPlanCode());
        scheduPlanInfo.setExecuteStatus(planInfo.getExecuteStatus());
        scheduPlanInfo.setUpdateBy(planInfo.getOperUser());
        int count = scheduPlanInfoMapper.updateScheduPlanInfoByPlanCode(scheduPlanInfo);
        // 更新计划状态后，需要存一条历史数据
        ScheduPlanInfoVO planInfoVO = scheduPlanInfoMapper.selectScheduPlanInfoByPlanCode(planInfo.getPlanCode());
        if (null != planInfoVO) {
            ScheduPlanHis scheduPlanHis = new ScheduPlanHis();
            BeanUtils.copyProperties(planInfoVO, scheduPlanHis);
            scheduPlanHis.setPlanId(planInfoVO.getId());
            int hisCount = scheduPlanHisMapper.insertScheduPlanHis(scheduPlanHis);
            log.info("保存计划历史信息数量:{}", hisCount);
        }
        return count > 0;
    }

    /**
     * 监测活动查询接口
     * 
     * @param businessType
     * @param siteType
     * @return
     */
    @Override
    public List<ScheduMonitorActivityInfo> getActivityInfoList(String businessType, String siteType) {
        ScheduMonitorActivityInfoQueryDTO scheduMonitorActivityInfo = new ScheduMonitorActivityInfoQueryDTO();
        if (StringUtils.isNotEmpty(businessType)) {
            scheduMonitorActivityInfo.setBusinessType(businessType);
        }
        if (StringUtils.isNotEmpty(siteType)) {
            scheduMonitorActivityInfo.setSiteType(siteType);
        }
        return scheduMonitorActivityInfoMapper.selectScheduMonitorActivityInfoList(scheduMonitorActivityInfo);
    }

    /**
     * 调度计划创建接口
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean addPlanInfo(AddPlanInfoOpenApiDTO dto) {
        log.info("计划创建，sysId:{}，operUser:{}", dto.getSysId(), dto.getOperUser());
        dto.getPlanInfo().setSubmitFlag(CommonConstant.SUBMIT_FLAG_1);
        int count = savePlanService.savePlan(dto.getPlanInfo(), dto.getOperUser());
        // TODO 调用调度计划创建接口，如果是不需要审核的计划，直接调活动接口推
        return count > 0;
    }

    /**
     * 计划信息查询
     *
     * @param queryDto
     * @return 计划信息列表
     */
    @Override
    public List<ScheduPlanInfoVO> getPlanInfoList(ScheduPlanInfoQueryDTO queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getCityCode())) {
            queryDto.setCityCodeArr(Arrays.asList(StringUtils.split(queryDto.getCityCode(), ",")));
        }
        queryDto.setBusinessTypeDictClassCode(SysDictConstant.BUSINESS_TYPE_CLASS_CODE);
        queryDto.setSiteTypeDictClassCode(SysDictConstant.SITE_TYPE_CLASS_CODE);
        // 查询已审核通过的
        queryDto.setApprovalStatus(CommonConstant.APPROVAL_STATUS_APPROVED);
        List<ScheduPlanInfoVO> result = scheduPlanInfoMapper.selectScheduPlanInfoList(queryDto);
        // 查询扩展信息
        if (result.size() > 0) {
            List<String> planIds = result.stream().map(ScheduPlanInfoVO::getId).collect(Collectors.toList());
            List<ScheduPlanExtend> extendList = scheduPlanExtendMapper.selectPlanExtendByPlanIds(planIds);
            // 转字典
            Map<String, ScheduPlanExtend> extendMap = extendList.stream()
                .collect(Collectors.toMap(ScheduPlanExtend::getPlanId, v -> v, (n1, n2) -> n1));
            result.forEach(v -> {
                v.setPlanExtendInfo(extendMap.get(v.getId()));
            });
        }
        return result;
    }

    /**
     * 质量目标新增接口
     *
     * @param dto
     * @return 计划信息列表
     */
    @Override
    public Boolean batchAddQualityTarget(AddMonitorQualityTargetApiDTO dto) {
        log.info("质量目标新增，sysId:{}，operUser:{}", dto.getSysId(), dto.getOperUser());
        List<ScheduMonitorQualityTarget> targetList = dto.getTargetList();
        targetList.forEach(v -> {
            v.setCreateBy(dto.getOperUser());
            v.setUpdateBy(dto.getOperUser());
        });
        int count = scheduMonitorQualityTargetMapper.batchInsertScheduMonitorQualityTarget(targetList);
        return count > 0;
    }

    /**
     * 调度任务创建接口
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addTaskInfo(AddTaskInfoOpenApiDTO dto) {
        log.info("任务创建，sysId:{}，operUser:{}", dto.getSysId(), dto.getOperUser());
        String operUser = dto.getOperUser();
        List<ScheduTaskInfoDetailVO> addDto = dto.getTaskInfos();
        // 要存储的任务信息
        List<ScheduTaskInfo> taskInfoList = new ArrayList<>();
        // 要存储的任务历史信息
        List<ScheduTaskHis> taskHisList = new ArrayList<>();
        // 要存储的任务和计划的关联关系
        List<ScheduTaskPlanRel> taskPlanRelList = new ArrayList<>();
        // 要存储的审核信息
        List<ScheduApprovalRecord> approvalRecordList = new ArrayList<>();
        // 要存储的任务拓展信息
        List<ScheduTaskExtend> extendsInfos = addDto.stream().flatMap(v -> v.getExtendsInfos().stream())
            .collect(Collectors.toList());
        for (ScheduTaskInfoDetailVO taskInfoDetail : addDto) {
            // 此任务关联的计划信息是否有重要的活动（存在重要活动需要审核）
            boolean isImportant = taskInfoDetail.getPlanInfos().stream()
                .anyMatch(obj -> CommonConstant.IMPORTANT_ACTIVITY.equals(obj.getIsImportant()));
            ScheduTaskInfo taskInfo = new ScheduTaskInfo();
            BeanUtils.copyProperties(taskInfoDetail, taskInfo);
            taskInfo.setCreatMethod(TaskConstant.TASK_CREATE_TYPE_SCHEDULE);
            taskInfo.setTaskStatus(TaskConstant.TASK_STATUS_WAIT_PUSH);
            taskInfo.setApprovalStatus(
                isImportant ? CommonConstant.APPROVAL_STATUS_PENDING : CommonConstant.APPROVAL_STATUS_APPROVED);
            taskInfo.setCreateBy(operUser);
            taskInfo.setUpdateBy(operUser);
            taskInfoList.add(taskInfo);
            ScheduTaskHis taskHis = new ScheduTaskHis();
            BeanUtils.copyProperties(taskInfo, taskHis);
            taskHisList.add(taskHis);
            // 要存储的关系数据
            taskInfoDetail.getPlanInfos().forEach(v -> {
                ScheduTaskPlanRel taskPlanRel = new ScheduTaskPlanRel();
                BeanUtils.copyProperties(v, taskPlanRel);
                BeanUtils.copyProperties(taskInfoDetail, taskPlanRel);
                taskPlanRel.setPlanId(v.getId());
                taskPlanRel.setCreateBy(operUser);
                taskPlanRel.setUpdateBy(operUser);
                taskPlanRelList.add(taskPlanRel);
            });
            taskInfoDetail.getExtendsInfos().forEach(v -> {
                v.setTaskCode(taskInfo.getTaskCode());
                v.setTaskName(taskInfo.getTaskName());
                v.setCreateBy(operUser);
                v.setUpdateBy(operUser);
            });
        }
        // 此时新增完成以后才会生成主键
        int taskCount = scheduTaskInfoMapper.batchInsertScheduTaskInfo(taskInfoList);
        // 生成taskCode-id字典
        Map<String, String> taskCodeIdMap = taskInfoList.stream()
            .collect(Collectors.toMap(ScheduTaskInfo::getTaskCode, ScheduTaskInfo::getId));
        // 各数据补充taskId
        taskPlanRelList.forEach(v -> v.setTaskId(taskCodeIdMap.get(v.getTaskCode())));
        taskHisList.forEach(v -> v.setTaskId(taskCodeIdMap.get(v.getTaskCode())));
        extendsInfos.forEach(v -> v.setTaskId(taskCodeIdMap.get(v.getTaskCode())));
        // 需要存储的审核信息
        taskInfoList.forEach(v -> {
            if (v.getApprovalStatus().equals(CommonConstant.APPROVAL_STATUS_PENDING)) {
                ScheduApprovalRecord approvalRecord = new ScheduApprovalRecord();
                approvalRecord.setBusinessId(v.getId());
                approvalRecord.setModuleType(CommonConstant.APPROVAL_SOURCE_TASK_INFO);
                approvalRecord.setApprovalStatus(v.getApprovalStatus());
                approvalRecord.setCreateBy(operUser);
                approvalRecord.setUpdateBy(operUser);
                approvalRecordList.add(approvalRecord);
            }
        });
        // 存储到任务历史表（统计用）
        int taskHisCount = scheduTaskHisMapper.batchInsertScheduTaskHis(taskHisList);
        // 存储任务和计划的关联关系
        int relCount = scheduTaskPlanRelMapper.batchInsertScheduTaskPlanRel(taskPlanRelList);
        // 存储任务拓展信息
        int extendCount = scheduTaskExtendMapper.batchInsertScheduTaskExtend(extendsInfos);
        // 更新任务状态为已调度
        List<ScheduPlanInfo> scheduPlanInfoList = taskPlanRelList.stream().map(v -> {
            ScheduPlanInfo scheduPlanInfo = new ScheduPlanInfo();
            scheduPlanInfo.setPlanCode(v.getPlanCode());
            scheduPlanInfo.setExecuteStatus(PlanConstant.SCHEDU_STATUS_DONE);
            scheduPlanInfo.setUpdateBy(operUser);
            return scheduPlanInfo;
        }).collect(Collectors.toList());
        int planCount = scheduPlanInfoMapper.batchUpdateScheduPlanInfoByPlanCode(scheduPlanInfoList);
        // 计划状态更新以后，需要保存一些历史数据
        List<String> planIdArr = taskPlanRelList.stream().map(ScheduTaskPlanRel::getPlanId).collect(Collectors.toList());
        ScheduPlanInfoQueryDTO queryDTO = new ScheduPlanInfoQueryDTO();
        queryDTO.setIdArr(planIdArr);
        List<ScheduPlanInfoVO> newPlanList = scheduPlanInfoMapper.selectScheduPlanInfoList(queryDTO);
        List<ScheduPlanHis> planHisList = new ArrayList<>();
        for (ScheduPlanInfoVO plan : newPlanList) {
            ScheduPlanHis planHis = new ScheduPlanHis();
            BeanUtils.copyProperties(plan, planHis);
            planHis.setPlanId(plan.getId());
            planHisList.add(planHis);
        }
        int hisCount = scheduPlanHisMapper.batchInsertScheduPlanHis(planHisList);
        log.info("保存计划历史信息数量:{}", hisCount);
        // 存储要审核的数据
        int approvalCount = scheduApprovalRecordMapper.batchInsertScheduApprovalRecord(approvalRecordList);
        log.info("调度任务创建接口: taskCount:{},taskHisCount:{},relCount:{},extendCount:{},planCount:{},approvalCount:{}",
            taskCount, taskHisCount, relCount, extendCount, planCount, approvalCount);
        return taskCount > 0;
    }

    /**
     * 批量更新计划扩展信息
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdatePlanExtend(UpdatePlanExtendApiDTO dto) {
        log.info("批量更新计划扩展信息，sysId:{}，operUser:{}", dto.getSysId(), dto.getOperUser());
        if (null == dto.getExtendList() || dto.getExtendList().isEmpty()) {
            return false;
        }
        int count = scheduPlanExtendMapper.batchUpdateScheduPlanExtend(dto.getExtendList());
        return count > 0;
    }

    /**
     * 查询算法基础信息列表
     *
     * @return
     */
    @Override
    public List<ScheduAlgorithmInfo> getAlgorithmInfoList() {
        return scheduAlgorithmInfoMapper.selectScheduAlgorithmInfoList(new ScheduAlgorithmInfo());
    }

    /**
     * 查询算法规则列表
     *
     * @return
     */
    @Override
    public List<ScheduAlgruleConfigVO> getAlgruleConfigList() {
        return scheduAlgruleConfigMapper.selectScheduAlgruleConfigList(new ScheduAlgruleConfigQueryDTO());
    }

    /**
     * 批量新增算法运行记录信息
     *
     * @param dto
     * @return
     */
    @Override
    public Boolean batchAddAlgorithmInvokeRecord(List<ScheduAlgorithmInvokeRecord> dto) {
        int count = scheduAlgorithmInvokeRecordMapper.batchInsertScheduAlgorithmInvokeRecord(dto);
        return count > 0;
    }
}
