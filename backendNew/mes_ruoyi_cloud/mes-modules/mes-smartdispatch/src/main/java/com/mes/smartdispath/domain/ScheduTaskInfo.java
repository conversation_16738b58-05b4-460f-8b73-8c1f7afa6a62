package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 调度任务信息对象 tb_schedu_task_info
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduTaskInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 业务分类 */
    @Excel(name = "业务分类")
    private String businessType;

    /** 任务编号，全局唯一标识 */
    @Excel(name = "任务编号，全局唯一标识")
    private String taskCode;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 任务创建方式，1-定时调度，2-即时调度，3-人工分配 */
    @Excel(name = "任务创建方式，1-定时调度，2-即时调度，3-人工分配")
    private String creatMethod;

    /** 任务状态（1-待推送，2-已推送，3-进行中，4-已完成，5-已回退，6-已中止） */
    @Excel(name = "任务状态", readConverterExp = "1=-待推送，2-已推送，3-进行中，4-已完成，5-已回退，6-已中止")
    private String taskStatus;

    /** 任务派发时间 */
    @Excel(name = "任务派发时间")
    private String dispatchedTime;

    /** 任务开始时间 */
    @Excel(name = "任务开始时间")
    private String startTime;

    /** 任务结束时间 */
    @Excel(name = "任务结束时间")
    private String endTime;

    /** 任务描述，如回退原因等 */
    @Excel(name = "任务描述，如回退原因等")
    private String taskDesc;

    /** 退回时间 */
    @Excel(name = "退回时间")
    private String backTime;

    /** 软删除标志（Y/N） */
    @Excel(name = "软删除标志", readConverterExp = "Y=/N")
    private String isDeleted;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    /** 任务审批状态: approved-已批准, rejected-已驳回, pending-待审批 */
    @Excel(name = "任务审批状态: approved-已批准, rejected-已驳回, pending-待审批")
    private String approvalStatus;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setCreatMethod(String creatMethod) {
        this.creatMethod = creatMethod;
    }

    public String getCreatMethod() {
        return creatMethod;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setDispatchedTime(String dispatchedTime) {
        this.dispatchedTime = dispatchedTime;
    }

    public String getDispatchedTime() {
        return dispatchedTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc;
    }

    public String getTaskDesc() {
        return taskDesc;
    }

    public void setBackTime(String backTime) {
        this.backTime = backTime;
    }

    public String getBackTime() {
        return backTime;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() {
        return approvalStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("businessType", getBusinessType()).append("taskCode", getTaskCode())
            .append("taskName", getTaskName()).append("creatMethod", getCreatMethod())
            .append("taskStatus", getTaskStatus()).append("dispatchedTime", getDispatchedTime())
            .append("startTime", getStartTime()).append("endTime", getEndTime()).append("taskDesc", getTaskDesc())
            .append("backTime", getBackTime()).append("createTime", getCreateTime()).append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime()).append("updateBy", getUpdateBy()).append("isDeleted", getIsDeleted())
            .append("tenantId", getTenantId()).append("approvalStatus", getApprovalStatus()).toString();
    }
}
