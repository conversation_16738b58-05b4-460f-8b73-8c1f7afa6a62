package com.mes.smartdispath.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.mes.common.core.utils.StringUtils;
import com.mes.common.security.utils.SecurityUtils;
import com.mes.smartdispath.constant.CommonConstant;
import com.mes.smartdispath.constant.SysDictConstant;
import com.mes.smartdispath.domain.ScheduAlgorithmInfo;
import com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord;
import com.mes.smartdispath.domain.ScheduAlgruleConfig;
import com.mes.smartdispath.domain.dto.ScheduAlgruleConfigQueryDTO;
import com.mes.smartdispath.domain.vo.ScheduAlgorithmInvokeRecordVO;
import com.mes.smartdispath.domain.vo.ScheduAlgruleConfigVO;
import com.mes.smartdispath.mapper.ScheduAlgorithmInfoMapper;
import com.mes.smartdispath.mapper.ScheduAlgorithmInvokeRecordMapper;
import com.mes.smartdispath.mapper.ScheduAlgruleConfigMapper;
import com.mes.smartdispath.service.IDispatchRuleMgrService;
import com.mes.system.api.model.LoginUser;

/**
 * 调度规则管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
public class DispatchRuleMgrServiceImpl implements IDispatchRuleMgrService {
    private static final Logger log = LoggerFactory.getLogger(DispatchRuleMgrServiceImpl.class);

    @Autowired
    private ScheduAlgorithmInfoMapper scheduAlgorithmInfoMapper;

    @Autowired
    private ScheduAlgorithmInvokeRecordMapper scheduAlgorithmInvokeRecordMapper;

    @Autowired
    private ScheduAlgruleConfigMapper scheduAlgruleConfigMapper;

    /**
     * 算法列表查询
     *
     * @param scheduAlgorithmInfo
     * @return 算法列表
     */
    @Override
    public List<ScheduAlgorithmInfo> qryAlgorithmList(ScheduAlgorithmInfo scheduAlgorithmInfo) {
        return scheduAlgorithmInfoMapper.selectScheduAlgorithmInfoList(scheduAlgorithmInfo);
    }

    /**
     * 算法新增/编辑
     *
     * @param scheduAlgorithmInfo
     * @return 算法列表
     */
    @Override
    public int saveAlgorithmInfo(ScheduAlgorithmInfo scheduAlgorithmInfo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isEmpty(scheduAlgorithmInfo.getId())) {
            // 新增逻辑
            log.info("新增算法");
            scheduAlgorithmInfo.setCreateBy(loginUser.getUsername());
            return scheduAlgorithmInfoMapper.insertScheduAlgorithmInfo(scheduAlgorithmInfo);
        }
        else {
            // 编辑逻辑
            log.info("修改算法，id:{}", scheduAlgorithmInfo.getId());
            return scheduAlgorithmInfoMapper.updateScheduAlgorithmInfo(scheduAlgorithmInfo);
        }
    }

    /**
     * 算法删除
     *
     * @param id
     * @return
     */
    @Override
    public int delAlgorithmInfo(String id) {
        ScheduAlgorithmInfo scheduAlgorithmInfo = new ScheduAlgorithmInfo();
        scheduAlgorithmInfo.setId(id);
        scheduAlgorithmInfo.setIsDelete(CommonConstant.IS_DELETE_1);
        return scheduAlgorithmInfoMapper.updateScheduAlgorithmInfo(scheduAlgorithmInfo);
    }

    /**
     * 算法调用情况查询
     *
     * @param scheduAlgorithmInvokeRecord
     * @return 算法调用情况列表
     */
    @Override
    public List<ScheduAlgorithmInvokeRecordVO> qryAlgorithmCallList(
        ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord) {
        return scheduAlgorithmInvokeRecordMapper
            .selectCollectScheduAlgorithmInvokeRecordList(scheduAlgorithmInvokeRecord);
    }

    /**
     * 算法调用详细列表查询
     *
     * @param scheduAlgorithmInvokeRecord
     * @return
     */
    @Override
    public List<ScheduAlgorithmInvokeRecord> qryAlgorithmCallDetailList(
        ScheduAlgorithmInvokeRecord scheduAlgorithmInvokeRecord) {
        return scheduAlgorithmInvokeRecordMapper.selectScheduAlgorithmInvokeRecordList(scheduAlgorithmInvokeRecord);
    }

    /**
     * 算法规则库查询
     *
     * @param queryDTO
     * @return
     */
    @Override
    public List<ScheduAlgruleConfigVO> qryAlgorithmLibrary(ScheduAlgruleConfigQueryDTO queryDTO) {
        queryDTO.setAlgruleSubtypeBasClassCode(SysDictConstant.ALGRULE_SUBTYPE_BAS_CLASS_CODE);
        queryDTO.setAlgruleSubtypeDynClassCode(SysDictConstant.ALGRULE_SUBTYPE_DYN_DICT_CODE);
        if (StringUtils.isNotEmpty(queryDTO.getEffectiveRegion())) {
            queryDTO.setEffectiveRegionArr(Arrays.asList(StringUtils.split(queryDTO.getEffectiveRegion(), ",")));
        }
        return scheduAlgruleConfigMapper.selectScheduAlgruleConfigList(queryDTO);
    }

    /**
     * 算法规则库新增/编辑
     *
     * @param scheduAlgruleConfig
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int saveAlgorithmLibrary(ScheduAlgruleConfig scheduAlgruleConfig) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isEmpty(scheduAlgruleConfig.getId())) {
            log.info("新增调度算法规则记录");
            // 生成规则code所需参数
            String curDate = LocalDate.now().format(DateTimeFormatter.ofPattern("YYYYMMdd"));
            String algruleCode = CommonConstant.ALGORITHM_RULE_CODE_PREFIX + curDate + "-"
                + UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8);
            // 生效区域可能是多个
            List<String> effectiveRegionArr = Arrays
                .asList(StringUtils.split(scheduAlgruleConfig.getEffectiveRegion(), ","));
            List<ScheduAlgruleConfig> scheduAlgruleConfigList = new ArrayList<>();
            // 生成批量存储的参数
            for (String effectiveRegion : effectiveRegionArr) {
                ScheduAlgruleConfig scheduAlgruleConfigTemp = new ScheduAlgruleConfig();
                BeanUtils.copyProperties(scheduAlgruleConfig, scheduAlgruleConfigTemp);
                scheduAlgruleConfigTemp.setEffectiveRegion(effectiveRegion);
                scheduAlgruleConfigTemp.setCreateBy(loginUser.getUsername());
                scheduAlgruleConfigTemp.setUpdateBy(loginUser.getUsername());
                scheduAlgruleConfigTemp.setAlgruleCode(algruleCode);
                scheduAlgruleConfigList.add(scheduAlgruleConfigTemp);
            }
            return scheduAlgruleConfigMapper.batchInsertScheduAlgruleConfig(scheduAlgruleConfigList);
        }
        else {
            log.info("修改调度算法规则记录，id: {}", scheduAlgruleConfig.getId());
            scheduAlgruleConfig.setUpdateBy(loginUser.getUsername());
            return scheduAlgruleConfigMapper.updateScheduAlgruleConfig(scheduAlgruleConfig);
        }
    }

    /**
     * 算法规则删除
     *
     * @param id
     * @return
     */
    @Override
    public int delAlgorithmLibrary(String id) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ScheduAlgruleConfig scheduAlgruleConfig = new ScheduAlgruleConfig();
        scheduAlgruleConfig.setId(id);
        scheduAlgruleConfig.setUpdateBy(loginUser.getUsername());
        scheduAlgruleConfig.setIsDeleted(CommonConstant.IS_DELETE_Y);
        return scheduAlgruleConfigMapper.updateScheduAlgruleConfig(scheduAlgruleConfig);
    }
}
