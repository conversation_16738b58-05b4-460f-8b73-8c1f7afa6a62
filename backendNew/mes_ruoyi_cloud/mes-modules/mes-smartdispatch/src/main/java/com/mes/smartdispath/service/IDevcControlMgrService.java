package com.mes.smartdispath.service;

import java.util.List;

import com.mes.smartdispath.domain.ScheduDevcControlRecord;
import com.mes.smartdispath.domain.dto.DevcControlRecordQueryDTO;

/**
 * 通用查询Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface IDevcControlMgrService {
    /**
     * 设备反控记录列表
     * 
     * @param queryDto 设备反控记录查询参数
     * @return 设备反控记录列表
     */
    public List<ScheduDevcControlRecord> selectScheduDevcControlRecordList(DevcControlRecordQueryDTO queryDto);
}
