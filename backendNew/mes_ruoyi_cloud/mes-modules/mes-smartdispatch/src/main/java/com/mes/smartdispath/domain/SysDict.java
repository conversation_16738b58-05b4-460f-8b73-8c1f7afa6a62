package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 字典对象 tb_sys_dict
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public class SysDict extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 字典标识 */
    private String id;

    /** 字典类型编码 */
    @Excel(name = "字典类型编码")
    private String classCode;

    /** 字典值编号 */
    @Excel(name = "字典值编号")
    private String dictCode;

    /** 字典值 */
    @Excel(name = "字典值")
    private String dictValue;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private String sortIndex;

    /** 创建时间 */
    @Excel(name = "创建时间")
    private String createDate;

    /** 状态：A - 有效、X - 无效 */
    @Excel(name = "状态：A - 有效、X - 无效")
    private String state;

    /** 状态时间 */
    @Excel(name = "状态时间")
    private String stateDate;

    /** 修改时间 */
    @Excel(name = "修改时间")
    private String modifeDate;

    /** 样式属性（其他样式扩展） */
    @Excel(name = "样式属性", readConverterExp = "其=他样式扩展")
    private String cssClass;

    /** 表格回显样式 */
    @Excel(name = "表格回显样式")
    private String listClass;

    /** 是否默认（Y是 N否） */
    @Excel(name = "是否默认", readConverterExp = "Y=是,N=否")
    private String isDefault;

    /** 父级字典分类编码，用于构建字典层级关系 */
    @Excel(name = "父级字典分类编码，用于构建字典层级关系")
    private String parentClassCode;

    /** 父级字典项编码，用于构建字典层级关系 */
    @Excel(name = "父级字典项编码，用于构建字典层级关系")
    private String parentDictCode;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setClassCode(String classCode) {
        this.classCode = classCode;
    }

    public String getClassCode() {
        return classCode;
    }

    public void setDictCode(String dictCode) {
        this.dictCode = dictCode;
    }

    public String getDictCode() {
        return dictCode;
    }

    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }

    public String getDictValue() {
        return dictValue;
    }

    public void setSortIndex(String sortIndex) {
        this.sortIndex = sortIndex;
    }

    public String getSortIndex() {
        return sortIndex;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState() {
        return state;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setModifeDate(String modifeDate) {
        this.modifeDate = modifeDate;
    }

    public String getModifeDate() {
        return modifeDate;
    }

    public void setCssClass(String cssClass) {
        this.cssClass = cssClass;
    }

    public String getCssClass() {
        return cssClass;
    }

    public void setListClass(String listClass) {
        this.listClass = listClass;
    }

    public String getListClass() {
        return listClass;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    public String getIsDefault() {
        return isDefault;
    }

    public void setParentClassCode(String parentClassCode) {
        this.parentClassCode = parentClassCode;
    }

    public String getParentClassCode() {
        return parentClassCode;
    }

    public void setParentDictCode(String parentDictCode) {
        this.parentDictCode = parentDictCode;
    }

    public String getParentDictCode() {
        return parentDictCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("classCode", getClassCode()).append("dictCode", getDictCode()).append("dictValue", getDictValue())
            .append("sortIndex", getSortIndex()).append("createDate", getCreateDate()).append("state", getState())
            .append("stateDate", getStateDate()).append("modifeDate", getModifeDate()).append("cssClass", getCssClass())
            .append("listClass", getListClass()).append("isDefault", getIsDefault()).append("remark", getRemark())
            .append("parentClassCode", getParentClassCode()).append("parentDictCode", getParentDictCode()).toString();
    }
}
