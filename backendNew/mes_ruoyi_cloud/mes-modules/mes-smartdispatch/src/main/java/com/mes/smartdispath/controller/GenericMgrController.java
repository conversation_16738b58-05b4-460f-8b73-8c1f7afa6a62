package com.mes.smartdispath.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.smartdispath.domain.ResAreaInfo;
import com.mes.smartdispath.domain.SysDict;
import com.mes.smartdispath.domain.dto.ResSiteQueryDTO;
import com.mes.smartdispath.domain.dto.ScheduMonitorActivityInfoQueryDTO;
import com.mes.smartdispath.service.IGenericMgrService;

/**
 * 通用查询 Controller
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/genericmgr")
public class GenericMgrController extends BaseController {
    @Autowired
    private IGenericMgrService genericMgrService;

    /**
     * 1.1.1、行政区划tree @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getAreaTreeInfo")
    public AjaxResult getAreaTreeInfo() {
        return success(genericMgrService.getAreaTreeInfo());
    }

    /**
     * 1.1.2、行政区划-省 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getProvinceInfo")
    public AjaxResult getProvinceInfo(ResAreaInfo resAreaInfo) {
        return success(genericMgrService.getProvinceInfo(resAreaInfo));
    }

    /**
     * 1.1.3、行政区划-市 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getCityInfo")
    public AjaxResult getCityInfo(ResAreaInfo resAreaInfo) {
        return success(genericMgrService.getCityInfo(resAreaInfo));
    }

    /**
     * 1.1.4、行政区划tree【无全国节点】 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getSimAreaTreeInfo")
    public AjaxResult getSimAreaTreeInfo() {
        return success(genericMgrService.getSimAreaTreeInfo());
    }

    /**
     * 1.2.1、业务分类 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getBusinessType")
    public AjaxResult getBusinessType(SysDict sysDict) {
        return success(genericMgrService.getBusinessType(sysDict));
    }

    /**
     * 1.3.1、站点类型 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getSiteType")
    public AjaxResult getSiteType(SysDict sysDict) {
        return success(genericMgrService.getSiteType(sysDict));
    }

    /**
     * 1.4.1、站点信息 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getSiteInfo")
    public AjaxResult getSiteInfo(ResSiteQueryDTO queryDto) {
        return success(genericMgrService.getSiteInfo(queryDto));
    }

    /**
     * 1.5.1、获取监测活动大类 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getActivityParentType")
    public AjaxResult getActivityParentType(ScheduMonitorActivityInfoQueryDTO queryDTO) {
        return success(genericMgrService.getActivityParentType(queryDTO));
    }

    /**
     * 1.6.1、获取监测活动小类 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getActivityType")
    public AjaxResult getActivityType(ScheduMonitorActivityInfoQueryDTO queryDTO) {
        return success(genericMgrService.getActivityType(queryDTO));
    }

    /**
     * 1.8.1、计划类别 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getPlanCategory")
    public AjaxResult getPlanCategory(SysDict sysDict) {
        return success(genericMgrService.getPlanCategory(sysDict));
    }

    /**
     * 1.9.1、片区站点树 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getPackageTreeInfo")
    public AjaxResult getPackageTreeInfo() {
        return success(genericMgrService.getPackageTreeInfo());
    }

    /**
     * 1.9.2、行政区划站点树 @RequiresPermissions("smartdispatch:pubRegion:query")
     */
    @GetMapping("/getRegionTreeInfo")
    public AjaxResult getRegionTreeInfo() {
        return success(genericMgrService.getRegionTreeInfo());
    }

    /**
     * 1.9.3、片区省份树
     */
    @GetMapping("/getPackageProvinceTreeInfo")
    public AjaxResult getPackageProvinceTreeInfo() {
        return success(genericMgrService.getPackageProvinceTreeInfo());
    }

    /**
     * 1.9.4、行政区划省份树
     */
    @GetMapping("/getRegionProvinceTreeInfo")
    public AjaxResult getRegionProvinceTreeInfo() {
        return success(genericMgrService.getRegionProvinceTreeInfo());
    }

    /**
     * 1.10.1、行政区划运维公司树
     */
    @GetMapping("/getRegionMaintainUnitTreeInfo")
    public AjaxResult getRegionMaintainUnitTreeInfo() {
        return success(genericMgrService.getRegionMaintainUnitTreeInfo());
    }

    /**
     * 1.11.1、运维公司查询
     */
    @GetMapping("/getMaintainUnitInfo")
    public AjaxResult getMaintainUnitInfo(String businessType) {
        return success(genericMgrService.getMaintainUnitInfo(businessType));
    }
}
