package com.mes.smartdispath.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.smartdispath.domain.ScheduDevcControlRecord;
import com.mes.smartdispath.domain.dto.DevcControlRecordQueryDTO;
import com.mes.smartdispath.service.IDevcControlMgrService;

/**
 * 设备反控管理 Controller
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/dispatchEfficiencymgr")
public class DevcControlMgrController extends BaseController {
    @Autowired
    private IDevcControlMgrService devcControlMgrService;

    /**
     * 7.1.1、反控设备列表
     */
    @GetMapping("/qryDeviceList")
    public TableDataInfo qryDeviceList(DevcControlRecordQueryDTO queryDto) {
        return getDataTable(new ArrayList<>());
    }

    /**
     * 7.2.1、设备反控记录列表
     */
    @GetMapping("/qryDevcControlRecordList")
    public TableDataInfo qryDevcControlRecordList(DevcControlRecordQueryDTO queryDto) {
        startPage();
        List<ScheduDevcControlRecord> list = devcControlMgrService.selectScheduDevcControlRecordList(queryDto);
        return getDataTable(list);
    }
}
