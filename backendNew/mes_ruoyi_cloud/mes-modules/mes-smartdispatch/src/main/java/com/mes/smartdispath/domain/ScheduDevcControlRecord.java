package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 设备反控记录对象 tb_schedu_devc_control_record
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduDevcControlRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 设备编码 */
    @Excel(name = "设备编码")
    private String devcCode;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String devcName;

    /** 反控指令 */
    @Excel(name = "反控指令")
    private String controlCommand;

    /** 反控状态：1成功，2失败 */
    @Excel(name = "反控状态：1成功，2失败")
    private String status;

    /** 下发时间 */
    @Excel(name = "下发时间")
    private String sendTime;

    /** 租户ID */
    @Excel(name = "租户ID")
    private String tenantId;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setDevcCode(String devcCode) {
        this.devcCode = devcCode;
    }

    public String getDevcCode() {
        return devcCode;
    }

    public void setDevcName(String devcName) {
        this.devcName = devcName;
    }

    public String getDevcName() {
        return devcName;
    }

    public void setControlCommand(String controlCommand) {
        this.controlCommand = controlCommand;
    }

    public String getControlCommand() {
        return controlCommand;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantId() {
        return tenantId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("devcCode", getDevcCode()).append("devcName", getDevcName())
            .append("controlCommand", getControlCommand()).append("status", getStatus())
            .append("sendTime", getSendTime()).append("createTime", getCreateTime()).append("tenantId", getTenantId())
            .toString();
    }
}
