package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 点位检测项(风险检测)对象 tb_res_site_inspection_item
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
public class ResSiteInspectionItem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private String id;

    /** 点位ID */
    @Excel(name = "点位ID")
    private String siteId;

    /** 检测类型（YEAR-年度，MONTH-月度） */
    @Excel(name = "检测类型", readConverterExp = "Y=EAR-年度，MONTH-月度")
    private String inspectionType;

    /** 检测时间（年度：YYYY；月度：YYYY-MM） */
    @Excel(name = "检测时间", readConverterExp = "年=度：YYYY；月度：YYYY-MM")
    private String inspectionTime;

    /** 检测参数（逗号分隔） */
    @Excel(name = "检测参数", readConverterExp = "逗=号分隔")
    private String parameters;

    /** 风险等级 */
    @Excel(name = "风险等级")
    private String riskLevel;

    /** 风险成因 */
    @Excel(name = "风险成因")
    private String riskReason;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createdBy;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updatedBy;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setInspectionTime(String inspectionTime) {
        this.inspectionTime = inspectionTime;
    }

    public String getInspectionTime() {
        return inspectionTime;
    }

    public void setParameters(String parameters) {
        this.parameters = parameters;
    }

    public String getParameters() {
        return parameters;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskReason(String riskReason) {
        this.riskReason = riskReason;
    }

    public String getRiskReason() {
        return riskReason;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("siteId", getSiteId()).append("inspectionType", getInspectionType())
            .append("inspectionTime", getInspectionTime()).append("parameters", getParameters())
            .append("riskLevel", getRiskLevel()).append("riskReason", getRiskReason()).append("remarks", getRemarks())
            .append("createdBy", getCreatedBy()).append("createTime", getCreateTime())
            .append("updatedBy", getUpdatedBy()).append("updateTime", getUpdateTime()).toString();
    }
}
