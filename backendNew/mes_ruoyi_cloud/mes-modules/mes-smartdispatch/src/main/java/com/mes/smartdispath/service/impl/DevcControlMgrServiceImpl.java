package com.mes.smartdispath.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mes.smartdispath.domain.ScheduDevcControlRecord;
import com.mes.smartdispath.domain.dto.DevcControlRecordQueryDTO;
import com.mes.smartdispath.mapper.ScheduDevcControlRecordMapper;
import com.mes.smartdispath.service.IDevcControlMgrService;

/**
 * 通用查询Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class DevcControlMgrServiceImpl implements IDevcControlMgrService {
    @Autowired
    private ScheduDevcControlRecordMapper scheduDevcControlRecordMapper;

    /**
     * 设备反控记录列表
     * 
     * @param queryDto 设备反控记录查询参数
     * @return 设备反控记录列表
     */
    @Override
    public List<ScheduDevcControlRecord> selectScheduDevcControlRecordList(DevcControlRecordQueryDTO queryDto) {
        return scheduDevcControlRecordMapper.selectScheduDevcControlRecordList(queryDto);
    }
}
