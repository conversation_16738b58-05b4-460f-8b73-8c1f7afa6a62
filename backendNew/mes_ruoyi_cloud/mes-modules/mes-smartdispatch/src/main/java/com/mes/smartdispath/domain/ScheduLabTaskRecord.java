package com.mes.smartdispath.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.mes.common.core.annotation.Excel;
import com.mes.common.core.web.domain.BaseEntity;

/**
 * 发送实验室的任务信息记录对象 tb_schedu_lab_task_record
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public class ScheduLabTaskRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private String id;

    /** 实验室名称 */
    @Excel(name = "实验室名称")
    private String laboratoryName;

    /** 实验室code */
    @Excel(name = "实验室code")
    private String laboratoryCode;

    /** 任务执行人ID */
    @Excel(name = "任务执行人ID")
    private String executorId;

    /** 任务执行人姓名 */
    @Excel(name = "任务执行人姓名")
    private String executorName;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String phone;

    /** 任务ID */
    @Excel(name = "任务ID")
    private String taskId;

    /** 任务编码 */
    @Excel(name = "任务编码")
    private String taskCode;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 计划编码 */
    @Excel(name = "计划编码")
    private String planCode;

    /** 计划名称 */
    @Excel(name = "计划名称")
    private String planName;

    /** 全部采样指标，josn方式存储 */
    @Excel(name = "全部采样指标，josn方式存储")
    private String collectPara;

    /** 发送状态，0未发送，1成功，2失败 */
    @Excel(name = "发送状态，0未发送，1成功，2失败")
    private String sendStatus;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setLaboratoryName(String laboratoryName) {
        this.laboratoryName = laboratoryName;
    }

    public String getLaboratoryName() {
        return laboratoryName;
    }

    public void setLaboratoryCode(String laboratoryCode) {
        this.laboratoryCode = laboratoryCode;
    }

    public String getLaboratoryCode() {
        return laboratoryCode;
    }

    public void setExecutorId(String executorId) {
        this.executorId = executorId;
    }

    public String getExecutorId() {
        return executorId;
    }

    public void setExecutorName(String executorName) {
        this.executorName = executorName;
    }

    public String getExecutorName() {
        return executorName;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setPlanCode(String planCode) {
        this.planCode = planCode;
    }

    public String getPlanCode() {
        return planCode;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setCollectPara(String collectPara) {
        this.collectPara = collectPara;
    }

    public String getCollectPara() {
        return collectPara;
    }

    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getSendStatus() {
        return sendStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId())
            .append("laboratoryName", getLaboratoryName()).append("laboratoryCode", getLaboratoryCode())
            .append("executorId", getExecutorId()).append("executorName", getExecutorName()).append("phone", getPhone())
            .append("taskId", getTaskId()).append("taskCode", getTaskCode()).append("taskName", getTaskName())
            .append("planCode", getPlanCode()).append("planName", getPlanName()).append("collectPara", getCollectPara())
            .append("createTime", getCreateTime()).append("updateTime", getUpdateTime())
            .append("sendStatus", getSendStatus()).toString();
    }
}
