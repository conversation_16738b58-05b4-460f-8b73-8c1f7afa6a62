package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ResPersonBasic;
import com.mes.smartdispath.domain.dto.ResPersonBasicQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.MaintainPersonCountVO;
import com.mes.smartdispath.domain.vo.ResPersonBasicVO;

/**
 * 人员基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface ResPersonBasicMapper {
    public MaintainPersonCountVO selectMaintainPersonStatic(@Param("businessType") String businessType,
        @Param("personType") String personType, @Param("onDutyStatus") String onDutyStatus,
        @Param("leaveStatus") String leaveStatus);

    /**
     * 查询运维人员数量
     *
     * @param queryDto
     * @return 运维人员数量
     */
    public Integer selectMaintenancePersonCount(TaskStaticQueryDTO queryDto);

    /**
     * 查询人员基本信息
     *
     * @param personId 人员基本信息主键
     * @return 人员基本信息
     */
    public ResPersonBasic selectResPersonBasicByPersonId(String personId);

    /**
     * 查询人员基本信息列表
     *
     * @param resPersonBasic 人员基本信息
     * @return 人员基本信息集合
     */
    public List<ResPersonBasicVO> selectResPersonBasicList(ResPersonBasicQueryDTO resPersonBasic);

    /**
     * 新增人员基本信息
     *
     * @param resPersonBasic 人员基本信息
     * @return 结果
     */
    public int insertResPersonBasic(ResPersonBasic resPersonBasic);

    /**
     * 批量新增人员基本信息
     *
     * @param resPersonBasicList 人员基本信息List
     * @return 结果
     */
    public int batchInsertResPersonBasic(List<ResPersonBasic> resPersonBasicList);

    /**
     * 修改人员基本信息
     *
     * @param resPersonBasic 人员基本信息
     * @return 结果
     */
    public int updateResPersonBasic(ResPersonBasic resPersonBasic);

    /**
     * 批量修改人员基本信息
     *
     * @param resPersonBasicList 人员基本信息List
     * @return 结果
     */
    public int batchUpdateResPersonBasic(List<ResPersonBasic> resPersonBasicList);

    /**
     * 删除人员基本信息
     *
     * @param personId 人员基本信息主键
     * @return 结果
     */
    public int deleteResPersonBasicByPersonId(String personId);

    /**
     * 批量删除人员基本信息
     *
     * @param personIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteResPersonBasicByPersonIds(String[] personIds);
}
