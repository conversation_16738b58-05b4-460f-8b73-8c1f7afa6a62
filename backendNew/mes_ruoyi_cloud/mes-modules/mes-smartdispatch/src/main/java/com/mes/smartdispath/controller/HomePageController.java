package com.mes.smartdispath.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.mes.common.core.web.controller.BaseController;
import com.mes.common.core.web.domain.AjaxResult;
import com.mes.common.core.web.page.TableDataInfo;
import com.mes.smartdispath.domain.dto.QualityTargetPerformanceQueryDTO;
import com.mes.smartdispath.domain.dto.TaskStaticQueryDTO;
import com.mes.smartdispath.domain.vo.SiteTaskListVO;
import com.mes.smartdispath.service.IHomePageService;

import io.jsonwebtoken.lang.Assert;

/**
 * 首页 Controller
 *
 * @author: li.haoyang @date： 2025/7/10
 */
@RestController
@RequestMapping("/homepage")
public class HomePageController extends BaseController {
    @Autowired
    private IHomePageService homePageService;

    /**
     * 统计当日的调度任务
     */
    @GetMapping("/qryTodayTaskStatic")
    public AjaxResult qryTodayTaskStatic(TaskStaticQueryDTO queryDTO) {
        return success(homePageService.qryTodayTaskStatic(queryDTO));
    }

    /**
     * 当日的调度任务列表
     */
    @GetMapping("/qryTodayTaskList")
    public TableDataInfo qryTodayTaskList(TaskStaticQueryDTO queryDTO) {
        startPage();
        List<SiteTaskListVO> list = homePageService.qryTodayTaskList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 数量目标完成情况
     */
    @GetMapping("/qryQuantityTargetPerformance")
    public AjaxResult qryQuantityTargetPerformance(String businessType) {
        Assert.notNull(businessType, "业务分类为必传参数,请核对请求参数!");
        return success(homePageService.qryQuantityTargetPerformance(businessType));
    }

    /**
     * 质量目标完成情况
     */
    @GetMapping("/qryQualityTargetPerformance")
    public AjaxResult qryQualityTargetPerformance(QualityTargetPerformanceQueryDTO queryDTO) {
        Assert.notNull(queryDTO.getBusinessType(), "业务分类为必传参数,请核对请求参数!");
        return success(homePageService.qryQualityTargetPerformance(queryDTO));
    }

    /**
     * 运维人员数量统计
     */
    @GetMapping("/qryMaintainPersonStatic")
    public AjaxResult qryMaintainPersonStatic(String businessType) {
        Assert.notNull(businessType, "业务分类为必传参数,请核对请求参数!");
        return success(homePageService.qryMaintainPersonStatic(businessType));
    }
}
