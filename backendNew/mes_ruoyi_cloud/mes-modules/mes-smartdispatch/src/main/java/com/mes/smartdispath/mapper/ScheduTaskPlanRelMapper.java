package com.mes.smartdispath.mapper;

import java.util.List;

import com.mes.smartdispath.domain.ScheduTaskPlanRel;
import com.mes.smartdispath.domain.vo.SiteTaskStaticVO;

/**
 * 调度任务与调度计划关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ScheduTaskPlanRelMapper {
    /**
     * 站点任务统计信息
     *
     * @param siteId 站点id
     * @return 站点任务统计信息
     */
    public SiteTaskStaticVO selectTaskStaticBySiteId(String siteId);

    /**
     * 查询调度任务与调度计划关系
     * 
     * @param id 调度任务与调度计划关系主键
     * @return 调度任务与调度计划关系
     */
    public ScheduTaskPlanRel selectScheduTaskPlanRelById(String id);

    /**
     * 查询调度任务与调度计划关系列表
     * 
     * @param scheduTaskPlanRel 调度任务与调度计划关系
     * @return 调度任务与调度计划关系集合
     */
    public List<ScheduTaskPlanRel> selectScheduTaskPlanRelList(ScheduTaskPlanRel scheduTaskPlanRel);

    /**
     * 新增调度任务与调度计划关系
     * 
     * @param scheduTaskPlanRel 调度任务与调度计划关系
     * @return 结果
     */
    public int insertScheduTaskPlanRel(ScheduTaskPlanRel scheduTaskPlanRel);

    /**
     * 批量新增调度任务与调度计划关系
     *
     * @param scheduTaskPlanRelList 调度任务与调度计划关系List
     * @return 结果
     */
    public int batchInsertScheduTaskPlanRel(List<ScheduTaskPlanRel> scheduTaskPlanRelList);

    /**
     * 修改调度任务与调度计划关系
     * 
     * @param scheduTaskPlanRel 调度任务与调度计划关系
     * @return 结果
     */
    public int updateScheduTaskPlanRel(ScheduTaskPlanRel scheduTaskPlanRel);

    /**
     * 批量修改调度任务与调度计划关系
     *
     * @param scheduTaskPlanRelList 调度任务与调度计划关系List
     * @return 结果
     */
    public int batchUpdateScheduTaskPlanRel(List<ScheduTaskPlanRel> scheduTaskPlanRelList);

    /**
     * 删除调度任务与调度计划关系
     * 
     * @param id 调度任务与调度计划关系主键
     * @return 结果
     */
    public int deleteScheduTaskPlanRelById(String id);

    /**
     * 批量删除调度任务与调度计划关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteScheduTaskPlanRelByIds(String[] ids);
}
