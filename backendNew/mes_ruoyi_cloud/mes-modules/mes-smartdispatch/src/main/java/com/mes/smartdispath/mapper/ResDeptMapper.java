package com.mes.smartdispath.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.mes.smartdispath.domain.ResDept;
import com.mes.smartdispath.domain.vo.MaintenanceUnitVO;

/**
 * 组织Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface ResDeptMapper {
    /**
     * 查询运维公司
     *
     * @param businessType
     * @param maintenanceUnitRoleType
     * @return
     */
    public List<MaintenanceUnitVO> selectMaintenanceUnitList(@Param("businessType") String businessType,
        @Param("maintenanceUnitRoleType") String maintenanceUnitRoleType);

    /**
     * 查询组织
     *
     * @param deptId 组织主键
     * @return 组织
     */
    public ResDept selectResDeptByDeptId(String deptId);

    /**
     * 查询组织列表
     *
     * @param resDept 组织
     * @return 组织集合
     */
    public List<ResDept> selectResDeptList(ResDept resDept);

    /**
     * 新增组织
     *
     * @param resDept 组织
     * @return 结果
     */
    public int insertResDept(ResDept resDept);

    /**
     * 批量新增组织
     *
     * @param resDeptList 组织List
     * @return 结果
     */
    public int batchInsertResDept(List<ResDept> resDeptList);

    /**
     * 修改组织
     *
     * @param resDept 组织
     * @return 结果
     */
    public int updateResDept(ResDept resDept);

    /**
     * 批量修改组织
     *
     * @param resDeptList 组织List
     * @return 结果
     */
    public int batchUpdateResDept(List<ResDept> resDeptList);

    /**
     * 删除组织
     *
     * @param deptId 组织主键
     * @return 结果
     */
    public int deleteResDeptByDeptId(String deptId);

    /**
     * 批量删除组织
     *
     * @param deptIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteResDeptByDeptIds(String[] deptIds);
}
