## Tomcat
#server:
#  port: 19094
#
## Spring
#spring:
#  application:
#    # 应用名称
#    name: mes-smartdispatch
#  profiles:
#    # 环境配置
#    active: dev
#  cloud:
#    nacos:
#      discovery:
#        # 服务注册地址
#        server-addr: 127.0.0.1:8848
#        enable: true
#        register-enabled: true
#      config:
#        # 配置中心地址
#        server-addr: 127.0.0.1:8848
#        enable: false
#        # 配置文件格式
#        file-extension: yml
#        # 共享配置
#        shared-configs:
#          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
#        group: MES

server:
  port: 19094

# Spring
spring:
  application:
    # 应用名称
    name: mes-smartdispatch
  profiles:
    # 环境配置
    active: prod
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 172.20.102.220:18848
        enable: true
        register-enabled: true
      config:
        # 配置中心地址
        server-addr: 172.20.102.220:18848
        enable: true
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        group: MES
        data-id: smartdispatch
