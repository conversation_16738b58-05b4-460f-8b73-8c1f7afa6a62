# spring配置
spring:
  redis:
    host: *************
    port: 6379
    #password: test1234
  datasource:
    driver-class-name: dm.jdbc.driver.DmDriver
    url: jdbc:dm://*************:5236/CNEMC_ENVIRONMENT?schema=CNEMC_ENVIRONMENT
    username: SYSDBA
    password: SYSDBA_dm001
# mybatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.mes.smartdispath
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath:mapper/**/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# springdoc配置
springdoc:
  gatewayUrl: http://localhost:8080/${spring.application.name}
  api-docs:
    # 是否开启接口文档
    enabled: true
  info:
    # 标题
    title: '智能调度模块接口文档'
    # 描述
    description: '智能调度模块接口描述'
    # 作者信息
    contact:
      name: RuoYi
      url: https://ruoyi.vip

powerjobclient:
  address: ************:28089
  appName: mes
  appSecret: mes

# 需要特殊处理的监测活动大类（三级分类）
activityType:
  # 断面采样的监测活动大类code
  sectionSampling: 'water_collect_sample'
  # 样品运输的监测活动大类code
  sampleTransportation: 'water_transport_sample'
  # 现场监督的监测活动大类code
  sceneSupervision: 'water_supervision'
  # 盲样考核的监测活动大类code
  blinkCheck: 'water_blink_inspection'
  # 周质控(气)的监测活动大类code
  weeklyGas: 'air_week_quactrl_region'
  # 周质控(水)的监测活动大类code
  weeklyWater: 'water_week_quactrl'
  # 月质控(气)的监测活动大类code
  monthlyGas: 'air_month_quactrl_region'
  # 月质控(水)的监测活动大类code
  monthlyWater: 'water_month_quactrl'

## spring配置
#spring:
#  redis:
#    host: **************
#    port: 6379
#    password: mestest
#  datasource:
#    driver-class-name: dm.jdbc.driver.DmDriver
#    url: jdbc:dm://*************:30033/CNEMC_ENVIRONMENT?schema=CNEMC_ENVIRONMENT
#    username: SYSADMIN
#    password: @MXDtIKoOU2j
## mybatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.mes.smartdispath
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath:mapper/**/*.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#
## springdoc配置
#springdoc:
#  gatewayUrl: http://localhost:8080/${spring.application.name}
#  api-docs:
#    # 是否开启接口文档
#    enabled: true
#  info:
#    # 标题
#    title: '智能调度模块接口文档'
#    # 描述
#    description: '智能调度模块接口描述'
#    # 作者信息
#    contact:
#      name: RuoYi
#      url: https://ruoyi.vip
#
#powerjobclient:
#  address: ************:28089
#  appName: mes
#  appSecret: mes
#
## 需要特殊处理的监测活动大类（三级分类）
#activityType:
#  # 断面采样的监测活动大类code
#  sectionSampling: 'section_sample'
#  # 样品运输的监测活动大类code
#  sampleTransportation: 'transport_sample'
#  # 现场监督的监测活动大类code
#  sceneSupervision: 'supervision'