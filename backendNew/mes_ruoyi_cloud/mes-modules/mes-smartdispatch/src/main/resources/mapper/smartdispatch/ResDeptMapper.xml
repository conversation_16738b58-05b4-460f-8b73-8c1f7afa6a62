<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ResDeptMapper">

    <resultMap type="com.mes.smartdispath.domain.ResDept" id="ResDeptResult">
        <result property="deptId" column="dept_id"/>
        <result property="monitoringElement" column="monitoring_element"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="deptCode" column="dept_code"/>
        <result property="deptType" column="dept_type"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="ancestors" column="ancestors"/>
    </resultMap>

    <resultMap id="MaintenanceUnitResult" type="com.mes.smartdispath.domain.vo.MaintenanceUnitVO">
        <result property="maintenanceUnitId" column="dept_id"/>
        <result property="maintenanceUnitName" column="dept_name"/>
    </resultMap>

    <sql id="selectResDeptVo">
        select dept_id,
               monitoring_element,
               parent_id,
               dept_name,
               dept_code,
               dept_type,
               order_num,
               leader,
               phone,
               email,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag,
               ancestors
        from tb_res_dept
    </sql>

    <select id="selectMaintenanceUnitList" resultMap="MaintenanceUnitResult">
        SELECT t1.dept_id, t1.dept_name
        FROM tb_res_dept t1
        LEFT JOIN tb_res_dept_role_rel t2 on t1.dept_id = t2.dept_id
        WHERE t2.role_code = #{maintenanceUnitRoleType}
        <if test="businessType != null  and businessType != ''">
            and REGEXP_LIKE("monitoring_element", '(^|,)' || #{businessType} || '(,|$)')
        </if>
    </select>

    <select id="selectResDeptList" parameterType="com.mes.smartdispath.domain.ResDept"
            resultMap="ResDeptResult">
        <include refid="selectResDeptVo"/>
        <where>
            <if test="monitoringElement != null  and monitoringElement != ''">
                and monitoring_element = #{monitoringElement}
            </if>
            <if test="parentId != null  and parentId != ''">
                and parent_id = #{parentId}
            </if>
            <if test="deptName != null  and deptName != ''">
                and dept_name like concat('%', #{deptName}, '%')
            </if>
            <if test="deptCode != null  and deptCode != ''">
                and dept_code = #{deptCode}
            </if>
            <if test="deptType != null  and deptType != ''">
                and dept_type = #{deptType}
            </if>
            <if test="orderNum != null  and orderNum != ''">
                and order_num = #{orderNum}
            </if>
            <if test="leader != null  and leader != ''">
                and leader = #{leader}
            </if>
            <if test="phone != null  and phone != ''">
                and phone = #{phone}
            </if>
            <if test="email != null  and email != ''">
                and email = #{email}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
            <if test="ancestors != null  and ancestors != ''">
                and ancestors = #{ancestors}
            </if>
        </where>
    </select>

    <select id="selectResDeptByDeptId" parameterType="String"
            resultMap="ResDeptResult">
        <include refid="selectResDeptVo"/>
        where dept_id = #{deptId}
    </select>

    <insert id="insertResDept" parameterType="com.mes.smartdispath.domain.ResDept">
        insert into tb_res_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,
            </if>
            <if test="monitoringElement != null and monitoringElement != ''">monitoring_element,
            </if>
            <if test="parentId != null">parent_id,
            </if>
            <if test="deptName != null">dept_name,
            </if>
            <if test="deptCode != null">dept_code,
            </if>
            <if test="deptType != null">dept_type,
            </if>
            <if test="orderNum != null">order_num,
            </if>
            <if test="leader != null">leader,
            </if>
            <if test="phone != null">phone,
            </if>
            <if test="email != null">email,
            </if>
            <if test="status != null">status,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
            <if test="ancestors != null">ancestors,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},
            </if>
            <if test="monitoringElement != null and monitoringElement != ''">#{monitoringElement},
            </if>
            <if test="parentId != null">#{parentId},
            </if>
            <if test="deptName != null">#{deptName},
            </if>
            <if test="deptCode != null">#{deptCode},
            </if>
            <if test="deptType != null">#{deptType},
            </if>
            <if test="orderNum != null">#{orderNum},
            </if>
            <if test="leader != null">#{leader},
            </if>
            <if test="phone != null">#{phone},
            </if>
            <if test="email != null">#{email},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
            <if test="ancestors != null">#{ancestors},
            </if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertResDept" parameterType="java.util.List">
        insert into tb_res_dept (
        dept_id,
        monitoring_element,
        parent_id,
        dept_name,
        dept_code,
        dept_type,
        order_num,
        leader,
        phone,
        email,
        status,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        ancestors
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.deptId},
            #{item.monitoringElement},
            #{item.parentId},
            #{item.deptName},
            #{item.deptCode},
            #{item.deptType},
            #{item.orderNum},
            #{item.leader},
            #{item.phone},
            #{item.email},
            #{item.status},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.delFlag},
            #{item.ancestors}
            )
        </foreach>
    </insert>

    <update id="updateResDept" parameterType="com.mes.smartdispath.domain.ResDept">
        update tb_res_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="monitoringElement != null and monitoringElement != ''">monitoring_element =
                #{monitoringElement},
            </if>
            <if test="parentId != null">parent_id =
                #{parentId},
            </if>
            <if test="deptName != null">dept_name =
                #{deptName},
            </if>
            <if test="deptCode != null">dept_code =
                #{deptCode},
            </if>
            <if test="deptType != null">dept_type =
                #{deptType},
            </if>
            <if test="orderNum != null">order_num =
                #{orderNum},
            </if>
            <if test="leader != null">leader =
                #{leader},
            </if>
            <if test="phone != null">phone =
                #{phone},
            </if>
            <if test="email != null">email =
                #{email},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="delFlag != null">del_flag =
                #{delFlag},
            </if>
            <if test="ancestors != null">ancestors =
                #{ancestors},
            </if>
        </trim>
        where dept_id = #{deptId}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateResDept" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_res_dept
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.monitoringElement != null and item.monitoringElement != ''">
                    monitoring_element = #{item.monitoringElement},
                </if>
                <if test="item.parentId != null">
                    parent_id = #{item.parentId},
                </if>
                <if test="item.deptName != null">
                    dept_name = #{item.deptName},
                </if>
                <if test="item.deptCode != null">
                    dept_code = #{item.deptCode},
                </if>
                <if test="item.deptType != null">
                    dept_type = #{item.deptType},
                </if>
                <if test="item.orderNum != null">
                    order_num = #{item.orderNum},
                </if>
                <if test="item.leader != null">
                    leader = #{item.leader},
                </if>
                <if test="item.phone != null">
                    phone = #{item.phone},
                </if>
                <if test="item.email != null">
                    email = #{item.email},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.delFlag != null">
                    del_flag = #{item.delFlag},
                </if>
                <if test="item.ancestors != null">
                    ancestors = #{item.ancestors},
                </if>
            </trim>
            where dept_id = #{item.deptId}
        </foreach>
    </update>

    <delete id="deleteResDeptByDeptId" parameterType="String">
        delete
        from tb_res_dept
        where dept_id = #{deptId}
    </delete>

    <delete id="deleteResDeptByDeptIds" parameterType="String">
        delete from tb_res_dept where dept_id in
        <foreach item="deptId" collection="array" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </delete>
</mapper>