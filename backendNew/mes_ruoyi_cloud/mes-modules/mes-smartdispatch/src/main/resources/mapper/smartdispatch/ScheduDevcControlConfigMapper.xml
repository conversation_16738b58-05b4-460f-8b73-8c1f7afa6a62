<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduDevcControlConfigMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduDevcControlConfig" id="ScheduDevcControlConfigResult">
        <result property="id"    column="id"    />
        <result property="devcCode"    column="devc_code"    />
        <result property="devcName"    column="devc_name"    />
        <result property="businessType"    column="business_type"    />
        <result property="siteId"    column="site_id"    />
        <result property="siteName"    column="site_name"    />
        <result property="provinceName"    column="province_name"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="cityName"    column="city_name"    />
        <result property="cityCode"    column="city_code"    />
        <result property="controlCommand"    column="control_command"    />
        <result property="dispatchType"    column="dispatch_type"    />
        <result property="frequency"    column="frequency"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <sql id="selectScheduDevcControlConfigVo">
        select id, devc_code, devc_name, business_type, site_id, site_name, province_name, province_code, city_name, city_code, control_command, dispatch_type, frequency, status, create_by, create_time, update_by, update_time, tenant_id from tb_schedu_devc_control_config
    </sql>

    <select id="selectScheduDevcControlConfigList" parameterType="com.mes.smartdispath.domain.ScheduDevcControlConfig" resultMap="ScheduDevcControlConfigResult">
        <include refid="selectScheduDevcControlConfigVo"/>
        <where>  
            <if test="devcCode != null  and devcCode != ''"> and devc_code = #{devcCode}</if>
            <if test="devcName != null  and devcName != ''"> and devc_name like concat('%', #{devcName}, '%')</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="siteId != null  and siteId != ''"> and site_id = #{siteId}</if>
            <if test="siteName != null  and siteName != ''"> and site_name like concat('%', #{siteName}, '%')</if>
            <if test="provinceName != null  and provinceName != ''"> and province_name like concat('%', #{provinceName}, '%')</if>
            <if test="provinceCode != null  and provinceCode != ''"> and province_code = #{provinceCode}</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="cityCode != null  and cityCode != ''"> and city_code = #{cityCode}</if>
            <if test="controlCommand != null  and controlCommand != ''"> and control_command = #{controlCommand}</if>
            <if test="dispatchType != null  and dispatchType != ''"> and dispatch_type = #{dispatchType}</if>
            <if test="frequency != null  and frequency != ''"> and frequency = #{frequency}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
        </where>
    </select>
    
    <select id="selectScheduDevcControlConfigById" parameterType="String" resultMap="ScheduDevcControlConfigResult">
        <include refid="selectScheduDevcControlConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduDevcControlConfig" parameterType="com.mes.smartdispath.domain.ScheduDevcControlConfig">
        insert into tb_schedu_devc_control_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="devcCode != null and devcCode != ''">devc_code,</if>
            <if test="devcName != null and devcName != ''">devc_name,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="siteId != null and siteId != ''">site_id,</if>
            <if test="siteName != null and siteName != ''">site_name,</if>
            <if test="provinceName != null and provinceName != ''">province_name,</if>
            <if test="provinceCode != null and provinceCode != ''">province_code,</if>
            <if test="cityName != null and cityName != ''">city_name,</if>
            <if test="cityCode != null and cityCode != ''">city_code,</if>
            <if test="controlCommand != null and controlCommand != ''">control_command,</if>
            <if test="dispatchType != null and dispatchType != ''">dispatch_type,</if>
            <if test="frequency != null and frequency != ''">frequency,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null and createTime != ''">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tenantId != null">tenant_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="devcCode != null and devcCode != ''">#{devcCode},</if>
            <if test="devcName != null and devcName != ''">#{devcName},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="siteId != null and siteId != ''">#{siteId},</if>
            <if test="siteName != null and siteName != ''">#{siteName},</if>
            <if test="provinceName != null and provinceName != ''">#{provinceName},</if>
            <if test="provinceCode != null and provinceCode != ''">#{provinceCode},</if>
            <if test="cityName != null and cityName != ''">#{cityName},</if>
            <if test="cityCode != null and cityCode != ''">#{cityCode},</if>
            <if test="controlCommand != null and controlCommand != ''">#{controlCommand},</if>
            <if test="dispatchType != null and dispatchType != ''">#{dispatchType},</if>
            <if test="frequency != null and frequency != ''">#{frequency},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null and createTime != ''">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
         </trim>
    </insert>

    <update id="updateScheduDevcControlConfig" parameterType="com.mes.smartdispath.domain.ScheduDevcControlConfig">
        update tb_schedu_devc_control_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="devcCode != null and devcCode != ''">devc_code = #{devcCode},</if>
            <if test="devcName != null and devcName != ''">devc_name = #{devcName},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="siteId != null and siteId != ''">site_id = #{siteId},</if>
            <if test="siteName != null and siteName != ''">site_name = #{siteName},</if>
            <if test="provinceName != null and provinceName != ''">province_name = #{provinceName},</if>
            <if test="provinceCode != null and provinceCode != ''">province_code = #{provinceCode},</if>
            <if test="cityName != null and cityName != ''">city_name = #{cityName},</if>
            <if test="cityCode != null and cityCode != ''">city_code = #{cityCode},</if>
            <if test="controlCommand != null and controlCommand != ''">control_command = #{controlCommand},</if>
            <if test="dispatchType != null and dispatchType != ''">dispatch_type = #{dispatchType},</if>
            <if test="frequency != null and frequency != ''">frequency = #{frequency},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScheduDevcControlConfigById" parameterType="String">
        delete from tb_schedu_devc_control_config where id = #{id}
    </delete>

    <delete id="deleteScheduDevcControlConfigByIds" parameterType="String">
        delete from tb_schedu_devc_control_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>