<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduTaskExtendMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduTaskExtend" id="ScheduTaskExtendResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="executorId"    column="executor_id"    />
        <result property="executorName"    column="executor_name"    />
        <result property="phone"    column="phone"    />
        <result property="maintainUnitCode"    column="maintain_unit_code"    />
        <result property="maintainUnitName"    column="maintain_unit_name"    />
        <result property="laboratoryAddress"    column="laboratory_address"    />
        <result property="laboratoryName"    column="laboratory_name"    />
        <result property="laboratoryCode"    column="laboratory_code"    />
        <result property="deliveryAddress"    column="delivery_address"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.MaintenancePerformanceStaticVO" id="MaintenancePerformanceStaticResult">
        <result property="personId"    column="person_id"    />
        <result property="personName"    column="person_name"    />
        <result property="personCode"    column="person_code"    />
        <result property="maintainUnitCode"    column="maintain_unit_code"    />
        <result property="maintainUnitName"    column="maintain_unit_name"    />
        <result property="totalCount"    column="total_count"    />
        <result property="completeTaskCount"    column="complete_task_count"    />
        <result property="unCompleteTaskCount"    column="un_complete_task_count"    />
        <result property="averageTime"    column="average_time"    />
    </resultMap>

    <resultMap id="ScheduTaskExtendWithTaskDTOResultMap" type="com.mes.smartdispath.domain.vo.ScheduTaskExtendWithTaskDTO">
        <id property="id" column="id"/>
        <result property="executorId" column="executor_id"/>
        <result property="executorName" column="executor_name"/>
        <result property="maintainUnitCode" column="maintain_unit_code"/>
        <result property="maintainUnitName" column="maintain_unit_name"/>
        <result property="taskId" column="task_id"/>
        <result property="taskCode" column="task_code"/>
        <result property="taskName" column="task_name"/>
        <result property="taskStatus" column="task_status"/>
        <result property="dispatchedTime" column="dispatched_time"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
    </resultMap>

    <sql id="selectScheduTaskExtendVo">
        select id, task_id, task_code, task_name, executor_id, executor_name, phone, maintain_unit_code, maintain_unit_name, laboratory_address, laboratory_name, laboratory_code, delivery_address, create_time, create_by, update_time, update_by, is_deleted from tb_schedu_task_extend
    </sql>

    <select id="selectTodayTaskExecutorCount" parameterType="String" resultType="java.lang.Integer">
        select count(distinct a.executor_id)
        from tb_schedu_task_extend a
        left join tb_schedu_task_info b on a.task_id = b.id AND b.is_deleted = 'N'
        where
            a.is_deleted = 'N'
            AND now() >= b.start_time
            AND now() <![CDATA[<=]]> b.end_time
            AND b.task_status = '3'
            AND b.approval_status = 'approved'
            <if test="businessType != null  and businessType != ''"> and b.business_type = #{businessType}</if>
    </select>

    <select id="selectScheduTaskExtendWithTaskList" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultMap="ScheduTaskExtendWithTaskDTOResultMap">
        select t1.id, t1.executor_id, t1.executor_name, t1.maintain_unit_code, t1.maintain_unit_name,
               t1.task_id, t1.task_code, t1.task_name, t2.task_status, t2.dispatched_time, t2.start_time, t2.end_time,
               DATE_FORMAT(t2.start_time, '%Y-%m-%d') as start_date,
               DATE_FORMAT(t2.end_time, '%Y-%m-%d') as end_date
            from tb_schedu_task_extend t1
            left join tb_schedu_task_info t2 on t1.task_id = t2.id
            <where>
                <if test="startDate != null  and startDate != ''"> and t2.start_time >= #{startDate}</if>
                <if test="endDate != null  and endDate != ''"> and t2.start_time <![CDATA[<=]]>  #{endDate}</if>
                <if test="maintainUnitCode != null  and maintainUnitCode != ''"> and t1.maintain_unit_code = #{maintainUnitCode}</if>
            </where>
    </select>

    <select id="selectMaintenanceList" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultMap="MaintenancePerformanceStaticResult">
        SELECT
            e.executor_id AS person_id,
            e.executor_name AS person_name,
            f.user_id as person_code,
            e.maintain_unit_code,
            e.maintain_unit_name,
            COUNT(DISTINCT b.id) AS total_count,
            COUNT(DISTINCT CASE WHEN b.task_status = 4 THEN b.id END) AS complete_task_count,
            COUNT(DISTINCT CASE WHEN b.task_status IN (1, 2, 3) THEN b.id END) AS un_complete_task_count,
            AVG(
                CASE
                    WHEN b.task_status = 4
                THEN TIMESTAMPDIFF(SECOND, b.start_time, b.end_time)
                    ELSE NULL
                END
            ) AS average_time
        FROM
            tb_schedu_task_extend e
            INNER JOIN tb_schedu_task_info b
            ON e.task_id = b.id
            INNER JOIN tb_schedu_task_plan_rel a
            ON b.id = a.task_id
            INNER JOIN tb_schedu_plan_info c
            ON a.plan_id = c.id
            LEFT JOIN tb_res_person_basic f
            ON e.executor_id = f.person_id
        <where>
            <if test="businessType != null  and businessType != ''"> and c.business_type = #{businessType}</if>
            <if test="startDate != null  and startDate != ''"> and b.start_time >= #{startDate}</if>
            <if test="endDate != null  and endDate != ''"> and b.start_time <![CDATA[<=]]>  #{endDate}</if>
            <if test="province != null  and province != ''"> and c.province_code = #{province}</if>
            <if test="city != null  and city != ''"> and c.city_code = #{city}</if>
            <if test="maintainUnitCode != null  and maintainUnitCode != ''"> and e.maintain_unit_code = #{maintainUnitCode}</if>
        </where>
        GROUP BY
            e.executor_id,
            e.executor_name,
            f.user_id,
            e.maintain_unit_code,
            e.maintain_unit_name
        ORDER BY
            total_count DESC
    </select>

    <select id="selectTaskMaintenancePersonCount" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultType="java.lang.Integer">
        select count(distinct a.executor_id) from tb_schedu_task_extend a
        left join tb_schedu_task_info b on a.task_id = b.id
        left join tb_schedu_task_plan_rel c on a.task_id = c.task_id
        left join tb_schedu_plan_info d on c.plan_id = d.id
        <where>
            <if test="businessType != null  and businessType != ''"> and b.business_type = #{businessType}</if>
            <if test="startDate != null  and startDate != ''"> and b.start_time >= #{startDate}</if>
            <if test="endDate != null  and endDate != ''"> and b.start_time <![CDATA[<=]]>  #{endDate}</if>
            <if test="province != null  and province != ''"> and d.province_code = #{province}</if>
            <if test="city != null  and city != ''"> and d.city_code = #{city}</if>
            <if test="maintainUnitCode != null  and maintainUnitCode != ''"> and a.maintain_unit_code = #{maintainUnitCode}</if>
        </where>
    </select>

    <select id="selectScheduTaskExtendList" parameterType="com.mes.smartdispath.domain.dto.ScheduTaskExtendQueryDTO" resultMap="ScheduTaskExtendResult">
        <include refid="selectScheduTaskExtendVo"/>
        <where>  
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="executorId != null  and executorId != ''"> and executor_id = #{executorId}</if>
            <if test="executorName != null  and executorName != ''"> and executor_name like concat('%', #{executorName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="maintainUnitCode != null  and maintainUnitCode != ''"> and maintain_unit_code = #{maintainUnitCode}</if>
            <if test="maintainUnitName != null  and maintainUnitName != ''"> and maintain_unit_name like concat('%', #{maintainUnitName}, '%')</if>
            <if test="laboratoryAddress != null  and laboratoryAddress != ''"> and laboratory_address = #{laboratoryAddress}</if>
            <if test="laboratoryName != null  and laboratoryName != ''"> and laboratory_name like concat('%', #{laboratoryName}, '%')</if>
            <if test="laboratoryCode != null  and laboratoryCode != ''"> and laboratory_code = #{laboratoryCode}</if>
            <if test="deliveryAddress != null  and deliveryAddress != ''"> and delivery_address = #{deliveryAddress}</if>
            <if test="isDeleted != null  and isDeleted != ''"> and is_deleted = #{isDeleted}</if>
        </where>
    </select>
    
    <select id="selectScheduTaskExtendById" parameterType="String" resultMap="ScheduTaskExtendResult">
        <include refid="selectScheduTaskExtendVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduTaskExtend" parameterType="com.mes.smartdispath.domain.ScheduTaskExtend">
        insert into tb_schedu_task_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="executorId != null">executor_id,</if>
            <if test="executorName != null">executor_name,</if>
            <if test="phone != null">phone,</if>
            <if test="maintainUnitCode != null">maintain_unit_code,</if>
            <if test="maintainUnitName != null">maintain_unit_name,</if>
            <if test="laboratoryAddress != null">laboratory_address,</if>
            <if test="laboratoryName != null">laboratory_name,</if>
            <if test="laboratoryCode != null">laboratory_code,</if>
            <if test="deliveryAddress != null">delivery_address,</if>
            <if test="createTime != null and createTime != ''">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDeleted != null">is_deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="executorId != null">#{executorId},</if>
            <if test="executorName != null">#{executorName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="maintainUnitCode != null">#{maintainUnitCode},</if>
            <if test="maintainUnitName != null">#{maintainUnitName},</if>
            <if test="laboratoryAddress != null">#{laboratoryAddress},</if>
            <if test="laboratoryName != null">#{laboratoryName},</if>
            <if test="laboratoryCode != null">#{laboratoryCode},</if>
            <if test="deliveryAddress != null">#{deliveryAddress},</if>
            <if test="createTime != null and createTime != ''">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduTaskExtend" parameterType="java.util.List">
        insert into tb_schedu_task_extend (
            task_id,
            task_code,
            task_name,
            executor_id,
            executor_name,
            phone,
            maintain_unit_code,
            maintain_unit_name,
            laboratory_address,
            laboratory_name,
            laboratory_code,
            delivery_address,
            create_time,
            create_by,
            update_time,
            update_by,
            is_deleted
        ) values
        <foreach collection="list" item="item" separator=",">
        (
            #{item.taskId},
            #{item.taskCode},
            #{item.taskName},
            #{item.executorId},
            #{item.executorName},
            #{item.phone},
            #{item.maintainUnitCode},
            #{item.maintainUnitName},
            #{item.laboratoryAddress},
            #{item.laboratoryName},
            #{item.laboratoryCode},
            #{item.deliveryAddress},
            now(),
            #{item.createBy},
            now(),
            #{item.updateBy},
            'N'
        )
        </foreach>
    </insert>

    <update id="updateScheduTaskExtend" parameterType="com.mes.smartdispath.domain.ScheduTaskExtend">
        update tb_schedu_task_extend
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="executorId != null">executor_id = #{executorId},</if>
            <if test="executorName != null">executor_name = #{executorName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="maintainUnitCode != null">maintain_unit_code = #{maintainUnitCode},</if>
            <if test="maintainUnitName != null">maintain_unit_name = #{maintainUnitName},</if>
            <if test="laboratoryAddress != null">laboratory_address = #{laboratoryAddress},</if>
            <if test="laboratoryName != null">laboratory_name = #{laboratoryName},</if>
            <if test="laboratoryCode != null">laboratory_code = #{laboratoryCode},</if>
            <if test="deliveryAddress != null">delivery_address = #{deliveryAddress},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduTaskExtend" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_task_extend
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.taskId != null and item.taskId != ''">
                    task_id = #{item.taskId},
                </if>
                <if test="item.taskCode != null and item.taskCode != ''">
                    task_code = #{item.taskCode},
                </if>
                <if test="item.taskName != null and item.taskName != ''">
                    task_name = #{item.taskName},
                </if>
                <if test="item.executorId != null">
                    executor_id = #{item.executorId},
                </if>
                <if test="item.executorName != null">
                    executor_name = #{item.executorName},
                </if>
                <if test="item.phone != null">
                    phone = #{item.phone},
                </if>
                <if test="item.maintainUnitCode != null">
                    maintain_unit_code = #{item.maintainUnitCode},
                </if>
                <if test="item.maintainUnitName != null">
                    maintain_unit_name = #{item.maintainUnitName},
                </if>
                <if test="item.laboratoryAddress != null">
                    laboratory_address = #{item.laboratoryAddress},
                </if>
                <if test="item.laboratoryName != null">
                    laboratory_name = #{item.laboratoryName},
                </if>
                <if test="item.laboratoryCode != null">
                    laboratory_code = #{item.laboratoryCode},
                </if>
                <if test="item.deliveryAddress != null">
                    delivery_address = #{item.deliveryAddress},
                </if>
                <if test="item.createTime != null and item.createTime != ''">
                    create_time = #{item.createTime},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduTaskExtendById" parameterType="String">
        delete from tb_schedu_task_extend where id = #{id}
    </delete>

    <delete id="deleteScheduTaskExtendByIds" parameterType="String">
        delete from tb_schedu_task_extend where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteScheduTaskExtendByTaskId" parameterType="String">
        delete from tb_schedu_task_extend where task_id = #{taskId}
    </delete>
</mapper>