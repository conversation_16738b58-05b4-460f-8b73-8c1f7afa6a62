<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ResSiteMapper">

    <resultMap type="com.mes.smartdispath.domain.ResSite" id="ResSiteResult">
        <result property="id" column="id"/>
        <result property="siteNumber" column="site_number"/>
        <result property="siteName" column="site_name"/>
        <result property="siteCode" column="site_code"/>
        <result property="formerCodes" column="former_codes"/>
        <result property="monitoringElement" column="monitoring_element"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="siteType" column="site_type"/>
        <result property="siteStatus" column="site_status"/>
        <result property="siteBatch" column="site_batch"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="operationUnit" column="operation_unit"/>
        <result property="packageId" column="package_id"/>
        <result property="officialLongitude" column="official_longitude"/>
        <result property="officialLatitude" column="official_latitude"/>
        <result property="constructionTime" column="construction_time"/>
        <result property="constructionUnit" column="construction_unit"/>
        <result property="ministryAddress" column="ministry_address"/>
        <result property="actualAddress" column="actual_address"/>
        <result property="networkTime" column="network_time"/>
        <result property="waterQualityTarget" column="water_quality_target"/>
        <result property="riverBasin" column="river_basin"/>
        <result property="waterBody" column="water_body"/>
        <result property="riverLevel" column="river_level"/>
        <result property="inflowWaterBody" column="inflow_water_body"/>
        <result property="sectionAttribute" column="section_attribute"/>
        <result property="sectionDirection" column="section_direction"/>
        <result property="assessmentProvince" column="assessment_province"/>
        <result property="assessmentCity" column="assessment_city"/>
        <result property="hasSalinity" column="has_salinity"/>
        <result property="samplingPoints" column="sampling_points"/>
        <result property="samplingMethod" column="sampling_method"/>
        <result property="isSelfTesting" column="is_self_testing"/>
        <result property="hasAutomaticStation" column="has_automatic_station"/>
        <result property="stationConstructionTime" column="station_construction_time"/>
        <result property="constructionProvince" column="construction_province"/>
        <result property="stationClassification" column="station_classification"/>
        <result property="siteIntroduction" column="site_introduction"/>
        <result property="upstream" column="upstream"/>
        <result property="downstream" column="downstream"/>
        <result property="remarks" column="remarks"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="fullSamplingDuration" column="full_sampling_duration"/>
        <result property="siteAttribute" column="site_attribute"/>
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.RegionSiteCoutVO" id="RegionSiteCountResult">
        <result property="regionCode"    column="region_code"    />
        <result property="count"    column="count"    />
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.ResSiteVO" id="ResSiteVOResult" extends="ResSiteResult">
        <result property="siteId"    column="site_id"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityName"    column="city_name"    />
        <result property="packageName"    column="package_name"    />
        <result property="siteTypeName"    column="site_type_name"    />
        <result property="operationUnitName" column="operation_unit_name" />
    </resultMap>
    
    <resultMap id="maintenanceUnitResult" type="com.mes.smartdispath.domain.vo.MaintenanceUnitVO">
        <result property="maintenanceUnitId"    column="maintenance_unit_id"    />
        <result property="maintenanceUnitName"    column="maintenance_unit_name"    />
    </resultMap>

    <sql id="selectResSiteVo">
        select id, site_number, site_name, site_code, former_codes, monitoring_element, longitude, latitude, site_type, site_status, site_batch, province, city, operation_unit, package_id, official_longitude, official_latitude, construction_time, construction_unit, ministry_address, actual_address, network_time, water_quality_target, river_basin, water_body, river_level, inflow_water_body, section_attribute, section_direction, assessment_province, assessment_city, has_salinity, sampling_points, sampling_method, is_self_testing, has_automatic_station, station_construction_time, construction_province, station_classification, site_introduction, upstream, downstream, remarks, tenant_id, created_by, create_time, updated_by, update_time, full_sampling_duration, site_attribute
        from tb_res_site
    </sql>
    
    <select id="selectMaintenanceUnitList" resultMap="maintenanceUnitResult">
        SELECT distinct a.operation_unit as maintenance_unit_id, b.dept_name AS maintenance_unit_name
        FROM tb_res_site a
             inner JOIN tb_res_dept b on a.operation_unit = b.dept_id
        <where>
            <if test="businessType != null  and businessType != ''"> and a.monitoring_element = #{businessType}</if>
        </where>
    </select>

    <select id="selectPackageSiteInfos" resultMap="ResSiteVOResult">
        SELECT
            a.id as site_id, a.site_name, a.site_code, a.site_type,
            a.province, b.area_name AS province_name,
            a.city, c.area_name AS city_name,
            a.package_id, d.dict_value AS package_name, a.site_status
        FROM tb_res_site a
                 LEFT JOIN tb_res_area_info b on a.province = b.area_code
                 LEFT JOIN tb_res_area_info c on a.city = c.area_code
                 LEFT JOIN tb_sys_dict d on d.class_code = 'package_id' and a.package_id = d.dict_code
        WHERE
            package_id IS NOT NULL
          AND province IS NOT NULL
          AND city IS NOT NULL
        ORDER BY
            package_id,
            province,
            city
    </select>

    <select id="selectRegionSiteInfos" resultMap="ResSiteVOResult">
        SELECT
            a.id as site_id, a.site_name, a.site_code, a.site_type,
            a.province, b.area_name AS province_name,
            a.city, c.area_name AS city_name, a.site_status
        FROM tb_res_site a
                 LEFT JOIN tb_res_area_info b on a.province = b.area_code
                 LEFT JOIN tb_res_area_info c on a.city = c.area_code
        WHERE
            province IS NOT NULL
          AND city IS NOT NULL
        ORDER BY
            province,
            city
    </select>

    <select id="selectRegionSiteMaintainUnitInfos" resultMap="ResSiteVOResult">
        SELECT
            a.operation_unit, d.dept_name AS operation_unit_name,
            a.province, b.area_name AS province_name,
            a.city, c.area_name AS city_name
        FROM tb_res_site a
                 LEFT JOIN tb_res_area_info b on a.province = b.area_code
                 LEFT JOIN tb_res_area_info c on a.city = c.area_code
                 LEFT JOIN tb_res_dept d on a.operation_unit = d.dept_id
        WHERE
            province IS NOT NULL
          AND city IS NOT NULL
          AND operation_unit IS NOT NULL
        GROUP BY a.operation_unit, a.province, b.area_name, a.city, c.area_name, d.dept_name
        ORDER BY
            province,
            city
    </select>

    <select id="selectSiteVOList" resultMap="ResSiteVOResult" parameterType="com.mes.smartdispath.domain.dto.ResSiteQueryDTO">
        SELECT
            a.id as site_id, a.site_name, a.site_code, a.site_type,
            a.province, b.area_name AS province_name,
            a.city, c.area_name AS city_name, a.site_status
        FROM tb_res_site a
                 LEFT JOIN tb_res_area_info b on a.province = b.area_code
                 LEFT JOIN tb_res_area_info c on a.city = c.area_code
        <where>
            <if test="id != null  and id != ''"> and a.id = #{id}</if>
            <if test="siteIdArr != null  and siteIdArr != ''">
                 and a.id in
                    <foreach collection="siteIdArr" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
            </if>
            <if test="monitoringElement != null  and monitoringElement != ''"> and monitoring_element = #{monitoringElement}</if>
            <if test="siteType != null  and siteType != ''"> and site_type = #{siteType}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="hasAutomaticStation != null  and hasAutomaticStation != ''"> and has_automatic_station = #{hasAutomaticStation}</if>
        </where>
    </select>

    <select id="selectResSiteList" parameterType="com.mes.smartdispath.domain.ResSite" resultMap="ResSiteResult">
        <include refid="selectResSiteVo"/>
        <where>  
            <if test="siteNumber != null  and siteNumber != ''"> and site_number = #{siteNumber}</if>
            <if test="siteName != null  and siteName != ''"> and site_name like concat('%', #{siteName}, '%')</if>
            <if test="siteCode != null  and siteCode != ''"> and site_code = #{siteCode}</if>
            <if test="formerCodes != null  and formerCodes != ''"> and former_codes = #{formerCodes}</if>
            <if test="monitoringElement != null  and monitoringElement != ''"> and monitoring_element = #{monitoringElement}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="siteType != null  and siteType != ''"> and site_type = #{siteType}</if>
            <if test="siteStatus != null  and siteStatus != ''"> and site_status = #{siteStatus}</if>
            <if test="siteBatch != null  and siteBatch != ''"> and site_batch = #{siteBatch}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="operationUnit != null  and operationUnit != ''"> and operation_unit = #{operationUnit}</if>
            <if test="packageId != null  and packageId != ''"> and package_id = #{packageId}</if>
            <if test="officialLongitude != null  and officialLongitude != ''"> and official_longitude = #{officialLongitude}</if>
            <if test="officialLatitude != null  and officialLatitude != ''"> and official_latitude = #{officialLatitude}</if>
            <if test="constructionTime != null  and constructionTime != ''"> and construction_time = #{constructionTime}</if>
            <if test="constructionUnit != null  and constructionUnit != ''"> and construction_unit = #{constructionUnit}</if>
            <if test="ministryAddress != null  and ministryAddress != ''"> and ministry_address = #{ministryAddress}</if>
            <if test="actualAddress != null  and actualAddress != ''"> and actual_address = #{actualAddress}</if>
            <if test="networkTime != null  and networkTime != ''"> and network_time = #{networkTime}</if>
            <if test="waterQualityTarget != null  and waterQualityTarget != ''"> and water_quality_target = #{waterQualityTarget}</if>
            <if test="riverBasin != null  and riverBasin != ''"> and river_basin = #{riverBasin}</if>
            <if test="waterBody != null  and waterBody != ''"> and water_body = #{waterBody}</if>
            <if test="riverLevel != null  and riverLevel != ''"> and river_level = #{riverLevel}</if>
            <if test="inflowWaterBody != null  and inflowWaterBody != ''"> and inflow_water_body = #{inflowWaterBody}</if>
            <if test="sectionAttribute != null  and sectionAttribute != ''"> and section_attribute = #{sectionAttribute}</if>
            <if test="sectionDirection != null  and sectionDirection != ''"> and section_direction = #{sectionDirection}</if>
            <if test="assessmentProvince != null  and assessmentProvince != ''"> and assessment_province = #{assessmentProvince}</if>
            <if test="assessmentCity != null  and assessmentCity != ''"> and assessment_city = #{assessmentCity}</if>
            <if test="hasSalinity != null  and hasSalinity != ''"> and has_salinity = #{hasSalinity}</if>
            <if test="samplingPoints != null  and samplingPoints != ''"> and sampling_points = #{samplingPoints}</if>
            <if test="samplingMethod != null  and samplingMethod != ''"> and sampling_method = #{samplingMethod}</if>
            <if test="isSelfTesting != null  and isSelfTesting != ''"> and is_self_testing = #{isSelfTesting}</if>
            <if test="hasAutomaticStation != null  and hasAutomaticStation != ''"> and has_automatic_station = #{hasAutomaticStation}</if>
            <if test="stationConstructionTime != null  and stationConstructionTime != ''"> and station_construction_time = #{stationConstructionTime}</if>
            <if test="constructionProvince != null  and constructionProvince != ''"> and construction_province = #{constructionProvince}</if>
            <if test="stationClassification != null  and stationClassification != ''"> and station_classification = #{stationClassification}</if>
            <if test="siteIntroduction != null  and siteIntroduction != ''"> and site_introduction = #{siteIntroduction}</if>
            <if test="upstream != null  and upstream != ''"> and upstream = #{upstream}</if>
            <if test="downstream != null  and downstream != ''"> and downstream = #{downstream}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="createTime != null  and createTime != ''"> and create_time = #{createTime}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
            <if test="updateTime != null  and updateTime != ''"> and update_time = #{updateTime}</if>
        </where>
    </select>

    <select id="selectRegionSiteCoutList" resultMap="RegionSiteCountResult">
        select province as region_code, count(*) as count
        from tb_res_site
        where province is not null
        group by province
        union all
        select city as region_code, count(*) as count
        from tb_res_site
        where city is not null
        group by city
        union all
        select #{nationwideRegionCode} as region_code, count(*) as count
        from tb_res_site
    </select>
    
    <select id="selectResSiteInfoList" parameterType="com.mes.smartdispath.domain.dto.ResSiteQueryDTO" resultMap="ResSiteVOResult">
        select t1.id as site_id, t1.*, t2.dict_value as site_type_name
        from tb_res_site t1
        left join tb_sys_dict t2
            on t2.class_code = #{siteTypeDictClassCode} and t1.monitoring_element = t2.parent_dict_code and t1.site_type = t2.dict_code
        <where>
            <if test="businessType != null  and businessType != ''"> and t1.monitoring_element = #{businessType}</if>
            <if test="siteNumber != null  and siteNumber != ''"> and t1.site_number = #{siteNumber}</if>
            <if test="siteName != null  and siteName != ''"> and t1.site_name like concat('%', #{siteName}, '%')</if>
            <if test="siteCode != null  and siteCode != ''"> and t1.site_code = #{siteCode}</if>
            <if test="formerCodes != null  and formerCodes != ''"> and t1.former_codes = #{formerCodes}</if>
            <if test="monitoringElement != null  and monitoringElement != ''"> and t1.monitoring_element = #{monitoringElement}</if>
            <if test="longitude != null  and longitude != ''"> and t1.longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and t1.latitude = #{latitude}</if>
            <if test="siteType != null  and siteType != ''"> and t1.site_type = #{siteType}</if>
            <if test="siteStatus != null  and siteStatus != ''"> and t1.site_status = #{siteStatus}</if>
            <if test="siteBatch != null  and siteBatch != ''"> and t1.site_batch = #{siteBatch}</if>
            <if test="province != null  and province != ''"> and t1.province = #{province}</if>
            <if test="city != null  and city != ''"> and t1.city = #{city}</if>
            <if test="operationUnit != null  and operationUnit != ''"> and t1.operation_unit = #{operationUnit}</if>
            <if test="packageId != null  and packageId != ''"> and t1.package_id = #{packageId}</if>
            <if test="officialLongitude != null  and officialLongitude != ''"> and t1.official_longitude = #{officialLongitude}</if>
            <if test="officialLatitude != null  and officialLatitude != ''"> and t1.official_latitude = #{officialLatitude}</if>
            <if test="constructionTime != null  and constructionTime != ''"> and t1.construction_time = #{constructionTime}</if>
            <if test="constructionUnit != null  and constructionUnit != ''"> and t1.construction_unit = #{constructionUnit}</if>
            <if test="ministryAddress != null  and ministryAddress != ''"> and t1.ministry_address = #{ministryAddress}</if>
            <if test="actualAddress != null  and actualAddress != ''"> and t1.actual_address = #{actualAddress}</if>
            <if test="networkTime != null  and networkTime != ''"> and t1.network_time = #{networkTime}</if>
            <if test="waterQualityTarget != null  and waterQualityTarget != ''"> and t1.water_quality_target = #{waterQualityTarget}</if>
            <if test="riverBasin != null  and riverBasin != ''"> and t1.river_basin = #{riverBasin}</if>
            <if test="waterBody != null  and waterBody != ''"> and t1.water_body = #{waterBody}</if>
            <if test="riverLevel != null  and riverLevel != ''"> and t1.river_level = #{riverLevel}</if>
            <if test="inflowWaterBody != null  and inflowWaterBody != ''"> and t1.inflow_water_body = #{inflowWaterBody}</if>
            <if test="sectionAttribute != null  and sectionAttribute != ''"> and t1.section_attribute = #{sectionAttribute}</if>
            <if test="sectionDirection != null  and sectionDirection != ''"> and t1.section_direction = #{sectionDirection}</if>
            <if test="assessmentProvince != null  and assessmentProvince != ''"> and t1.assessment_province = #{assessmentProvince}</if>
            <if test="assessmentCity != null  and assessmentCity != ''"> and t1.assessment_city = #{assessmentCity}</if>
            <if test="hasSalinity != null  and hasSalinity != ''"> and t1.has_salinity = #{hasSalinity}</if>
            <if test="samplingPoints != null  and samplingPoints != ''"> and t1.sampling_points = #{samplingPoints}</if>
            <if test="samplingMethod != null  and samplingMethod != ''"> and t1.sampling_method = #{samplingMethod}</if>
            <if test="isSelfTesting != null  and isSelfTesting != ''"> and t1.is_self_testing = #{isSelfTesting}</if>
            <if test="hasAutomaticStation != null  and hasAutomaticStation != ''"> and t1.has_automatic_station = #{hasAutomaticStation}</if>
            <if test="stationConstructionTime != null  and stationConstructionTime != ''"> and t1.station_construction_time = #{stationConstructionTime}</if>
            <if test="constructionProvince != null  and constructionProvince != ''"> and t1.construction_province = #{constructionProvince}</if>
            <if test="stationClassification != null  and stationClassification != ''"> and t1.station_classification = #{stationClassification}</if>
            <if test="siteIntroduction != null  and siteIntroduction != ''"> and t1.site_introduction = #{siteIntroduction}</if>
            <if test="upstream != null  and upstream != ''"> and t1.upstream = #{upstream}</if>
            <if test="downstream != null  and downstream != ''"> and t1.downstream = #{downstream}</if>
            <if test="remarks != null  and remarks != ''"> and t1.remarks = #{remarks}</if>
            <if test="tenantId != null  and tenantId != ''"> and t1.tenant_id = #{tenantId}</if>
            <if test="createdBy != null  and createdBy != ''"> and t1.created_by = #{createdBy}</if>
            <if test="createTime != null  and createTime != ''"> and t1.create_time = #{createTime}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and t1.updated_by = #{updatedBy}</if>
            <if test="updateTime != null  and updateTime != ''"> and t1.update_time = #{updateTime}</if>
            <if test="siteIdArr != null  and siteIdArr != ''">
                   and t1.id in
                        <foreach collection="siteIdArr" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
            </if>
        </where>
    </select>
    
    <select id="selectResSiteById" parameterType="String" resultMap="ResSiteResult">
        <include refid="selectResSiteVo"/>
        where id = #{id}
    </select>
</mapper>