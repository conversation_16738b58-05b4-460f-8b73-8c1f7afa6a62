<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduMonitorActivityInfoMapper">

    <resultMap type="com.mes.smartdispath.domain.ScheduMonitorActivityInfo" id="ScheduMonitorActivityInfoResult">
        <result property="id" column="id"/>
        <result property="activityCode" column="activity_code"/>
        <result property="activityType" column="activity_type"/>
        <result property="businessType" column="business_type"/>
        <result property="siteType" column="site_type"/>
        <result property="isAutosite" column="is_autosite"/>
        <result property="activityTypeCode" column="activity_type_code"/>
        <result property="activityTypeName" column="activity_type_name"/>
        <result property="activitySubtypeCode" column="activity_subtype_code"/>
        <result property="activitySubtypeName" column="activity_subtype_name"/>
        <result property="activityDesc" column="activity_desc"/>
        <result property="isAssoactivity" column="is_assoactivity"/>
        <result property="assoactivityCode" column="assoactivity_code"/>
        <result property="scheduTime" column="schedu_time"/>
        <result property="scheduDesc" column="schedu_desc"/>
        <result property="testItems" column="test_items"/>
        <result property="isRegular" column="is_regular"/>
        <result property="isImportant" column="is_important"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectScheduMonitorActivityInfoVo">
        select id, activity_code, activity_type, business_type, site_type, is_autosite, activity_type_code, activity_type_name, activity_subtype_code, activity_subtype_name, activity_desc, is_assoactivity, assoactivity_code, schedu_time, schedu_desc, test_items, is_regular, is_important, status, create_by, create_time, update_by, update_time, tenant_id
        from tb_schedu_monitor_activity_info
    </sql>

    <!--  获取监测活动大类  -->
    <select id="selectActivityTypeList" parameterType="com.mes.smartdispath.domain.dto.ScheduMonitorActivityInfoQueryDTO"
            resultMap="ScheduMonitorActivityInfoResult">
        select distinct activity_type_code, activity_type_name, business_type, site_type, is_autosite
        from tb_schedu_monitor_activity_info
        where status = 'A'
        <if test="businessType != null  and businessType != ''">and business_type = #{businessType}</if>
        <if test="siteType != null  and siteType != ''">and site_type = #{siteType}</if>
        <if test="isRegular != null  and isRegular != ''">and is_regular = #{isRegular}</if>
        <if test="isAutositeArr != null  and isAutositeArr != ''">
            and is_autosite in
            <foreach collection="isAutositeArr" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--  获取监测活动小类  -->
    <select id="selectActivitySubtypeList" parameterType="com.mes.smartdispath.domain.dto.ScheduMonitorActivityInfoQueryDTO"
            resultMap="ScheduMonitorActivityInfoResult">
        SELECT distinct
        activity_subtype_code,activity_subtype_name,business_type,site_type, test_items, is_regular, is_important
        FROM tb_schedu_monitor_activity_info
        where status = 'A'
        <if test="businessType != null  and businessType != ''">and business_type = #{businessType}</if>
        <if test="siteType != null  and siteType != ''">and site_type = #{siteType}</if>
        <if test="activityTypeCode != null  and activityTypeCode != ''">and activity_type_code = #{activityTypeCode}
        </if>
        <if test="isRegular != null  and isRegular != ''">and is_regular = #{isRegular}</if>
        <if test="isAutositeArr != null  and isAutositeArr != ''">
            and is_autosite in
                <foreach collection="isAutositeArr" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
    </select>

    <select id="selectScheduMonitorActivityInfoList" parameterType="com.mes.smartdispath.domain.dto.ScheduMonitorActivityInfoQueryDTO"
            resultMap="ScheduMonitorActivityInfoResult">
        <include refid="selectScheduMonitorActivityInfoVo"/>
        <where>
            <if test="activityCodeArr != null  and activityCodeArr != ''">
                and activity_code in
                    <foreach collection="activityCodeArr" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="activityCode != null  and activityCode != ''">
                and activity_code = #{activityCode}
            </if>
            <if test="activityType != null  and activityType != ''">
                and activity_type = #{activityType}
            </if>
            <if test="businessType != null  and businessType != ''">
                and business_type = #{businessType}
            </if>
            <if test="siteType != null  and siteType != ''">
                and site_type = #{siteType}
            </if>
            <if test="isAutosite != null  and isAutosite != ''">
                and is_autosite = #{isAutosite}
            </if>
            <if test="activityTypeCode != null  and activityTypeCode != ''">
                and activity_type_code = #{activityTypeCode}
            </if>
            <if test="activityTypeName != null  and activityTypeName != ''">
                and activity_type_name like concat('%', #{activityTypeName}, '%')
            </if>
            <if test="activitySubtypeCode != null  and activitySubtypeCode != ''">
                and activity_subtype_code = #{activitySubtypeCode}
            </if>
            <if test="activitySubtypeName != null  and activitySubtypeName != ''">
                and activity_subtype_name like concat('%', #{activitySubtypeName}, '%')
            </if>
            <if test="activityDesc != null  and activityDesc != ''">
                and activity_desc = #{activityDesc}
            </if>
            <if test="isAssoactivity != null  and isAssoactivity != ''">
                and is_assoactivity = #{isAssoactivity}
            </if>
            <if test="assoactivityCode != null  and assoactivityCode != ''">
                and assoactivity_code = #{assoactivityCode}
            </if>
            <if test="scheduTime != null  and scheduTime != ''">
                and schedu_time = #{scheduTime}
            </if>
            <if test="scheduDesc != null  and scheduDesc != ''">
                and schedu_desc = #{scheduDesc}
            </if>
            <if test="testItems != null  and testItems != ''">
                and test_items = #{testItems}
            </if>
            <if test="isRegular != null  and isRegular != ''">
                and is_regular = #{isRegular}
            </if>
            <if test="isImportant != null  and isImportant != ''">
                and is_important = #{isImportant}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>
            <if test="tenantId != null  and tenantId != ''">
                and tenant_id = #{tenantId}
            </if>
            <if test="businessTypeArr != null  and businessTypeArr != ''">
                and business_type in
                <foreach collection="businessTypeArr" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="activityTypeArr != null  and activityTypeArr != ''">
                and activity_type_code in
                <foreach collection="activityTypeArr" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="activitySubtypeArr != null  and activitySubtypeArr != ''">
                and activity_subtype_code in
                <foreach collection="activitySubtypeArr" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectScheduMonitorActivityInfoById" parameterType="String"
            resultMap="ScheduMonitorActivityInfoResult">
        <include refid="selectScheduMonitorActivityInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectScheduMonitorActivityInfoByCode" resultMap="ScheduMonitorActivityInfoResult">
        <include refid="selectScheduMonitorActivityInfoVo"/>
        where activity_type_code = #{activityTypeCode} and activity_subtype_code = #{activitySubtypeCode}
    </select>

    <insert id="insertScheduMonitorActivityInfo" parameterType="com.mes.smartdispath.domain.ScheduMonitorActivityInfo">
        insert into tb_schedu_monitor_activity_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="activityCode != null and activityCode != ''">activity_code,
            </if>
            <if test="activityType != null">activity_type,
            </if>
            <if test="businessType != null">business_type,
            </if>
            <if test="siteType != null">site_type,
            </if>
            <if test="isAutosite != null">is_autosite,
            </if>
            <if test="activityTypeCode != null">activity_type_code,
            </if>
            <if test="activityTypeName != null">activity_type_name,
            </if>
            <if test="activitySubtypeCode != null">activity_subtype_code,
            </if>
            <if test="activitySubtypeName != null">activity_subtype_name,
            </if>
            <if test="activityDesc != null">activity_desc,
            </if>
            <if test="isAssoactivity != null">is_assoactivity,
            </if>
            <if test="assoactivityCode != null">assoactivity_code,
            </if>
            <if test="scheduTime != null">schedu_time,
            </if>
            <if test="scheduDesc != null">schedu_desc,
            </if>
            <if test="testItems != null">test_items,
            </if>
            <if test="isRegular != null">is_regular,
            </if>
            <if test="isImportant != null">is_important,
            </if>
            <if test="status != null">status,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="tenantId != null">tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="activityCode != null and activityCode != ''">#{activityCode},
            </if>
            <if test="activityType != null">#{activityType},
            </if>
            <if test="businessType != null">#{businessType},
            </if>
            <if test="siteType != null">#{siteType},
            </if>
            <if test="isAutosite != null">#{isAutosite},
            </if>
            <if test="activityTypeCode != null">#{activityTypeCode},
            </if>
            <if test="activityTypeName != null">#{activityTypeName},
            </if>
            <if test="activitySubtypeCode != null">#{activitySubtypeCode},
            </if>
            <if test="activitySubtypeName != null">#{activitySubtypeName},
            </if>
            <if test="activityDesc != null">#{activityDesc},
            </if>
            <if test="isAssoactivity != null">#{isAssoactivity},
            </if>
            <if test="assoactivityCode != null">#{assoactivityCode},
            </if>
            <if test="scheduTime != null">#{scheduTime},
            </if>
            <if test="scheduDesc != null">#{scheduDesc},
            </if>
            <if test="testItems != null">#{testItems},
            </if>
            <if test="isRegular != null">#{isRegular},
            </if>
            <if test="isImportant != null">#{isImportant},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="tenantId != null">#{tenantId},
            </if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduMonitorActivityInfo" parameterType="java.util.List">
        insert into tb_schedu_monitor_activity_info (
        id,
        activity_code,
        activity_type,
        business_type,
        site_type,
        is_autosite,
        activity_type_code,
        activity_type_name,
        activity_subtype_code,
        activity_subtype_name,
        activity_desc,
        is_assoactivity,
        assoactivity_code,
        schedu_time,
        schedu_desc,
        test_items,
        is_regular,
        is_important,
        status,
        create_by,
        create_time,
        update_by,
        update_time,
        tenant_id
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.activityCode},
            #{item.activityType},
            #{item.businessType},
            #{item.siteType},
            #{item.isAutosite},
            #{item.activityTypeCode},
            #{item.activityTypeName},
            #{item.activitySubtypeCode},
            #{item.activitySubtypeName},
            #{item.activityDesc},
            #{item.isAssoactivity},
            #{item.assoactivityCode},
            #{item.scheduTime},
            #{item.scheduDesc},
            #{item.testItems},
            #{item.isRegular},
            #{item.isImportant},
            #{item.status},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            #{item.tenantId}
            )
        </foreach>
    </insert>

    <update id="updateScheduMonitorActivityInfo" parameterType="com.mes.smartdispath.domain.ScheduMonitorActivityInfo">
        update tb_schedu_monitor_activity_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityCode != null and activityCode != ''">activity_code =
                #{activityCode},
            </if>
            <if test="activityType != null">activity_type =
                #{activityType},
            </if>
            <if test="businessType != null">business_type =
                #{businessType},
            </if>
            <if test="siteType != null">site_type =
                #{siteType},
            </if>
            <if test="isAutosite != null">is_autosite =
                #{isAutosite},
            </if>
            <if test="activityTypeCode != null">activity_type_code =
                #{activityTypeCode},
            </if>
            <if test="activityTypeName != null">activity_type_name =
                #{activityTypeName},
            </if>
            <if test="activitySubtypeCode != null">activity_subtype_code =
                #{activitySubtypeCode},
            </if>
            <if test="activitySubtypeName != null">activity_subtype_name =
                #{activitySubtypeName},
            </if>
            <if test="activityDesc != null">activity_desc =
                #{activityDesc},
            </if>
            <if test="isAssoactivity != null">is_assoactivity =
                #{isAssoactivity},
            </if>
            <if test="assoactivityCode != null">assoactivity_code =
                #{assoactivityCode},
            </if>
            <if test="scheduTime != null">schedu_time =
                #{scheduTime},
            </if>
            <if test="scheduDesc != null">schedu_desc =
                #{scheduDesc},
            </if>
            <if test="testItems != null">test_items =
                #{testItems},
            </if>
            <if test="isRegular != null">is_regular =
                #{isRegular},
            </if>
            <if test="isImportant != null">is_important =
                #{isImportant},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updateBy != null">update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="tenantId != null">tenant_id =
                #{tenantId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduMonitorActivityInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_monitor_activity_info
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.activityCode != null and item.activityCode != ''">
                    activity_code = #{item.activityCode},
                </if>
                <if test="item.activityType != null">
                    activity_type = #{item.activityType},
                </if>
                <if test="item.businessType != null">
                    business_type = #{item.businessType},
                </if>
                <if test="item.siteType != null">
                    site_type = #{item.siteType},
                </if>
                <if test="item.isAutosite != null">
                    is_autosite = #{item.isAutosite},
                </if>
                <if test="item.activityTypeCode != null">
                    activity_type_code = #{item.activityTypeCode},
                </if>
                <if test="item.activityTypeName != null">
                    activity_type_name = #{item.activityTypeName},
                </if>
                <if test="item.activitySubtypeCode != null">
                    activity_subtype_code = #{item.activitySubtypeCode},
                </if>
                <if test="item.activitySubtypeName != null">
                    activity_subtype_name = #{item.activitySubtypeName},
                </if>
                <if test="item.activityDesc != null">
                    activity_desc = #{item.activityDesc},
                </if>
                <if test="item.isAssoactivity != null">
                    is_assoactivity = #{item.isAssoactivity},
                </if>
                <if test="item.assoactivityCode != null">
                    assoactivity_code = #{item.assoactivityCode},
                </if>
                <if test="item.scheduTime != null">
                    schedu_time = #{item.scheduTime},
                </if>
                <if test="item.scheduDesc != null">
                    schedu_desc = #{item.scheduDesc},
                </if>
                <if test="item.testItems != null">
                    test_items = #{item.testItems},
                </if>
                <if test="item.isRegular != null">
                    is_regular = #{item.isRegular},
                </if>
                <if test="item.isImportant != null">
                    is_important = #{item.isImportant},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.tenantId != null">
                    tenant_id = #{item.tenantId},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduMonitorActivityInfoById" parameterType="String">
        delete
        from tb_schedu_monitor_activity_info where id = #{id}
    </delete>

    <delete id="deleteScheduMonitorActivityInfoByIds" parameterType="String">
        delete from tb_schedu_monitor_activity_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>