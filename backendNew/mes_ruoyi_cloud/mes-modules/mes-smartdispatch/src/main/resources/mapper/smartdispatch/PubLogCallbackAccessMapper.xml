<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.PubLogCallbackAccessMapper">
    <resultMap type="com.mes.smartdispath.domain.PubLogCallbackAccess" id="PubLogCallbackAccessResult">
        <result property="logId" column="log_id"/>
        <result property="logName" column="log_name"/>
        <result property="reqIp" column="req_ip"/>
        <result property="reqUri" column="req_uri"/>
        <result property="reqMethod" column="req_method"/>
        <result property="reqContent" column="req_content"/>
        <result property="resContent" column="res_content"/>
        <result property="accessTime" column="access_time"/>
        <result property="resTime" column="res_time"/>
        <result property="createTime" column="create_time"/>
        <result property="source" column="source"/>
    </resultMap>

    <sql id="selectPubLogCallbackAccessVo">
        select log_id,
               log_name,
               req_ip,
               req_uri,
               req_method,
               req_content,
               res_content,
               access_time,
               res_time,
               create_time,
               source
        from tb_pub_log_callback_access
    </sql>

    <select id="selectPubLogCallbackAccessList" parameterType="com.mes.smartdispath.domain.PubLogCallbackAccess"
            resultMap="PubLogCallbackAccessResult">
        <include refid="selectPubLogCallbackAccessVo"/>
        <where>
            <if test="logName != null  and logName != ''">
                and log_name like concat('%', #{logName}, '%')
            </if>
            <if test="reqIp != null  and reqIp != ''">
                and req_ip = #{reqIp}
            </if>
            <if test="reqUri != null  and reqUri != ''">
                and req_uri = #{reqUri}
            </if>
            <if test="reqMethod != null  and reqMethod != ''">
                and req_method = #{reqMethod}
            </if>
            <if test="reqContent != null  and reqContent != ''">
                and req_content = #{reqContent}
            </if>
            <if test="resContent != null  and resContent != ''">
                and res_content = #{resContent}
            </if>
            <if test="accessTime != null  and accessTime != ''">
                and access_time = #{accessTime}
            </if>
            <if test="resTime != null  and resTime != ''">
                and res_time = #{resTime}
            </if>
            <if test="source != null  and source != ''">
                and source = #{source}
            </if>
        </where>
    </select>

    <select id="selectPubLogCallbackAccessByLogId" parameterType="String" resultMap="PubLogCallbackAccessResult">
        <include refid="selectPubLogCallbackAccessVo"/>
        where log_id = #{logId}
    </select>

    <insert id="insertPubLogCallbackAccess" parameterType="com.mes.smartdispath.domain.PubLogCallbackAccess">
        insert into tb_pub_log_callback_access
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logId != null">
                log_id,
            </if>
            <if test="logName != null">
                log_name,
            </if>
            <if test="reqIp != null">
                req_ip,
            </if>
            <if test="reqUri != null">
                req_uri,
            </if>
            <if test="reqMethod != null">
                req_method,
            </if>
            <if test="reqContent != null">
                req_content,
            </if>
            <if test="resContent != null">
                res_content,
            </if>
            <if test="accessTime != null">
                access_time,
            </if>
            <if test="resTime != null">
                res_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="source != null">
                source,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logId != null">
                #{logId},
            </if>
            <if test="logName != null">
                #{logName},
            </if>
            <if test="reqIp != null">
                #{reqIp},
            </if>
            <if test="reqUri != null">
                #{reqUri},
            </if>
            <if test="reqMethod != null">
                #{reqMethod},
            </if>
            <if test="reqContent != null">
                #{reqContent},
            </if>
            <if test="resContent != null">
                #{resContent},
            </if>
            <if test="accessTime != null">
                #{accessTime},
            </if>
            <if test="resTime != null">
                #{resTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="source != null">
                #{source},
            </if>
        </trim>
    </insert>

    <update id="updatePubLogCallbackAccess" parameterType="com.mes.smartdispath.domain.PubLogCallbackAccess">
        update tb_pub_log_callback_access
        <trim prefix="SET" suffixOverrides=",">
            <if test="logName != null">
                log_name = #{logName},
            </if>
            <if test="reqIp != null">
                req_ip = #{reqIp},
            </if>
            <if test="reqUri != null">
                req_uri = #{reqUri},
            </if>
            <if test="reqMethod != null">
                req_method = #{reqMethod},
            </if>
            <if test="reqContent != null">
                req_content = #{reqContent},
            </if>
            <if test="resContent != null">
                res_content = #{resContent},
            </if>
            <if test="accessTime != null">
                access_time = #{accessTime},
            </if>
            <if test="resTime != null">
                res_time = #{resTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="source != null">
                source = #{source},
            </if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deletePubLogCallbackAccessByLogId" parameterType="String">
        delete
        from tb_pub_log_callback_access
        where log_id = #{logId}
    </delete>

    <delete id="deletePubLogCallbackAccessByLogIds" parameterType="String">
        delete
        from tb_pub_log_callback_access where log_id in
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
</mapper>