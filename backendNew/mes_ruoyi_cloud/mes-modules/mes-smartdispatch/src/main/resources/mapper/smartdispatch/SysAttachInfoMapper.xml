<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.SysAttachInfoMapper">
    
    <resultMap type="com.mes.smartdispath.domain.SysAttachInfo" id="SysAttachInfoResult">
        <result property="id"    column="id"    />
        <result property="attachId"    column="attach_id"    />
        <result property="attachName"    column="attach_name"    />
        <result property="attachType"    column="attach_type"    />
        <result property="attachPath"    column="attach_path"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="status"    column="status"    />
        <result property="attachSize"    column="attach_size"    />
    </resultMap>

    <sql id="selectSysAttachInfoVo">
        select id, attach_id, attach_name, attach_type, attach_path, create_time, create_by, update_time, update_by, status, attach_size from tb_sys_attach_info
    </sql>

    <select id="selectSysAttachInfoList" parameterType="com.mes.smartdispath.domain.SysAttachInfo" resultMap="SysAttachInfoResult">
        <include refid="selectSysAttachInfoVo"/>
        <where>  
            <if test="attachId != null  and attachId != ''"> and attach_id = #{attachId}</if>
            <if test="attachName != null  and attachName != ''"> and attach_name like concat('%', #{attachName}, '%')</if>
            <if test="attachType != null  and attachType != ''"> and attach_type = #{attachType}</if>
            <if test="attachPath != null  and attachPath != ''"> and attach_path = #{attachPath}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="attachSize != null  and attachSize != ''"> and attach_size = #{attachSize}</if>
        </where>
    </select>
    
    <select id="selectSysAttachInfoById" parameterType="String" resultMap="SysAttachInfoResult">
        <include refid="selectSysAttachInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysAttachInfo" parameterType="com.mes.smartdispath.domain.SysAttachInfo">
        insert into tb_sys_attach_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="attachId != null and attachId != ''">attach_id,</if>
            <if test="attachName != null">attach_name,</if>
            <if test="attachType != null">attach_type,</if>
            <if test="attachPath != null">attach_path,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="attachSize != null">attach_size,</if>
            status,
            create_time,
            update_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="attachId != null and attachId != ''">#{attachId},</if>
            <if test="attachName != null">#{attachName},</if>
            <if test="attachType != null">#{attachType},</if>
            <if test="attachPath != null">#{attachPath},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="attachSize != null">#{attachSize},</if>
            'A',
            now(),
            now(),
         </trim>
    </insert>

    <update id="updateSysAttachInfo" parameterType="com.mes.smartdispath.domain.SysAttachInfo">
        update tb_sys_attach_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="attachId != null and attachId != ''">attach_id = #{attachId},</if>
            <if test="attachName != null">attach_name = #{attachName},</if>
            <if test="attachType != null">attach_type = #{attachType},</if>
            <if test="attachPath != null">attach_path = #{attachPath},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="attachSize != null">attach_size = #{attachSize},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAttachInfoById" parameterType="String">
        delete from tb_sys_attach_info where id = #{id}
    </delete>

    <delete id="deleteSysAttachInfoByIds" parameterType="String">
        delete from tb_sys_attach_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>