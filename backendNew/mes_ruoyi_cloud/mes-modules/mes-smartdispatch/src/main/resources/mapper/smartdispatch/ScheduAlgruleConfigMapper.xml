<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduAlgruleConfigMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduAlgruleConfig" id="ScheduAlgruleConfigResult">
        <result property="id"    column="id"    />
        <result property="algruleCode"    column="algrule_code"    />
        <result property="algruleName"    column="algrule_name"    />
        <result property="algruleType"    column="algrule_type"    />
        <result property="algruleSubtype"    column="algrule_subtype"    />
        <result property="algruleExtend"    column="algrule_extend"    />
        <result property="algruleStatus"    column="algrule_status"    />
        <result property="effectiveRegion"    column="effective_region"    />
        <result property="algorithmId"    column="algorithm_id"    />
        <result property="algorithmName"    column="algorithm_name"    />
        <result property="algorithmCode"    column="algorithm_code"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>
    
    <resultMap id="ScheduAlgruleConfigVOResult" type="com.mes.smartdispath.domain.vo.ScheduAlgruleConfigVO">
        <result property="algruleTypeName"    column="algrule_type_name"    />
        <result property="algruleSubtypeName"    column="algrule_subtype_name"    />
        <result property="effectiveRegionName"    column="effective_region_name"    />
    </resultMap>

    <sql id="selectScheduAlgruleConfigVo">
        select id, algrule_code, algrule_name, algrule_type, algrule_subtype, algrule_extend, algrule_status, effective_region, algorithm_id, algorithm_name, algorithm_code, create_by, create_time, update_by, update_time, is_deleted, tenant_id from tb_schedu_algrule_config
    </sql>

    <select id="selectScheduAlgruleConfigList" parameterType="com.mes.smartdispath.domain.dto.ScheduAlgruleConfigQueryDTO" resultMap="ScheduAlgruleConfigResult">
        SELECT
            a.id,
            a.algrule_code,
            a.algrule_name,
            a.algrule_type,
            CASE
                    WHEN a.algrule_type = #{algruleSubtypeBasClassCode} THEN '基础规则'
                    WHEN a.algrule_type = #{algruleSubtypeDynClassCode} THEN '动态规则'
                ELSE '未知类型'
            END AS algrule_type_name,
            a.algrule_subtype,
            b.dict_value AS algrule_subtype_name,
            a.algrule_status,
            a.effective_region,
            c.area_name AS effective_region_name,
            a.algorithm_id,
            a.algorithm_name,
            a.algorithm_code,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.is_deleted,
            a.tenant_id
        FROM tb_schedu_algrule_config a
            LEFT JOIN tb_sys_dict b
            ON (
                (a.algrule_type = #{algruleSubtypeBasClassCode} AND b.class_code = #{algruleSubtypeBasClassCode})
                OR
                (a.algrule_type = #{algruleSubtypeDynClassCode} AND b.class_code = #{algruleSubtypeDynClassCode})
            )
            LEFT JOIN tb_res_area_info c on a.effective_region = c.area_code
        where a.is_deleted = 'N'
            <if test="algruleCode != null  and algruleCode != ''"> and a.algrule_code like concat('%', #{algruleCode}, '%')</if>
            <if test="algruleName != null  and algruleName != ''"> and a.algrule_name like concat('%', #{algruleName}, '%')</if>
            <if test="algruleType != null  and algruleType != ''"> and a.algrule_type = #{algruleType}</if>
            <if test="algruleSubtype != null  and algruleSubtype != ''"> and a.algrule_subtype = #{algruleSubtype}</if>
            <if test="algruleExtend != null  and algruleExtend != ''"> and a.algrule_extend = #{algruleExtend}</if>
            <if test="algruleStatus != null  and algruleStatus != ''"> and a.algrule_status = #{algruleStatus}</if>
            <if test="effectiveRegion != null  and effectiveRegion != '' and null == effectiveRegionArr"> and a.effective_region = #{effectiveRegion}</if>
            <if test="effectiveRegionArr != null  and effectiveRegionArr != ''">
                and a.effective_region in
                    <foreach item="item" collection="effectiveRegionArr" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="algorithmId != null  and algorithmId != ''"> and a.algorithm_id = #{algorithmId}</if>
            <if test="algorithmName != null  and algorithmName != ''"> and a.algorithm_name like concat('%', #{algorithmName}, '%')</if>
            <if test="algorithmCode != null  and algorithmCode != ''"> and a.algorithm_code = #{algorithmCode}</if>
            <if test="tenantId != null  and tenantId != ''"> and a.tenant_id = #{tenantId}</if>
            <if test="createUser != null  and createUser != ''"> and a.create_user = #{createUser}</if>
            <if test="startTime != null  and startTime != ''"> and a.create_time >= #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and a.create_time <![CDATA[<=]]> #{endTime}</if>
    </select>
    
    <select id="selectScheduAlgruleConfigById" parameterType="String" resultMap="ScheduAlgruleConfigResult">
        <include refid="selectScheduAlgruleConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduAlgruleConfig" parameterType="com.mes.smartdispath.domain.ScheduAlgruleConfig">
        insert into tb_schedu_algrule_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="algruleCode != null and algruleCode != ''">algrule_code,</if>
            <if test="algruleName != null and algruleName != ''">algrule_name,</if>
            <if test="algruleType != null and algruleType != ''">algrule_type,</if>
            <if test="algruleSubtype != null">algrule_subtype,</if>
            <if test="algruleExtend != null">algrule_extend,</if>
            <if test="algruleStatus != null and algruleStatus != ''">algrule_status,</if>
            <if test="effectiveRegion != null">effective_region,</if>
            <if test="algorithmId != null">algorithm_id,</if>
            <if test="algorithmName != null">algorithm_name,</if>
            <if test="algorithmCode != null">algorithm_code,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null and createTime != ''">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="tenantId != null">tenant_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="algruleCode != null and algruleCode != ''">#{algruleCode},</if>
            <if test="algruleName != null and algruleName != ''">#{algruleName},</if>
            <if test="algruleType != null and algruleType != ''">#{algruleType},</if>
            <if test="algruleSubtype != null">#{algruleSubtype},</if>
            <if test="algruleExtend != null">#{algruleExtend},</if>
            <if test="algruleStatus != null and algruleStatus != ''">#{algruleStatus},</if>
            <if test="effectiveRegion != null">#{effectiveRegion},</if>
            <if test="algorithmId != null">#{algorithmId},</if>
            <if test="algorithmName != null">#{algorithmName},</if>
            <if test="algorithmCode != null">#{algorithmCode},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null and createTime != ''">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="tenantId != null">#{tenantId},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduAlgruleConfig" parameterType="java.util.List">
        insert into tb_schedu_algrule_config (
            algrule_code,
            algrule_name,
            algrule_type,
            algrule_subtype,
            algrule_extend,
            algrule_status,
            effective_region,
            algorithm_id,
            algorithm_name,
            algorithm_code,
            create_by,
            create_time,
            update_by,
            update_time,
            is_deleted,
            tenant_id
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.algruleCode},
                #{item.algruleName},
                #{item.algruleType},
                #{item.algruleSubtype},
                #{item.algruleExtend},
                #{item.algruleStatus},
                #{item.effectiveRegion},
                #{item.algorithmId},
                #{item.algorithmName},
                #{item.algorithmCode},
                now(),
                #{item.createTime},
                #{item.updateBy},
                now(),
                'N',
                #{item.tenantId}
            )
        </foreach>
    </insert>

    <update id="updateScheduAlgruleConfig" parameterType="com.mes.smartdispath.domain.ScheduAlgruleConfig">
        update tb_schedu_algrule_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="algruleCode != null and algruleCode != ''">algrule_code = #{algruleCode},</if>
            <if test="algruleName != null and algruleName != ''">algrule_name = #{algruleName},</if>
            <if test="algruleType != null and algruleType != ''">algrule_type = #{algruleType},</if>
            <if test="algruleSubtype != null">algrule_subtype = #{algruleSubtype},</if>
            <if test="algruleExtend != null">algrule_extend = #{algruleExtend},</if>
            <if test="algruleStatus != null and algruleStatus != ''">algrule_status = #{algruleStatus},</if>
            <if test="effectiveRegion != null">effective_region = #{effectiveRegion},</if>
            <if test="algorithmId != null">algorithm_id = #{algorithmId},</if>
            <if test="algorithmName != null">algorithm_name = #{algorithmName},</if>
            <if test="algorithmCode != null">algorithm_code = #{algorithmCode},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduAlgruleConfig" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_algrule_config
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.algruleCode != null and item.algruleCode != ''">
                    algrule_code = #{item.algruleCode},
                </if>
                <if test="item.algruleName != null and item.algruleName != ''">
                    algrule_name = #{item.algruleName},
                </if>
                <if test="item.algruleType != null and item.algruleType != ''">
                    algrule_type = #{item.algruleType},
                </if>
                <if test="item.algruleSubtype != null">
                    algrule_subtype = #{item.algruleSubtype},
                </if>
                <if test="item.algruleExtend != null">
                    algrule_extend = #{item.algruleExtend},
                </if>
                <if test="item.algruleStatus != null and item.algruleStatus != ''">
                    algrule_status = #{item.algruleStatus},
                </if>
                <if test="item.effectiveRegion != null">
                    effective_region = #{item.effectiveRegion},
                </if>
                <if test="item.algorithmId != null">
                    algorithm_id = #{item.algorithmId},
                </if>
                <if test="item.algorithmName != null">
                    algorithm_name = #{item.algorithmName},
                </if>
                <if test="item.algorithmCode != null">
                    algorithm_code = #{item.algorithmCode},
                </if>
                <if test="item.createBy != null and item.createBy != ''">
                    create_by = #{item.createBy},
                </if>
                <if test="item.createTime != null and item.createTime != ''">
                    create_time = #{item.createTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted},
                </if>
                <if test="item.tenantId != null">
                    tenant_id = #{item.tenantId},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduAlgruleConfigById" parameterType="String">
        delete from tb_schedu_algrule_config where id = #{id}
    </delete>

    <delete id="deleteScheduAlgruleConfigByIds" parameterType="String">
        delete from tb_schedu_algrule_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>