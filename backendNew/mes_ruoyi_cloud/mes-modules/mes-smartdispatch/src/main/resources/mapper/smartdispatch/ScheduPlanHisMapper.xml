<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduPlanHisMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduPlanHis" id="ScheduPlanHisResult">
        <result property="id"    column="id"    />
        <result property="planId"    column="plan_id"    />
        <result property="planCode"    column="plan_code"    />
        <result property="planName"    column="plan_name"    />
        <result property="scheduStatus"    column="schedu_status"    />
        <result property="executeStatus"    column="execute_status"    />
        <result property="planStartTime"    column="plan_start_time"    />
        <result property="planEndTime"    column="plan_end_time"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="status"    column="status"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="devcModel"    column="devc_model"    />
    </resultMap>

    <sql id="selectScheduPlanHisVo">
        select id, plan_id, plan_code, plan_name, schedu_status, execute_status, plan_start_time, plan_end_time, remark, create_time, create_by, update_time, update_by, status, tenant_id, devc_model from tb_schedu_plan_his
    </sql>

    <select id="selectScheduPlanHisList" parameterType="com.mes.smartdispath.domain.ScheduPlanHis" resultMap="ScheduPlanHisResult">
        <include refid="selectScheduPlanHisVo"/>
        <where>  
            <if test="planId != null  and planId != ''"> and plan_id = #{planId}</if>
            <if test="planCode != null  and planCode != ''"> and plan_code = #{planCode}</if>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="scheduStatus != null  and scheduStatus != ''"> and schedu_status = #{scheduStatus}</if>
            <if test="executeStatus != null  and executeStatus != ''"> and execute_status = #{executeStatus}</if>
            <if test="planStartTime != null  and planStartTime != ''"> and plan_start_time = #{planStartTime}</if>
            <if test="planEndTime != null  and planEndTime != ''"> and plan_end_time = #{planEndTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="devcModel != null  and devcModel != ''"> and devc_model = #{devcModel}</if>
        </where>
    </select>
    
    <select id="selectScheduPlanHisById" parameterType="String" resultMap="ScheduPlanHisResult">
        <include refid="selectScheduPlanHisVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduPlanHis" parameterType="com.mes.smartdispath.domain.ScheduPlanHis">
        insert into tb_schedu_plan_his
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="planCode != null and planCode != ''">plan_code,</if>
            <if test="planName != null and planName != ''">plan_name,</if>
            <if test="scheduStatus != null">schedu_status,</if>
            <if test="executeStatus != null">execute_status,</if>
            <if test="planStartTime != null and planStartTime != ''">plan_start_time,</if>
            <if test="planEndTime != null and planEndTime != ''">plan_end_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null and createTime != ''">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="status != null">status,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="devcModel != null">devc_model,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="planCode != null and planCode != ''">#{planCode},</if>
            <if test="planName != null and planName != ''">#{planName},</if>
            <if test="scheduStatus != null">#{scheduStatus},</if>
            <if test="executeStatus != null">#{executeStatus},</if>
            <if test="planStartTime != null and planStartTime != ''">#{planStartTime},</if>
            <if test="planEndTime != null and planEndTime != ''">#{planEndTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null and createTime != ''">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="status != null">#{status},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="devcModel != null">#{devcModel},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduPlanHis" parameterType="java.util.List">
        insert into tb_schedu_plan_his (
        id,
        plan_id,
        plan_code,
        plan_name,
        schedu_status,
        execute_status,
        plan_start_time,
        plan_end_time,
        remark,
        create_time,
        create_by,
        update_time,
        update_by,
        status,
        tenant_id,
        devc_model
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.planId},
            #{item.planCode},
            #{item.planName},
            #{item.scheduStatus},
            #{item.executeStatus},
            #{item.planStartTime},
            #{item.planEndTime},
            #{item.remark},
            #{item.createTime},
            #{item.createBy},
            #{item.updateTime},
            #{item.updateBy},
            #{item.status},
            #{item.tenantId},
            #{item.devcModel}
            )
        </foreach>
    </insert>

    <update id="updateScheduPlanHis" parameterType="com.mes.smartdispath.domain.ScheduPlanHis">
        update tb_schedu_plan_his
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="planCode != null and planCode != ''">plan_code = #{planCode},</if>
            <if test="planName != null and planName != ''">plan_name = #{planName},</if>
            <if test="scheduStatus != null">schedu_status = #{scheduStatus},</if>
            <if test="executeStatus != null">execute_status = #{executeStatus},</if>
            <if test="planStartTime != null and planStartTime != ''">plan_start_time = #{planStartTime},</if>
            <if test="planEndTime != null and planEndTime != ''">plan_end_time = #{planEndTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="devcModel != null">devc_model = #{devcModel},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateScheduPlanHisByPlanId" parameterType="com.mes.smartdispath.domain.ScheduPlanHis">
        update tb_schedu_plan_his
        <trim prefix="SET" suffixOverrides=",">
            <if test="planCode != null and planCode != ''">plan_code = #{planCode},</if>
            <if test="planName != null and planName != ''">plan_name = #{planName},</if>
            <if test="scheduStatus != null">schedu_status = #{scheduStatus},</if>
            <if test="executeStatus != null">execute_status = #{executeStatus},</if>
            <if test="planStartTime != null and planStartTime != ''">plan_start_time = #{planStartTime},</if>
            <if test="planEndTime != null and planEndTime != ''">plan_end_time = #{planEndTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="devcModel != null">devc_model = #{devcModel},</if>
            update_time = now(),
        </trim>
        where plan_id = #{planId}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduPlanHis" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_plan_his
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.planId != null">
                    plan_id = #{item.planId},
                </if>
                <if test="item.planCode != null and item.planCode != ''">
                    plan_code = #{item.planCode},
                </if>
                <if test="item.planName != null and item.planName != ''">
                    plan_name = #{item.planName},
                </if>
                <if test="item.scheduStatus != null">
                    schedu_status = #{item.scheduStatus},
                </if>
                <if test="item.executeStatus != null">
                    execute_status = #{item.executeStatus},
                </if>
                <if test="item.planStartTime != null and item.planStartTime != ''">
                    plan_start_time = #{item.planStartTime},
                </if>
                <if test="item.planEndTime != null and item.planEndTime != ''">
                    plan_end_time = #{item.planEndTime},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark},
                </if>
                <if test="item.createTime != null and item.createTime != ''">
                    create_time = #{item.createTime},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.tenantId != null">
                    tenant_id = #{item.tenantId},
                </if>
                <if test="item.devcModel != null">
                    devc_model = #{item.devcModel},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduPlanHisById" parameterType="String">
        delete from tb_schedu_plan_his where id = #{id}
    </delete>

    <delete id="deleteScheduPlanHisByIds" parameterType="String">
        delete from tb_schedu_plan_his where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>