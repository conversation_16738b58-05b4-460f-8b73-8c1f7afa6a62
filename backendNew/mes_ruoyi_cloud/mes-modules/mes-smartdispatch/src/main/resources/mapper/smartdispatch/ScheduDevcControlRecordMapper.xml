<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduDevcControlRecordMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduDevcControlRecord" id="ScheduDevcControlRecordResult">
        <result property="id"    column="id"    />
        <result property="devcCode"    column="devc_code"    />
        <result property="devcName"    column="devc_name"    />
        <result property="controlCommand"    column="control_command"    />
        <result property="status"    column="status"    />
        <result property="sendTime"    column="send_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <sql id="selectScheduDevcControlRecordVo">
        select id, devc_code, devc_name, control_command, status, send_time, create_time, tenant_id from tb_schedu_devc_control_record
    </sql>

    <select id="selectScheduDevcControlRecordList" parameterType="com.mes.smartdispath.domain.dto.DevcControlRecordQueryDTO" resultMap="ScheduDevcControlRecordResult">
        <include refid="selectScheduDevcControlRecordVo"/>
        <where>  
            <if test="devcCode != null  and devcCode != ''"> and devc_code = #{devcCode}</if>
            <if test="devcName != null  and devcName != ''"> and devc_name like concat('%', #{devcName}, '%')</if>
            <if test="controlCommand != null  and controlCommand != ''"> and control_command = #{controlCommand}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="sendTime != null  and sendTime != ''"> and send_time = #{sendTime}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="startTime != null  and startTime != ''"> and send_time >= #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and send_time <![CDATA[<=]]> #{endTime}</if>
        </where>
    </select>
    
    <select id="selectScheduDevcControlRecordById" parameterType="String" resultMap="ScheduDevcControlRecordResult">
        <include refid="selectScheduDevcControlRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduDevcControlRecord" parameterType="com.mes.smartdispath.domain.ScheduDevcControlRecord">
        insert into tb_schedu_devc_control_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="devcCode != null">devc_code,</if>
            <if test="devcName != null">devc_name,</if>
            <if test="controlCommand != null">control_command,</if>
            <if test="status != null">status,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="tenantId != null">tenant_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="devcCode != null">#{devcCode},</if>
            <if test="devcName != null">#{devcName},</if>
            <if test="controlCommand != null">#{controlCommand},</if>
            <if test="status != null">#{status},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="tenantId != null">#{tenantId},</if>
         </trim>
    </insert>

    <update id="updateScheduDevcControlRecord" parameterType="com.mes.smartdispath.domain.ScheduDevcControlRecord">
        update tb_schedu_devc_control_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="devcCode != null">devc_code = #{devcCode},</if>
            <if test="devcName != null">devc_name = #{devcName},</if>
            <if test="controlCommand != null">control_command = #{controlCommand},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScheduDevcControlRecordById" parameterType="String">
        delete from tb_schedu_devc_control_record where id = #{id}
    </delete>

    <delete id="deleteScheduDevcControlRecordByIds" parameterType="String">
        delete from tb_schedu_devc_control_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>