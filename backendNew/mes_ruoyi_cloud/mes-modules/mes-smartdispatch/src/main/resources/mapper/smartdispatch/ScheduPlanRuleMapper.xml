<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduPlanRuleMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduPlanRule" id="ScheduPlanRuleResult">
        <result property="id"    column="id"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="businessType"    column="business_type"    />
        <result property="siteType"    column="site_type"    />
        <result property="siteName"    column="site_name"    />
        <result property="siteId"    column="site_id"    />
        <result property="provinceName"    column="province_name"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="cityName"    column="city_name"    />
        <result property="cityCode"    column="city_code"    />
        <result property="activityType"    column="activity_type"    />
        <result property="activitySubtype"    column="activity_subtype"    />
        <result property="isAssoplan"    column="is_assoplan"    />
        <result property="planCronRule"    column="plan_cron_rule"    />
        <result property="planCycle"    column="plan_cycle"    />
        <result property="ruleStartTime"    column="rule_start_time"    />
        <result property="ruleEndTime"    column="rule_end_time"    />
        <result property="ruleStatus"    column="rule_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="approvalStatus"    column="approval_status"    />
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.ScheduPlanRuleVO" id="PlanRuleVOResult" extends="ScheduPlanRuleResult">
        <result property="siteTypeName"    column="site_type_name"    />
        <result property="hasAutomaticStation"    column="has_automatic_station"    />
        <result property="businessTypeName"    column="business_type_name"    />
        <result property="activityTypeName"    column="activity_type_name"    />
        <result property="activitySubtypeName"    column="activity_subtype_name"    />
        <result property="approvalOpinion"    column="approval_opinion"    />
        <result property="approver"    column="approver"    />
        <result property="approvalTime"    column="approval_time"    />
    </resultMap>

    <sql id="selectScheduPlanRuleVo">
        select id, rule_code, rule_name, business_type, site_type, site_name, site_id, province_name, province_code, city_name, city_code, activity_type, activity_subtype, is_assoplan, plan_cron_rule, plan_cycle, rule_start_time, rule_end_time, rule_status, create_by, create_time, update_by, update_time, status, tenant_id, approval_status from tb_schedu_plan_rule
    </sql>

    <select id="selectScheduPlanRuleList" parameterType="com.mes.smartdispath.domain.dto.ScheduPlanRuleQueryDTO" resultMap="PlanRuleVOResult">
        select t1.id, t1.rule_code, t1.rule_name, t1.business_type, t1.site_type, t1.site_name, t1.site_id,
            t1.province_name, t1.province_code, t1.city_name, t1.city_code, t1.activity_type, t1.activity_subtype,
            t1.is_assoplan, t1.plan_cron_rule, t1.plan_cycle, t1.rule_start_time, t1.rule_end_time,
            t1.rule_status, t1.create_by, t1.create_time, t1.update_by, t1.update_time, t1.status, t1.tenant_id, t1.approval_status,
            (SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{siteTypeDictClassCode} AND b.state = 'A' AND b.dict_code = t1.site_type AND b.parent_dict_code = t1.business_type limit 1) AS site_type_name,
            (SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{businessTypeDictClassCode} AND b.state = 'A' AND b.dict_code = t1.business_type) AS business_type_name,
            (SELECT b.activity_type_name FROM tb_schedu_monitor_activity_info b WHERE b.activity_type_code = t1.activity_type LIMIT 1) AS activity_type_name,
            (SELECT b.activity_subtype_name FROM tb_schedu_monitor_activity_info b WHERE b.activity_subtype_code = t1.activity_subtype LIMIT 1) AS activity_subtype_name,
            t2.has_automatic_station
        from tb_schedu_plan_rule t1
        left join tb_res_site t2 on t1.site_id = t2.id
        where
            status = 'A'
            <if test="ruleCode != null  and ruleCode != ''"> and t1.rule_code like concat('%', #{ruleCode}, '%')</if>
            <if test="ruleName != null  and ruleName != ''"> and t1.rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="businessType != null  and businessType != ''"> and t1.business_type = #{businessType}</if>
            <if test="siteType != null  and siteType != ''"> and t1.site_type = #{siteType}</if>
            <if test="siteName != null  and siteName != ''"> and t1.site_name like concat('%', #{siteName}, '%')</if>
            <if test="siteId != null  and siteId != '' and null == siteIdArr"> and t1.site_id = #{siteId}</if>
            <if test="siteId != siteIdArr  and siteIdArr != ''">
                and t1.site_id in
                    <foreach collection="siteIdArr" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="provinceName != null  and provinceName != ''"> and t1.province_name like concat('%', #{provinceName}, '%')</if>
            <if test="provinceCode != null  and provinceCode != ''"> and t1.province_code = #{provinceCode}</if>
            <if test="cityName != null  and cityName != ''"> and t1.city_name like concat('%', #{cityName}, '%')</if>
            <if test="cityCode != null  and cityCode != '' and null == cityCodeArr"> and t1.city_code = #{cityCode}</if>
            <if test="cityCodeArr != null  and cityCodeArr != ''">
                and t1.city_code in
                    <foreach collection="cityCodeArr" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="activityType != null  and activityType != ''"> and t1.activity_type = #{activityType}</if>
            <if test="activitySubtype != null  and activitySubtype != ''"> and t1.activity_subtype = #{activitySubtype}</if>
            <if test="isAssoplan != null  and isAssoplan != ''"> and t1.is_assoplan = #{isAssoplan}</if>
            <if test="planCronRule != null  and planCronRule != ''"> and t1.plan_cron_rule = #{planCronRule}</if>
            <if test="planCycle != null  and planCycle != ''"> and t1.plan_cycle = #{planCycle}</if>
            <if test="ruleStartTime != null  and ruleStartTime != ''"> and t1.rule_start_time = #{ruleStartTime}</if>
            <if test="ruleEndTime != null  and ruleEndTime != ''"> and t1.rule_end_time = #{ruleEndTime}</if>
            <if test="ruleStatus != null  and ruleStatus != ''"> and t1.rule_status = #{ruleStatus}</if>
            <if test="status != null  and status != ''"> and t1.status = #{status}</if>
            <if test="tenantId != null  and tenantId != ''"> and t1.tenant_id = #{tenantId}</if>
            <if test="approvalStatus != null  and approvalStatus != '' and null == approvalStatusArr"> and t1.approval_status = #{approvalStatus}</if>
            <if test="approvalStatusArr != null  and approvalStatusArr != ''">
                and t1.approval_status in
                    <foreach collection="approvalStatusArr" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="hasAutomaticStation != null  and hasAutomaticStation != ''"> and t2.has_automatic_station = #{hasAutomaticStation}</if>
        order by t1.create_time desc
    </select>
    
    <select id="selectScheduPlanRuleById" parameterType="String" resultMap="ScheduPlanRuleResult">
        <include refid="selectScheduPlanRuleVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduPlanRule" parameterType="com.mes.smartdispath.domain.ScheduPlanRule" useGeneratedKeys="true" keyProperty="id">
        insert into tb_schedu_plan_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">rule_code,</if>
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="businessType != null">business_type,</if>
            <if test="siteType != null">site_type,</if>
            <if test="siteName != null">site_name,</if>
            <if test="siteId != null">site_id,</if>
            <if test="provinceName != null">province_name,</if>
            <if test="provinceCode != null">province_code,</if>
            <if test="cityName != null">city_name,</if>
            <if test="cityCode != null">city_code,</if>
            <if test="activityType != null">activity_type,</if>
            <if test="activitySubtype != null">activity_subtype,</if>
            <if test="isAssoplan != null and isAssoplan != ''">is_assoplan,</if>
            <if test="planCronRule != null">plan_cron_rule,</if>
            <if test="planCycle != null">plan_cycle,</if>
            <if test="ruleStartTime != null">rule_start_time,</if>
            <if test="ruleEndTime != null">rule_end_time,</if>
            <if test="ruleStatus != null and ruleStatus != ''">rule_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="approvalStatus != null">approval_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">#{ruleCode},</if>
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="siteType != null">#{siteType},</if>
            <if test="siteName != null">#{siteName},</if>
            <if test="siteId != null">#{siteId},</if>
            <if test="provinceName != null">#{provinceName},</if>
            <if test="provinceCode != null">#{provinceCode},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="cityCode != null">#{cityCode},</if>
            <if test="activityType != null">#{activityType},</if>
            <if test="activitySubtype != null">#{activitySubtype},</if>
            <if test="isAssoplan != null and isAssoplan != ''">#{isAssoplan},</if>
            <if test="planCronRule != null">#{planCronRule},</if>
            <if test="planCycle != null">#{planCycle},</if>
            <if test="ruleStartTime != null">#{ruleStartTime},</if>
            <if test="ruleEndTime != null">#{ruleEndTime},</if>
            <if test="ruleStatus != null and ruleStatus != ''">#{ruleStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
         </trim>
    </insert>

    <update id="updateScheduPlanRule" parameterType="com.mes.smartdispath.domain.ScheduPlanRule">
        update tb_schedu_plan_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">rule_code = #{ruleCode},</if>
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="siteType != null">site_type = #{siteType},</if>
            <if test="siteName != null">site_name = #{siteName},</if>
            <if test="siteId != null">site_id = #{siteId},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="activitySubtype != null">activity_subtype = #{activitySubtype},</if>
            <if test="isAssoplan != null and isAssoplan != ''">is_assoplan = #{isAssoplan},</if>
            <if test="planCronRule != null">plan_cron_rule = #{planCronRule},</if>
            <if test="planCycle != null">plan_cycle = #{planCycle},</if>
            <if test="ruleStartTime != null">rule_start_time = #{ruleStartTime},</if>
            <if test="ruleEndTime != null">rule_end_time = #{ruleEndTime},</if>
            <if test="ruleStatus != null and ruleStatus != ''">rule_status = #{ruleStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

    <update id="updateScheduPlanRuleByIdArr" parameterType="com.mes.smartdispath.domain.dto.ScheduPlanRuleQueryDTO">
        update tb_schedu_plan_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">rule_code = #{ruleCode},</if>
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="siteType != null">site_type = #{siteType},</if>
            <if test="siteName != null">site_name = #{siteName},</if>
            <if test="siteId != null">site_id = #{siteId},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="provinceCode != null">province_code = #{provinceCode},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="cityCode != null">city_code = #{cityCode},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="activitySubtype != null">activity_subtype = #{activitySubtype},</if>
            <if test="isAssoplan != null and isAssoplan != ''">is_assoplan = #{isAssoplan},</if>
            <if test="planCronRule != null">plan_cron_rule = #{planCronRule},</if>
            <if test="planCycle != null">plan_cycle = #{planCycle},</if>
            <if test="ruleStartTime != null">rule_start_time = #{ruleStartTime},</if>
            <if test="ruleEndTime != null">rule_end_time = #{ruleEndTime},</if>
            <if test="ruleStatus != null and ruleStatus != ''">rule_status = #{ruleStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            update_time = now(),
        </trim>
        where id in
        <foreach item="item" collection="idArr" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <delete id="deleteScheduPlanRuleById" parameterType="String">
        delete from tb_schedu_plan_rule where id = #{id}
    </delete>

    <delete id="deleteScheduPlanRuleByIds" parameterType="String">
        delete from tb_schedu_plan_rule where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>