<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduTaskExtendHisMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduTaskExtendHis" id="ScheduTaskExtendHisResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="executorId"    column="executor_id"    />
        <result property="executorName"    column="executor_name"    />
        <result property="phone"    column="phone"    />
        <result property="maintainUnitCode"    column="maintain_unit_code"    />
        <result property="maintainUnitName"    column="maintain_unit_name"    />
        <result property="laboratoryAddress"    column="laboratory_address"    />
        <result property="laboratoryName"    column="laboratory_name"    />
        <result property="laboratoryCode"    column="laboratory_code"    />
        <result property="deliveryAddress"    column="delivery_address"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <sql id="selectScheduTaskExtendHisVo">
        select id, task_id, task_code, task_name, executor_id, executor_name, phone, maintain_unit_code, maintain_unit_name, laboratory_address, laboratory_name, laboratory_code, delivery_address, create_time, create_by, update_time, update_by, is_deleted from tb_schedu_task_extend_his
    </sql>

    <select id="selectScheduTaskExtendHisList" parameterType="com.mes.smartdispath.domain.ScheduTaskExtendHis" resultMap="ScheduTaskExtendHisResult">
        <include refid="selectScheduTaskExtendHisVo"/>
        <where>  
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="executorId != null  and executorId != ''"> and executor_id = #{executorId}</if>
            <if test="executorName != null  and executorName != ''"> and executor_name like concat('%', #{executorName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="maintainUnitCode != null  and maintainUnitCode != ''"> and maintain_unit_code = #{maintainUnitCode}</if>
            <if test="maintainUnitName != null  and maintainUnitName != ''"> and maintain_unit_name like concat('%', #{maintainUnitName}, '%')</if>
            <if test="laboratoryAddress != null  and laboratoryAddress != ''"> and laboratory_address = #{laboratoryAddress}</if>
            <if test="laboratoryName != null  and laboratoryName != ''"> and laboratory_name like concat('%', #{laboratoryName}, '%')</if>
            <if test="laboratoryCode != null  and laboratoryCode != ''"> and laboratory_code = #{laboratoryCode}</if>
            <if test="deliveryAddress != null  and deliveryAddress != ''"> and delivery_address = #{deliveryAddress}</if>
            <if test="isDeleted != null  and isDeleted != ''"> and is_deleted = #{isDeleted}</if>
        </where>
    </select>
    
    <select id="selectScheduTaskExtendHisById" parameterType="String" resultMap="ScheduTaskExtendHisResult">
        <include refid="selectScheduTaskExtendHisVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduTaskExtendHis" parameterType="com.mes.smartdispath.domain.ScheduTaskExtendHis">
        insert into tb_schedu_task_extend_his
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="executorId != null">executor_id,</if>
            <if test="executorName != null">executor_name,</if>
            <if test="phone != null">phone,</if>
            <if test="maintainUnitCode != null">maintain_unit_code,</if>
            <if test="maintainUnitName != null">maintain_unit_name,</if>
            <if test="laboratoryAddress != null">laboratory_address,</if>
            <if test="laboratoryName != null">laboratory_name,</if>
            <if test="laboratoryCode != null">laboratory_code,</if>
            <if test="deliveryAddress != null">delivery_address,</if>
            <if test="createTime != null and createTime != ''">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDeleted != null">is_deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="executorId != null">#{executorId},</if>
            <if test="executorName != null">#{executorName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="maintainUnitCode != null">#{maintainUnitCode},</if>
            <if test="maintainUnitName != null">#{maintainUnitName},</if>
            <if test="laboratoryAddress != null">#{laboratoryAddress},</if>
            <if test="laboratoryName != null">#{laboratoryName},</if>
            <if test="laboratoryCode != null">#{laboratoryCode},</if>
            <if test="deliveryAddress != null">#{deliveryAddress},</if>
            <if test="createTime != null and createTime != ''">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduTaskExtendHis" parameterType="java.util.List">
        insert into tb_schedu_task_extend_his (
        id,
        task_id,
        task_code,
        task_name,
        executor_id,
        executor_name,
        phone,
        maintain_unit_code,
        maintain_unit_name,
        laboratory_address,
        laboratory_name,
        laboratory_code,
        delivery_address,
        create_time,
        create_by,
        update_time,
        update_by,
        is_deleted
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.taskId},
            #{item.taskCode},
            #{item.taskName},
            #{item.executorId},
            #{item.executorName},
            #{item.phone},
            #{item.maintainUnitCode},
            #{item.maintainUnitName},
            #{item.laboratoryAddress},
            #{item.laboratoryName},
            #{item.laboratoryCode},
            #{item.deliveryAddress},
            #{item.createTime},
            #{item.createBy},
            #{item.updateTime},
            #{item.updateBy},
            #{item.isDeleted}
            )
        </foreach>
    </insert>

    <update id="updateScheduTaskExtendHis" parameterType="com.mes.smartdispath.domain.ScheduTaskExtendHis">
        update tb_schedu_task_extend_his
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="executorId != null">executor_id = #{executorId},</if>
            <if test="executorName != null">executor_name = #{executorName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="maintainUnitCode != null">maintain_unit_code = #{maintainUnitCode},</if>
            <if test="maintainUnitName != null">maintain_unit_name = #{maintainUnitName},</if>
            <if test="laboratoryAddress != null">laboratory_address = #{laboratoryAddress},</if>
            <if test="laboratoryName != null">laboratory_name = #{laboratoryName},</if>
            <if test="laboratoryCode != null">laboratory_code = #{laboratoryCode},</if>
            <if test="deliveryAddress != null">delivery_address = #{deliveryAddress},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduTaskExtendHis" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_task_extend_his
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.taskId != null and item.taskId != ''">
                    task_id = #{item.taskId},
                </if>
                <if test="item.taskCode != null and item.taskCode != ''">
                    task_code = #{item.taskCode},
                </if>
                <if test="item.taskName != null and item.taskName != ''">
                    task_name = #{item.taskName},
                </if>
                <if test="item.executorId != null">
                    executor_id = #{item.executorId},
                </if>
                <if test="item.executorName != null">
                    executor_name = #{item.executorName},
                </if>
                <if test="item.phone != null">
                    phone = #{item.phone},
                </if>
                <if test="item.maintainUnitCode != null">
                    maintain_unit_code = #{item.maintainUnitCode},
                </if>
                <if test="item.maintainUnitName != null">
                    maintain_unit_name = #{item.maintainUnitName},
                </if>
                <if test="item.laboratoryAddress != null">
                    laboratory_address = #{item.laboratoryAddress},
                </if>
                <if test="item.laboratoryName != null">
                    laboratory_name = #{item.laboratoryName},
                </if>
                <if test="item.laboratoryCode != null">
                    laboratory_code = #{item.laboratoryCode},
                </if>
                <if test="item.deliveryAddress != null">
                    delivery_address = #{item.deliveryAddress},
                </if>
                <if test="item.createTime != null and item.createTime != ''">
                    create_time = #{item.createTime},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduTaskExtendHisById" parameterType="String">
        delete from tb_schedu_task_extend_his where id = #{id}
    </delete>

    <delete id="deleteScheduTaskExtendHisByIds" parameterType="String">
        delete from tb_schedu_task_extend_his where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>