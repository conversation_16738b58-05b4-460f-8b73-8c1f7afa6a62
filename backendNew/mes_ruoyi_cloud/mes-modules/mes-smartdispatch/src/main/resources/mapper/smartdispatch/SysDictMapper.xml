<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.SysDictMapper">

    <resultMap type="com.mes.smartdispath.domain.SysDict" id="SysDictResult">
            <result property="id" column="id"/>
            <result property="classCode" column="class_code"/>
            <result property="dictCode" column="dict_code"/>
            <result property="dictValue" column="dict_value"/>
            <result property="sortIndex" column="sort_index"/>
            <result property="createDate" column="create_date"/>
            <result property="state" column="state"/>
            <result property="stateDate" column="state_date"/>
            <result property="modifeDate" column="modife_date"/>
            <result property="cssClass" column="css_class"/>
            <result property="listClass" column="list_class"/>
            <result property="isDefault" column="is_default"/>
            <result property="remark" column="remark"/>
            <result property="parentClassCode" column="parent_class_code"/>
            <result property="parentDictCode" column="parent_dict_code"/>
    </resultMap>

    <sql id="selectSysDictVo">
        select id, class_code, dict_code, dict_value, sort_index, create_date, state, state_date, modife_date, css_class, list_class, is_default, remark, parent_class_code, parent_dict_code
        from tb_sys_dict
    </sql>

    <select id="selectSysDictList" parameterType="com.mes.smartdispath.domain.SysDict"
            resultMap="SysDictResult">
        <include refid="selectSysDictVo"/>
        where
            state = 'A'
                        <if test="classCode != null  and classCode != ''">
                            and class_code = #{classCode}
                        </if>
                        <if test="dictCode != null  and dictCode != ''">
                            and dict_code = #{dictCode}
                        </if>
                        <if test="dictValue != null  and dictValue != ''">
                            and dict_value = #{dictValue}
                        </if>
                        <if test="sortIndex != null  and sortIndex != ''">
                            and sort_index = #{sortIndex}
                        </if>
                        <if test="createDate != null  and createDate != ''">
                            and create_date = #{createDate}
                        </if>
                        <if test="stateDate != null  and stateDate != ''">
                            and state_date = #{stateDate}
                        </if>
                        <if test="modifeDate != null  and modifeDate != ''">
                            and modife_date = #{modifeDate}
                        </if>
                        <if test="cssClass != null  and cssClass != ''">
                            and css_class = #{cssClass}
                        </if>
                        <if test="listClass != null  and listClass != ''">
                            and list_class = #{listClass}
                        </if>
                        <if test="isDefault != null  and isDefault != ''">
                            and is_default = #{isDefault}
                        </if>
                        <if test="parentClassCode != null  and parentClassCode != ''">
                            and parent_class_code = #{parentClassCode}
                        </if>
                        <if test="parentDictCode != null  and parentDictCode != ''">
                            and parent_dict_code = #{parentDictCode}
                        </if>
    </select>

    <select id="selectSysDictById" parameterType="String"
            resultMap="SysDictResult">
            <include refid="selectSysDictVo"/>
            where id = #{id}
    </select>

    <insert id="insertSysDict" parameterType="com.mes.smartdispath.domain.SysDict">
        insert into tb_sys_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,
                    </if>
                    <if test="classCode != null">class_code,
                    </if>
                    <if test="dictCode != null">dict_code,
                    </if>
                    <if test="dictValue != null">dict_value,
                    </if>
                    <if test="sortIndex != null">sort_index,
                    </if>
                    <if test="createDate != null">create_date,
                    </if>
                    <if test="state != null">state,
                    </if>
                    <if test="stateDate != null">state_date,
                    </if>
                    <if test="modifeDate != null">modife_date,
                    </if>
                    <if test="cssClass != null">css_class,
                    </if>
                    <if test="listClass != null">list_class,
                    </if>
                    <if test="isDefault != null">is_default,
                    </if>
                    <if test="remark != null">remark,
                    </if>
                    <if test="parentClassCode != null">parent_class_code,
                    </if>
                    <if test="parentDictCode != null">parent_dict_code,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="classCode != null">#{classCode},
                    </if>
                    <if test="dictCode != null">#{dictCode},
                    </if>
                    <if test="dictValue != null">#{dictValue},
                    </if>
                    <if test="sortIndex != null">#{sortIndex},
                    </if>
                    <if test="createDate != null">#{createDate},
                    </if>
                    <if test="state != null">#{state},
                    </if>
                    <if test="stateDate != null">#{stateDate},
                    </if>
                    <if test="modifeDate != null">#{modifeDate},
                    </if>
                    <if test="cssClass != null">#{cssClass},
                    </if>
                    <if test="listClass != null">#{listClass},
                    </if>
                    <if test="isDefault != null">#{isDefault},
                    </if>
                    <if test="remark != null">#{remark},
                    </if>
                    <if test="parentClassCode != null">#{parentClassCode},
                    </if>
                    <if test="parentDictCode != null">#{parentDictCode},
                    </if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertSysDict" parameterType="java.util.List">
        insert into tb_sys_dict (
                id,
                class_code,
                dict_code,
                dict_value,
                sort_index,
                create_date,
                state,
                state_date,
                modife_date,
                css_class,
                list_class,
                is_default,
                remark,
                parent_class_code,
                parent_dict_code
        ) values
        <foreach collection="list" item="item" separator=",">
        (
                #{item.id},
                #{item.classCode},
                #{item.dictCode},
                #{item.dictValue},
                #{item.sortIndex},
                #{item.createDate},
                #{item.state},
                #{item.stateDate},
                #{item.modifeDate},
                #{item.cssClass},
                #{item.listClass},
                #{item.isDefault},
                #{item.remark},
                #{item.parentClassCode},
                #{item.parentDictCode}
        )
        </foreach>
    </insert>

    <update id="updateSysDict" parameterType="com.mes.smartdispath.domain.SysDict">
        update tb_sys_dict
        <trim prefix="SET" suffixOverrides=",">
                    <if test="classCode != null">class_code =
                        #{classCode},
                    </if>
                    <if test="dictCode != null">dict_code =
                        #{dictCode},
                    </if>
                    <if test="dictValue != null">dict_value =
                        #{dictValue},
                    </if>
                    <if test="sortIndex != null">sort_index =
                        #{sortIndex},
                    </if>
                    <if test="createDate != null">create_date =
                        #{createDate},
                    </if>
                    <if test="state != null">state =
                        #{state},
                    </if>
                    <if test="stateDate != null">state_date =
                        #{stateDate},
                    </if>
                    <if test="modifeDate != null">modife_date =
                        #{modifeDate},
                    </if>
                    <if test="cssClass != null">css_class =
                        #{cssClass},
                    </if>
                    <if test="listClass != null">list_class =
                        #{listClass},
                    </if>
                    <if test="isDefault != null">is_default =
                        #{isDefault},
                    </if>
                    <if test="remark != null">remark =
                        #{remark},
                    </if>
                    <if test="parentClassCode != null">parent_class_code =
                        #{parentClassCode},
                    </if>
                    <if test="parentDictCode != null">parent_dict_code =
                        #{parentDictCode},
                    </if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateSysDict" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_sys_dict
            <trim prefix="SET" suffixOverrides=",">
                        <if test="item.classCode != null">
                            class_code = #{item.classCode},
                        </if>
                        <if test="item.dictCode != null">
                            dict_code = #{item.dictCode},
                        </if>
                        <if test="item.dictValue != null">
                            dict_value = #{item.dictValue},
                        </if>
                        <if test="item.sortIndex != null">
                            sort_index = #{item.sortIndex},
                        </if>
                        <if test="item.createDate != null">
                            create_date = #{item.createDate},
                        </if>
                        <if test="item.state != null">
                            state = #{item.state},
                        </if>
                        <if test="item.stateDate != null">
                            state_date = #{item.stateDate},
                        </if>
                        <if test="item.modifeDate != null">
                            modife_date = #{item.modifeDate},
                        </if>
                        <if test="item.cssClass != null">
                            css_class = #{item.cssClass},
                        </if>
                        <if test="item.listClass != null">
                            list_class = #{item.listClass},
                        </if>
                        <if test="item.isDefault != null">
                            is_default = #{item.isDefault},
                        </if>
                        <if test="item.remark != null">
                            remark = #{item.remark},
                        </if>
                        <if test="item.parentClassCode != null">
                            parent_class_code = #{item.parentClassCode},
                        </if>
                        <if test="item.parentDictCode != null">
                            parent_dict_code = #{item.parentDictCode},
                        </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteSysDictById" parameterType="String">
        delete
        from tb_sys_dict where id = #{id}
    </delete>

    <delete id="deleteSysDictByIds" parameterType="String">
        delete from tb_sys_dict where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>