<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduTargetFileMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduTargetFile" id="ScheduTargetFileResult">
        <result property="id"    column="id"    />
        <result property="businessType"    column="business_type"    />
        <result property="statYear"    column="stat_year"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="fileName"    column="file_name"    />
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.ScheduTargetFileVO" id="ScheduTargetFileVOResult" extends="ScheduTargetFileResult">
        <result property="businessTypeName"    column="business_type_name"    />
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.dto.ScheduTargetFileDTO" id="TargetFileWithAttachInfoResult" extends="ScheduTargetFileResult">
        <result property="attachId"    column="attach_id"    />
        <result property="attachName"    column="attach_name"    />
        <result property="attachType"    column="attach_type"    />
        <result property="attachPath"    column="attach_path"    />
        <result property="attachSize"    column="attach_size"    />
    </resultMap>

    <sql id="selectScheduTargetFileVo">
        select id, business_type, stat_year, create_by, create_time, update_by, update_time, status, tenant_id, file_name from tb_schedu_target_file
    </sql>

    <select id="selectTargetFileWithAttachInfoById" parameterType="String" resultMap="TargetFileWithAttachInfoResult">
        SELECT
            t1.*,
            t3.attach_id,
            t3.attach_name,
            t3.attach_type,
            t3.attach_path,
            t3.attach_size
        FROM tb_schedu_target_file t1
            left join tb_sys_attach_info_rel t2 on t1.id = t2.business_id and t2.tb_mark = #{attachSource}
            left join tb_sys_attach_info t3 on t2.attach_id = t3.attach_id
        where t1.status = 'A'
            and t1.id = #{id}
    </select>

    <select id="selectScheduTargetFileList" parameterType="com.mes.smartdispath.domain.dto.ScheduTargetFileQueryDTO" resultMap="ScheduTargetFileVOResult">
        select
            id, business_type, stat_year, create_by, create_time,
            update_by, update_time, status, tenant_id, file_name,
            (SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{businessTypeDictClassCode} AND b.state = 'A' AND b.dict_code = business_type) AS business_type_name
        from tb_schedu_target_file
        where
            status = 'A'
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="statYear != null  and statYear != ''"> and stat_year = #{statYear}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="startTime != null  and startTime != ''"> and create_time >= #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and create_time <![CDATA[<= ]]> #{endTime}</if>
    </select>

    <select id="selectScheduTargetFileById" parameterType="String" resultMap="ScheduTargetFileResult">
        <include refid="selectScheduTargetFileVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduTargetFile" parameterType="com.mes.smartdispath.domain.ScheduTargetFile" useGeneratedKeys="true" keyProperty="id">
        insert into tb_schedu_target_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="statYear != null and statYear != ''">stat_year,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="fileName != null">file_name,</if>
            status,
            create_time,
            update_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="statYear != null and statYear != ''">#{statYear},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="fileName != null">#{fileName},</if>
            'A',
            now(),
            now()
         </trim>
    </insert>

    <update id="updateScheduTargetFile" parameterType="com.mes.smartdispath.domain.ScheduTargetFile">
        update tb_schedu_target_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="statYear != null and statYear != ''">stat_year = #{statYear},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = now(),</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScheduTargetFileById" parameterType="String">
        delete from tb_schedu_target_file where id = #{id}
    </delete>

    <delete id="deleteScheduTargetFileByIds" parameterType="String">
        delete from tb_schedu_target_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>