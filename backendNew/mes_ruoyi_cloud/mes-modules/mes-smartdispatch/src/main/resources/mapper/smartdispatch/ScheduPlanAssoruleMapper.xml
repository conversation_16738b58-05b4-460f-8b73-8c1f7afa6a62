<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduPlanAssoruleMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduPlanAssorule" id="ScheduPlanAssoruleResult">
        <result property="id"    column="id"    />
        <result property="ruleCode"    column="rule_code"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="mainRuleId"    column="main_rule_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="siteType"    column="site_type"    />
        <result property="activityType"    column="activity_type"    />
        <result property="activitySubtype"    column="activity_subtype"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
    </resultMap>

    <resultMap id="ScheduPlanAssoruleVOResult" type="com.mes.smartdispath.domain.vo.ScheduPlanAssoruleVO" extends="ScheduPlanAssoruleResult">
        <result property="businessTypeName"    column="business_type_name"    />
        <result property="activityTypeName"    column="activity_type_name"    />
        <result property="activitySubtypeName"    column="activity_subtype_name"    />
    </resultMap>

    <sql id="selectScheduPlanAssoruleVo">
        select id, rule_code, rule_name, main_rule_id, business_type, site_type, activity_type, activity_subtype, create_time, update_time, status from tb_schedu_plan_assorule
    </sql>

    <select id="selectScheduPlanAssoruleList" parameterType="com.mes.smartdispath.domain.ScheduPlanAssorule" resultMap="ScheduPlanAssoruleResult">
        <include refid="selectScheduPlanAssoruleVo"/>
        <where>  
            <if test="ruleCode != null  and ruleCode != ''"> and rule_code = #{ruleCode}</if>
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="mainRuleId != null  and mainRuleId != ''"> and main_rule_id = #{mainRuleId}</if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="siteType != null  and siteType != ''"> and site_type = #{siteType}</if>
            <if test="activityType != null  and activityType != ''"> and activity_type = #{activityType}</if>
            <if test="activitySubtype != null  and activitySubtype != ''"> and activity_subtype = #{activitySubtype}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectScheduPlanAssoruleListByMainRuleId" parameterType="String" resultMap="ScheduPlanAssoruleVOResult">
        SELECT a.*,
               ( SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{businessTypeDictClassCode} AND b.state = 'A' AND b.dict_code = a.business_type) AS business_type_name,
               ( SELECT b.activity_type_name FROM tb_schedu_monitor_activity_info b WHERE b.activity_type_code = a.activity_type LIMIT 1) AS activity_type_name,
               ( SELECT b.activity_subtype_name FROM tb_schedu_monitor_activity_info b WHERE b.activity_subtype_code = a.activity_subtype LIMIT 1)AS activity_subtype_name
            FROM tb_schedu_plan_assorule a
            WHERE a.status = 'A'
            and a.main_rule_id = #{mainRuleId}
    </select>

    <select id="selectScheduPlanAssoruleById" parameterType="String" resultMap="ScheduPlanAssoruleResult">
        <include refid="selectScheduPlanAssoruleVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduPlanAssorule" parameterType="com.mes.smartdispath.domain.ScheduPlanAssorule">
        insert into tb_schedu_plan_assorule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="ruleCode != null and ruleCode != ''">rule_code,</if>
            <if test="ruleName != null and ruleName != ''">rule_name,</if>
            <if test="mainRuleId != null and mainRuleId != ''">main_rule_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="siteType != null">site_type,</if>
            <if test="activityType != null">activity_type,</if>
            <if test="activitySubtype != null">activity_subtype,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="ruleCode != null and ruleCode != ''">#{ruleCode},</if>
            <if test="ruleName != null and ruleName != ''">#{ruleName},</if>
            <if test="mainRuleId != null and mainRuleId != ''">#{mainRuleId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="siteType != null">#{siteType},</if>
            <if test="activityType != null">#{activityType},</if>
            <if test="activitySubtype != null">#{activitySubtype},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduPlanAssorule" parameterType="java.util.List">
        insert into tb_schedu_plan_assorule (
            id,
            rule_code,
            rule_name,
            main_rule_id,
            business_type,
            site_type,
            activity_type,
            activity_subtype,
            create_time,
            update_time,
            status
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.ruleCode},
                #{item.ruleName},
                #{item.mainRuleId},
                #{item.businessType},
                #{item.siteType},
                #{item.activityType},
                #{item.activitySubtype},
                #{item.createTime},
                #{item.updateTime},
                #{item.status}
            )
        </foreach>
    </insert>

    <update id="updateScheduPlanAssorule" parameterType="com.mes.smartdispath.domain.ScheduPlanAssorule">
        update tb_schedu_plan_assorule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">rule_code = #{ruleCode},</if>
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="mainRuleId != null and mainRuleId != ''">main_rule_id = #{mainRuleId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="siteType != null">site_type = #{siteType},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="activitySubtype != null">activity_subtype = #{activitySubtype},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateByMainRuleId" parameterType="com.mes.smartdispath.domain.ScheduPlanAssorule">
        update tb_schedu_plan_assorule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleCode != null and ruleCode != ''">rule_code = #{ruleCode},</if>
            <if test="ruleName != null and ruleName != ''">rule_name = #{ruleName},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="siteType != null">site_type = #{siteType},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="activitySubtype != null">activity_subtype = #{activitySubtype},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            update_time = now(),
        </trim>
        where main_rule_id = #{mainRuleId}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduPlanAssorule" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_plan_assorule
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.ruleCode != null and item.ruleCode != ''">
                    rule_code = #{item.ruleCode},
                </if>
                <if test="item.ruleName != null and item.ruleName != ''">
                    rule_name = #{item.ruleName},
                </if>
                <if test="item.mainRuleId != null and item.mainRuleId != ''">
                    main_rule_id = #{item.mainRuleId},
                </if>
                <if test="item.businessType != null">
                    business_type = #{item.businessType},
                </if>
                <if test="item.siteType != null">
                    site_type = #{item.siteType},
                </if>
                <if test="item.activityType != null">
                    activity_type = #{item.activityType},
                </if>
                <if test="item.activitySubtype != null">
                    activity_subtype = #{item.activitySubtype},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduPlanAssoruleById" parameterType="String">
        delete from tb_schedu_plan_assorule where id = #{id}
    </delete>

    <delete id="deleteScheduPlanAssoruleByIds" parameterType="String">
        delete from tb_schedu_plan_assorule where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>