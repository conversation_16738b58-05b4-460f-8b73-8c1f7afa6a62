<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.SysAttachInfoRelMapper">
    
    <resultMap type="com.mes.smartdispath.domain.SysAttachInfoRel" id="SysAttachInfoRelResult">
        <result property="id"    column="id"    />
        <result property="businessId"    column="business_id"    />
        <result property="tbMark"    column="tb_mark"    />
        <result property="attachId"    column="attach_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectSysAttachInfoRelVo">
        select id, business_id, tb_mark, attach_id, create_by, create_time, update_by, update_time, status from tb_sys_attach_info_rel
    </sql>

    <select id="selectSysAttachInfoRelList" parameterType="com.mes.smartdispath.domain.SysAttachInfoRel" resultMap="SysAttachInfoRelResult">
        <include refid="selectSysAttachInfoRelVo"/>
        <where>  
            <if test="businessId != null  and businessId != ''"> and business_id = #{businessId}</if>
            <if test="tbMark != null  and tbMark != ''"> and tb_mark = #{tbMark}</if>
            <if test="attachId != null  and attachId != ''"> and attach_id = #{attachId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysAttachInfoRelById" parameterType="String" resultMap="SysAttachInfoRelResult">
        <include refid="selectSysAttachInfoRelVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysAttachInfoRel" parameterType="com.mes.smartdispath.domain.SysAttachInfoRel">
        insert into tb_sys_attach_info_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessId != null and businessId != ''">business_id,</if>
            <if test="tbMark != null and tbMark != ''">tb_mark,</if>
            <if test="attachId != null and attachId != ''">attach_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            status,
            create_time,
            update_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessId != null and businessId != ''">#{businessId},</if>
            <if test="tbMark != null and tbMark != ''">#{tbMark},</if>
            <if test="attachId != null and attachId != ''">#{attachId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            'A',
            now(),
            now(),
         </trim>
    </insert>

    <update id="updateSysAttachInfoRel" parameterType="com.mes.smartdispath.domain.SysAttachInfoRel">
        update tb_sys_attach_info_rel
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessId != null and businessId != ''">business_id = #{businessId},</if>
            <if test="tbMark != null and tbMark != ''">tb_mark = #{tbMark},</if>
            <if test="attachId != null and attachId != ''">attach_id = #{attachId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysAttachInfoRelById" parameterType="String">
        delete from tb_sys_attach_info_rel where id = #{id}
    </delete>

    <delete id="deleteSysAttachInfoRelByIds" parameterType="String">
        delete from tb_sys_attach_info_rel where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>