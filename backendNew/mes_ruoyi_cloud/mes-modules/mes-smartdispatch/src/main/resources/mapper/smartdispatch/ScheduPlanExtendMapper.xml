<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduPlanExtendMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduPlanExtend" id="ScheduPlanExtendResult">
        <result property="id"    column="id"    />
        <result property="planId"    column="plan_id"    />
        <result property="planName"    column="plan_name"    />
        <result property="planCode"    column="plan_code"    />
        <result property="siteId"    column="site_id"    />
        <result property="siteName"    column="site_name"    />
        <result property="siteAttr"    column="site_attr"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="riskPara"    column="risk_para"    />
        <result property="collectPara"    column="collect_para"    />
        <result property="isQualityCtl"    column="is_quality_ctl"    />
        <result property="maxCollPoint"    column="max_coll_point"    />
        <result property="isMark"    column="is_mark"    />
        <result property="isBlind"    column="is_blind"    />
        <result property="planExtend"    column="plan_extend"    />
    </resultMap>

    <sql id="selectScheduPlanExtendVo">
        select id, plan_id, plan_name, plan_code, site_id, site_name, site_attr, risk_level, risk_para, collect_para, is_quality_ctl, max_coll_point, is_mark, is_blind, plan_extend from tb_schedu_plan_extend
    </sql>

    <select id="selectScheduPlanExtendList" parameterType="com.mes.smartdispath.domain.ScheduPlanExtend" resultMap="ScheduPlanExtendResult">
        <include refid="selectScheduPlanExtendVo"/>
        <where>  
            <if test="planId != null  and planId != ''"> and plan_id = #{planId}</if>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="planCode != null  and planCode != ''"> and plan_code = #{planCode}</if>
            <if test="siteId != null  and siteId != ''"> and site_id = #{siteId}</if>
            <if test="siteName != null  and siteName != ''"> and site_name like concat('%', #{siteName}, '%')</if>
            <if test="siteAttr != null  and siteAttr != ''"> and site_attr = #{siteAttr}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="riskPara != null  and riskPara != ''"> and risk_para = #{riskPara}</if>
            <if test="collectPara != null  and collectPara != ''"> and collect_para = #{collectPara}</if>
            <if test="isQualityCtl != null  and isQualityCtl != ''"> and is_quality_ctl = #{isQualityCtl}</if>
            <if test="maxCollPoint != null  and maxCollPoint != ''"> and max_coll_point = #{maxCollPoint}</if>
            <if test="isMark != null  and isMark != ''"> and is_mark = #{isMark}</if>
            <if test="isBlind != null  and isBlind != ''"> and is_blind = #{isBlind}</if>
            <if test="planExtend != null  and planExtend != ''"> and plan_extend = #{planExtend}</if>
        </where>
    </select>

    <select id="selectPlanExtendByPlanIds" resultMap="ScheduPlanExtendResult">
        <include refid="selectScheduPlanExtendVo"/>
        where plan_id in
            <foreach item="planId" collection="planIds" open="(" separator="," close=")">
                #{planId}
            </foreach>
    </select>
    
    <select id="selectScheduPlanExtendById" parameterType="String" resultMap="ScheduPlanExtendResult">
        <include refid="selectScheduPlanExtendVo"/>
        where id = #{id}
    </select>

    <select id="selectScheduPlanExtendByPlanId" parameterType="String" resultMap="ScheduPlanExtendResult">
        <include refid="selectScheduPlanExtendVo"/>
        where plan_id = #{planId}
    </select>

    <insert id="insertScheduPlanExtend" parameterType="com.mes.smartdispath.domain.ScheduPlanExtend">
        insert into tb_schedu_plan_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="planName != null">plan_name,</if>
            <if test="planCode != null and planCode != ''">plan_code,</if>
            <if test="siteId != null">site_id,</if>
            <if test="siteName != null">site_name,</if>
            <if test="siteAttr != null">site_attr,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="riskPara != null">risk_para,</if>
            <if test="collectPara != null">collect_para,</if>
            <if test="isQualityCtl != null">is_quality_ctl,</if>
            <if test="maxCollPoint != null">max_coll_point,</if>
            <if test="isMark != null">is_mark,</if>
            <if test="isBlind != null">is_blind,</if>
            <if test="planExtend != null">plan_extend,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},</if>
            <if test="planName != null">#{planName},</if>
            <if test="planCode != null and planCode != ''">#{planCode},</if>
            <if test="siteId != null">#{siteId},</if>
            <if test="siteName != null">#{siteName},</if>
            <if test="siteAttr != null">#{siteAttr},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="riskPara != null">#{riskPara},</if>
            <if test="collectPara != null">#{collectPara},</if>
            <if test="isQualityCtl != null">#{isQualityCtl},</if>
            <if test="maxCollPoint != null">#{maxCollPoint},</if>
            <if test="isMark != null">#{isMark},</if>
            <if test="isBlind != null">#{isBlind},</if>
            <if test="planExtend != null">#{planExtend},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduPlanExtend" parameterType="java.util.List">
        insert into tb_schedu_plan_extend (
        id,
        plan_id,
        plan_name,
        plan_code,
        site_id,
        site_name,
        site_attr,
        risk_level,
        risk_para,
        collect_para,
        is_quality_ctl,
        max_coll_point,
        is_mark,
        is_blind,
        plan_extend
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.planId},
            #{item.planName},
            #{item.planCode},
            #{item.siteId},
            #{item.siteName},
            #{item.siteAttr},
            #{item.riskLevel},
            #{item.riskPara},
            #{item.collectPara},
            #{item.isQualityCtl},
            #{item.maxCollPoint},
            #{item.isMark},
            #{item.isBlind},
            #{item.planExtend}
            )
        </foreach>
    </insert>

    <update id="updateScheduPlanExtend" parameterType="com.mes.smartdispath.domain.ScheduPlanExtend">
        update tb_schedu_plan_extend
        <trim prefix="SET" suffixOverrides=",">
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="planCode != null and planCode != ''">plan_code = #{planCode},</if>
            <if test="siteId != null">site_id = #{siteId},</if>
            <if test="siteName != null">site_name = #{siteName},</if>
            <if test="siteAttr != null">site_attr = #{siteAttr},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="riskPara != null">risk_para = #{riskPara},</if>
            <if test="collectPara != null">collect_para = #{collectPara},</if>
            <if test="isQualityCtl != null">is_quality_ctl = #{isQualityCtl},</if>
            <if test="maxCollPoint != null">max_coll_point = #{maxCollPoint},</if>
            <if test="isMark != null">is_mark = #{isMark},</if>
            <if test="isBlind != null">is_blind = #{isBlind},</if>
            <if test="planExtend != null">plan_extend = #{planExtend},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduPlanExtend" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_plan_extend
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.planId != null">
                    plan_id = #{item.planId},
                </if>
                <if test="item.planName != null">
                    plan_name = #{item.planName},
                </if>
                <if test="item.siteId != null">
                    site_id = #{item.siteId},
                </if>
                <if test="item.siteName != null">
                    site_name = #{item.siteName},
                </if>
                <if test="item.siteAttr != null">
                    site_attr = #{item.siteAttr},
                </if>
                <if test="item.riskLevel != null">
                    risk_level = #{item.riskLevel},
                </if>
                <if test="item.riskPara != null">
                    risk_para = #{item.riskPara},
                </if>
                <if test="item.collectPara != null">
                    collect_para = #{item.collectPara},
                </if>
                <if test="item.isQualityCtl != null">
                    is_quality_ctl = #{item.isQualityCtl},
                </if>
                <if test="item.maxCollPoint != null">
                    max_coll_point = #{item.maxCollPoint},
                </if>
                <if test="item.isMark != null">
                    is_mark = #{item.isMark},
                </if>
                <if test="item.isBlind != null">
                    is_blind = #{item.isBlind},
                </if>
                <if test="item.planExtend != null">
                    plan_extend = #{item.planExtend},
                </if>
            </trim>
            where plan_code = #{item.planCode}
        </foreach>
    </update>

    <delete id="deleteScheduPlanExtendById" parameterType="String">
        delete from tb_schedu_plan_extend where id = #{id}
    </delete>

    <delete id="deleteScheduPlanExtendByIds" parameterType="String">
        delete from tb_schedu_plan_extend where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>