<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduMonitorQualityTargetMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduMonitorQualityTarget" id="ScheduMonitorQualityTargetResult">
        <result property="id"    column="id"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityCode"    column="city_code"    />
        <result property="cityName"    column="city_name"    />
        <result property="siteName"    column="site_name"    />
        <result property="siteId"    column="site_id"    />
        <result property="siteType"    column="site_type"    />
        <result property="businessType"    column="business_type"    />
        <result property="monitIndex"    column="monit_index"    />
        <result property="accuracy"    column="accuracy"    />
        <result property="precision"    column="precision"    />
        <result property="effectivenessRate"    column="effectiveness_rate"    />
        <result property="captureRate"    column="capture_rate"    />
        <result property="quactrlPassRate"    column="quactrl_pass_rate"    />
        <result property="statYear"    column="stat_year"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <resultMap id="MonitorQualityTargetVOResult" type="com.mes.smartdispath.domain.vo.ScheduMonitorQualityTargetVO" extends="ScheduMonitorQualityTargetResult">
        <result property="siteTypeName"    column="site_type_name"    />
        <result property="hasAutomaticStation"    column="has_automatic_station"    />
        <result property="businessTypeName"    column="business_type_name"    />
        <result property="monitIndexName"    column="monit_index_name"    />
    </resultMap>

    <sql id="selectScheduMonitorQualityTargetVo">
        select id, province_code, province_name, city_code, city_name, site_name, site_id, site_type, business_type, monit_index, accuracy, precision, effectiveness_rate, capture_rate, quactrl_pass_rate, stat_year, create_by, create_time, update_by, update_time, status, tenant_id from tb_schedu_monitor_quality_target
    </sql>

    <select id="selectStatYearCollectMonitorQualityTargetList" parameterType="com.mes.smartdispath.domain.dto.QualityTargetPerformanceQueryDTO" resultMap="MonitorQualityTargetVOResult">
        select
        t1.business_type, t1.monit_index,
        t1.stat_year,
        t3.dict_value AS monit_index_name,
        ROUND(AVG(t1.accuracy),2) as accuracy,
        ROUND(AVG(t1.precision),2) as precision,
        ROUND(AVG(t1.effectiveness_rate),2) as effectiveness_rate,
        ROUND(AVG(t1.capture_rate),2) as capture_rate,
        ROUND(AVG(t1.quactrl_pass_rate),2) as quactrl_pass_rate
        from tb_schedu_monitor_quality_target t1
            left join tb_res_site t2 on t1.site_id = t2.id
            left join tb_sys_dict t3 on t1.monit_index = t3.dict_code
            and t3.state = 'A' and t3.class_code = (CASE
                WHEN business_type = 'water' THEN 'water_quality_index'
                WHEN business_type = 'air' THEN 'gas_quality_index'
            END)
        where
            t1.status = 'A'
            and t1.business_type = #{businessType}
            and t1.stat_year = #{statYear}
            <if test="provinceCodeArr != null  and provinceCodeArr != ''">
                and t1.province_code in
                <foreach item="item" collection="provinceCodeArr" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="maintainUnitCodeArr != null  and maintainUnitCodeArr != ''">
                and t2.operation_unit in
                <foreach item="item" collection="maintainUnitCodeArr" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        group by t1.business_type, t1.monit_index, t1.stat_year, t3.dict_value
    </select>

    <select id="selectScheduMonitorQualityTargetList" parameterType="com.mes.smartdispath.domain.dto.ScheduMonitorQualityTargetQueryDTO" resultMap="MonitorQualityTargetVOResult">
        select
            t1.id, t1.province_code, t1.province_name, t1.city_code, t1.city_name, t1.site_name, t1.site_id, t1.site_type,
            t1.business_type, t1.monit_index, t1.accuracy, t1.precision, t1.effectiveness_rate, t1.capture_rate, t1.quactrl_pass_rate,
            t1.stat_year, t1.create_by, t1.create_time, t1.update_by, t1.update_time, t1.status, t1.tenant_id,
            (SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{siteTypeDictClassCode} AND b.state = 'A' AND b.dict_code = t1.site_type AND b.parent_dict_code = t1.business_type limit 1) AS site_type_name,
            (SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{businessTypeDictClassCode} AND b.state = 'A' AND b.dict_code = t1.business_type limit 1) AS business_type_name,
            CASE
                WHEN business_type = 'water' THEN (SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = 'water_quality_index' AND b.state = 'A' AND t1.monit_index = b.dict_code limit 1)
                WHEN business_type = 'air' THEN (SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = 'gas_quality_index' AND b.state = 'A' AND t1.monit_index = b.dict_code limit 1)
            END AS monit_index_name,
            t2.has_automatic_station
        from tb_schedu_monitor_quality_target t1
        left join tb_res_site t2 on t1.site_id = t2.id
        where
            t1.status = 'A'
            <if test="siteIdArr != null  and siteIdArr != ''">
                and t1.site_id in
                <foreach item="item" collection="siteIdArr" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="siteId != null  and siteId != '' and null == siteIdArr"> and t1.site_id = #{siteId}</if>
            <if test="cityCodeArr != null  and cityCodeArr != ''">
                and t1.city_code in
                <foreach item="item" collection="cityCodeArr" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cityCode != null  and cityCode != '' and null == cityCodeArr"> and t1.city_code = #{cityCode}</if>
            <if test="provinceCode != null  and provinceCode != ''"> and t1.province_code = #{provinceCode}</if>
            <if test="provinceName != null  and provinceName != ''"> and t1.province_name like concat('%', #{provinceName}, '%')</if>
            <if test="cityName != null  and cityName != ''"> and t1.city_name like concat('%', #{cityName}, '%')</if>
            <if test="siteName != null  and siteName != ''"> and t1.site_name like concat('%', #{siteName}, '%')</if>
            <if test="siteType != null  and siteType != ''"> and t1.site_type = #{siteType}</if>
            <if test="businessType != null  and businessType != ''"> and t1.business_type = #{businessType}</if>
            <if test="monitIndex != null  and monitIndex != ''"> and t1.monit_index = #{monitIndex}</if>
            <if test="accuracy != null  and accuracy != ''"> and t1.accuracy = #{accuracy}</if>
            <if test="precision != null  and precision != ''"> and t1.precision = #{precision}</if>
            <if test="effectivenessRate != null  and effectivenessRate != ''"> and t1.effectiveness_rate = #{effectivenessRate}</if>
            <if test="captureRate != null  and captureRate != ''"> and t1.capture_rate = #{captureRate}</if>
            <if test="quactrlPassRate != null  and quactrlPassRate != ''"> and t1.quactrl_pass_rate = #{quactrlPassRate}</if>
            <if test="statYear != null  and statYear != ''"> and t1.stat_year = #{statYear}</if>
            <if test="tenantId != null  and tenantId != ''"> and t1.tenant_id = #{tenantId}</if>
            <if test="packageId != null  and packageId != '' and null == packageIdArr"> and t2.package_id = #{packageId}</if>
            <if test="packageIdArr != null and packageIdArr != ''">
                and t2.package_id in
                <foreach item="item" collection="packageIdArr" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="hasAutomaticStation != null  and hasAutomaticStation != ''"> and t2.has_automatic_station = #{hasAutomaticStation}</if>
            <if test="startTime != null  and startTime != ''"> and t1.create_time >= #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and t1.create_time &lt;= #{endTime}</if>
            ORDER by t1.stat_year desc, t1.province_code, t1.city_code, t1.site_type
    </select>

    <select id="selectScheduMonitorQualityTargetCount" parameterType="com.mes.smartdispath.domain.dto.ScheduMonitorQualityTargetQueryDTO" resultType="java.lang.Integer">
        SELECT count(*) from tb_schedu_monitor_quality_target
        WHERE status = 'A'
        <if test="siteId != null  and siteId != ''"> and site_id = #{siteId}</if>
        <if test="siteIdArr != null  and siteIdArr != ''">
            and site_id in
            <foreach item="item" collection="siteIdArr" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
        <if test="monitIndex != null  and monitIndex != ''"> and monit_index = #{monitIndex}</if>
        <if test="statYear != null  and statYear != ''"> and stat_year = #{statYear}</if>
    </select>
    
    <select id="selectScheduMonitorQualityTargetById" parameterType="String" resultMap="ScheduMonitorQualityTargetResult">
        <include refid="selectScheduMonitorQualityTargetVo"/>
        where id = #{id}
    </select>

    <insert id="batchInsertScheduMonitorQualityTarget" parameterType="java.util.List">
        INSERT INTO tb_schedu_monitor_quality_target (
            province_code,
            province_name,
            city_code,
            city_name,
            site_name,
            site_id,
            site_type,
            business_type,
            monit_index,
            accuracy,
            precision,
            effectiveness_rate,
            capture_rate,
            quactrl_pass_rate,
            create_by,
            create_time,
            update_by,
            update_time,
            stat_year,
            status
        ) VALUES
        <foreach collection="targetList" item="item" separator=",">
            (
                #{item.provinceCode},
                #{item.provinceName},
                #{item.cityCode},
                #{item.cityName},
                #{item.siteName},
                #{item.siteId},
                #{item.siteType},
                #{item.businessType},
                #{item.monitIndex},
                #{item.accuracy},
                #{item.precision},
                #{item.effectivenessRate},
                #{item.captureRate},
                #{item.quactrlPassRate},
                #{item.createBy},
                now(),
                #{item.updateBy},
                now(),
                #{item.statYear},
                'A'
            )
        </foreach>
    </insert>

    <insert id="insertScheduMonitorQualityTarget" parameterType="com.mes.smartdispath.domain.ScheduMonitorQualityTarget">
        insert into tb_schedu_monitor_quality_target
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="provinceCode != null and provinceCode != ''">province_code,</if>
            <if test="provinceName != null and provinceName != ''">province_name,</if>
            <if test="cityCode != null and cityCode != ''">city_code,</if>
            <if test="cityName != null and cityName != ''">city_name,</if>
            <if test="siteName != null and siteName != ''">site_name,</if>
            <if test="siteId != null and siteId != ''">site_id,</if>
            <if test="siteType != null and siteType != ''">site_type,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="monitIndex != null and monitIndex != ''">monit_index,</if>
            <if test="accuracy != null">accuracy,</if>
            <if test="precision != null">precision,</if>
            <if test="effectivenessRate != null">effectiveness_rate,</if>
            <if test="captureRate != null">capture_rate,</if>
            <if test="quactrlPassRate != null">quactrl_pass_rate,</if>
            <if test="statYear != null">stat_year,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="tenantId != null">tenant_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="provinceCode != null and provinceCode != ''">#{provinceCode},</if>
            <if test="provinceName != null and provinceName != ''">#{provinceName},</if>
            <if test="cityCode != null and cityCode != ''">#{cityCode},</if>
            <if test="cityName != null and cityName != ''">#{cityName},</if>
            <if test="siteName != null and siteName != ''">#{siteName},</if>
            <if test="siteId != null and siteId != ''">#{siteId},</if>
            <if test="siteType != null and siteType != ''">#{siteType},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="monitIndex != null and monitIndex != ''">#{monitIndex},</if>
            <if test="accuracy != null">#{accuracy},</if>
            <if test="precision != null">#{precision},</if>
            <if test="effectivenessRate != null">#{effectivenessRate},</if>
            <if test="captureRate != null">#{captureRate},</if>
            <if test="quactrlPassRate != null">#{quactrlPassRate},</if>
            <if test="statYear != null">#{statYear},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="tenantId != null">#{tenantId},</if>
         </trim>
    </insert>

    <update id="updateScheduMonitorQualityTarget" parameterType="com.mes.smartdispath.domain.ScheduMonitorQualityTarget">
        update tb_schedu_monitor_quality_target
        <trim prefix="SET" suffixOverrides=",">
            <if test="provinceCode != null and provinceCode != ''">province_code = #{provinceCode},</if>
            <if test="provinceName != null and provinceName != ''">province_name = #{provinceName},</if>
            <if test="cityCode != null and cityCode != ''">city_code = #{cityCode},</if>
            <if test="cityName != null and cityName != ''">city_name = #{cityName},</if>
            <if test="siteName != null and siteName != ''">site_name = #{siteName},</if>
            <if test="siteId != null and siteId != ''">site_id = #{siteId},</if>
            <if test="siteType != null and siteType != ''">site_type = #{siteType},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="monitIndex != null and monitIndex != ''">monit_index = #{monitIndex},</if>
            <if test="accuracy != null">accuracy = #{accuracy},</if>
            <if test="precision != null">precision = #{precision},</if>
            <if test="effectivenessRate != null">effectiveness_rate = #{effectivenessRate},</if>
            <if test="captureRate != null">capture_rate = #{captureRate},</if>
            <if test="quactrlPassRate != null">quactrl_pass_rate = #{quactrlPassRate},</if>
            <if test="statYear != null">stat_year = #{statYear},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScheduMonitorQualityTargetById" parameterType="String">
        delete from tb_schedu_monitor_quality_target where id = #{id}
    </delete>

    <delete id="deleteScheduMonitorQualityTargetByIds" parameterType="String">
        delete from tb_schedu_monitor_quality_target where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>