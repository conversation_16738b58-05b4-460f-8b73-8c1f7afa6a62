<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduApprovalRecordMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduApprovalRecord" id="ScheduApprovalRecordResult">
        <result property="id"    column="id"    />
        <result property="businessId"    column="business_id"    />
        <result property="moduleType"    column="module_type"    />
        <result property="approver"    column="approver"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="approvalOpinion"    column="approval_opinion"    />
        <result property="approvalTime"    column="approval_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <sql id="selectScheduApprovalRecordVo">
        select id, business_id, module_type, approver, approval_status, approval_opinion, approval_time, create_by, create_time, update_by, update_time, status, tenant_id from tb_schedu_approval_record
    </sql>

    <select id="selectBizApprovalRecordList" parameterType="com.mes.smartdispath.domain.dto.ScheduApprovalRecordQueryDTO" resultMap="ScheduApprovalRecordResult">
        SELECT
            business_id,
            approval_opinion,
            approver,
            approval_time
        FROM (
            SELECT
                business_id,
                approval_opinion,
                approver,
                approval_time,
                ROW_NUMBER() OVER (
                    PARTITION BY business_id
                    ORDER BY approval_time DESC
                ) AS rn
                FROM tb_schedu_approval_record
                WHERE status = 'A'
                AND module_type = #{moduleType}
                <if test="businessIdArr != null  and businessIdArr != ''">
                  and business_id in
                    <foreach item="item" collection="businessIdArr" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            )
            WHERE rn = 1
    </select>

    <select id="selectScheduApprovalRecordList" parameterType="com.mes.smartdispath.domain.ScheduApprovalRecord" resultMap="ScheduApprovalRecordResult">
        <include refid="selectScheduApprovalRecordVo"/>
        <where>  
            <if test="businessId != null  and businessId != ''"> and business_id = #{businessId}</if>
            <if test="moduleType != null  and moduleType != ''"> and module_type = #{moduleType}</if>
            <if test="approver != null  and approver != ''"> and approver = #{approver}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="approvalOpinion != null  and approvalOpinion != ''"> and approval_opinion = #{approvalOpinion}</if>
            <if test="approvalTime != null  and approvalTime != ''"> and approval_time = #{approvalTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
        </where>
    </select>
    
    <select id="selectScheduApprovalRecordById" parameterType="String" resultMap="ScheduApprovalRecordResult">
        <include refid="selectScheduApprovalRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduApprovalRecord" parameterType="com.mes.smartdispath.domain.ScheduApprovalRecord">
        insert into tb_schedu_approval_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="businessId != null and businessId != ''">business_id,</if>
            <if test="moduleType != null and moduleType != ''">module_type,</if>
            <if test="approver != null">approver,</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status,</if>
            <if test="approvalOpinion != null">approval_opinion,</if>
            <if test="approvalTime != null">approval_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="tenantId != null">tenant_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="businessId != null and businessId != ''">#{businessId},</if>
            <if test="moduleType != null and moduleType != ''">#{moduleType},</if>
            <if test="approver != null">#{approver},</if>
            <if test="approvalStatus != null and approvalStatus != ''">#{approvalStatus},</if>
            <if test="approvalOpinion != null">#{approvalOpinion},</if>
            <if test="approvalTime != null">#{approvalTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="tenantId != null">#{tenantId},</if>
         </trim>
    </insert>

    <insert id="batchInsertScheduApprovalRecord" parameterType="java.util.List">
        INSERT INTO tb_schedu_approval_record (
            business_id,
            module_type,
            approver,
            approval_status,
            approval_opinion,
            approval_time,
            create_by,
            create_time,
            update_by,
            update_time,
            status
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.businessId},
            #{item.moduleType},
            #{item.approver},
            #{item.approvalStatus},
            #{item.approvalOpinion},
            now(),
            #{item.createBy},
            now(),
            #{item.updateBy},
            now(),
            'A'
        )
        </foreach>
    </insert>

    <update id="updateScheduApprovalRecord" parameterType="com.mes.smartdispath.domain.ScheduApprovalRecord">
        update tb_schedu_approval_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessId != null and businessId != ''">business_id = #{businessId},</if>
            <if test="moduleType != null and moduleType != ''">module_type = #{moduleType},</if>
            <if test="approver != null">approver = #{approver},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="approvalOpinion != null">approval_opinion = #{approvalOpinion},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateByBizId" parameterType="com.mes.smartdispath.domain.ScheduApprovalRecord">
        update tb_schedu_approval_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="approver != null">approver = #{approver},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="approvalOpinion != null">approval_opinion = #{approvalOpinion},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            update_time = now(),
        </trim>
        where business_id = #{businessId} and module_type = #{moduleType}
    </update>

    <delete id="deleteScheduApprovalRecordById" parameterType="String">
        delete from tb_schedu_approval_record where id = #{id}
    </delete>

    <delete id="deleteScheduApprovalRecordByIds" parameterType="String">
        delete from tb_schedu_approval_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>