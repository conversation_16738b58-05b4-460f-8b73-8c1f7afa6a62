<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduTaskHisMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduTaskHis" id="ScheduTaskHisResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="creatMethod"    column="creat_method"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="dispatchedTime"    column="dispatched_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="taskDesc"    column="task_desc"    />
        <result property="backTime"    column="back_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="approvalStatus"    column="approval_status"    />
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.TaskTrendVO" id="TaskTrendResult">
        <result property="date"    column="date"    />
        <result property="totalCount"    column="total_count"    />
        <result property="completeTaskCount"    column="complete_task_count"    />
        <result property="unCompleteTaskCount"    column="un_complete_task_count"    />
    </resultMap>

    <sql id="selectScheduTaskHisVo">
        select id, task_id, task_code, task_name, creat_method, task_status, dispatched_time, start_time, end_time, task_desc, back_time, create_time, create_by, update_time, update_by, is_deleted, tenant_id, approval_status from tb_schedu_task_his
    </sql>

    <select id="selectScheduTaskHisList" parameterType="com.mes.smartdispath.domain.ScheduTaskHis" resultMap="ScheduTaskHisResult">
        <include refid="selectScheduTaskHisVo"/>
        <where>  
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="creatMethod != null  and creatMethod != ''"> and creat_method = #{creatMethod}</if>
            <if test="taskStatus != null  and taskStatus != ''"> and task_status = #{taskStatus}</if>
            <if test="dispatchedTime != null  and dispatchedTime != ''"> and dispatched_time = #{dispatchedTime}</if>
            <if test="startTime != null  and startTime != ''"> and start_time = #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and end_time = #{endTime}</if>
            <if test="taskDesc != null  and taskDesc != ''"> and task_desc = #{taskDesc}</if>
            <if test="backTime != null  and backTime != ''"> and back_time = #{backTime}</if>
            <if test="isDeleted != null  and isDeleted != ''"> and is_deleted = #{isDeleted}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
        </where>
    </select>

    <select id="selectTaskTrend" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultMap="TaskTrendResult">
        SELECT
            DATE_FORMAT(b.create_time, '%Y-%m-%d') AS date,
            COUNT(DISTINCT b.task_id) AS total_count,
            COUNT(DISTINCT CASE WHEN b.task_status = 4 THEN b.task_id END) AS complete_task_count,
            COUNT(DISTINCT CASE WHEN b.task_status IN (1, 2, 3) THEN b.task_id END) AS un_complete_task_count
        FROM
            tb_schedu_task_his b
            INNER JOIN tb_schedu_task_plan_rel a
            ON b.task_id = a.task_id
            INNER JOIN tb_schedu_plan_info c
            ON a.plan_id = c.id
            LEFT JOIN tb_schedu_task_extend e
            ON b.task_id = e.task_id
            INNER JOIN (
                SELECT
                    task_id,
                    DATE_FORMAT(create_time, '%Y-%m-%d') AS date,
                    MAX(create_time) AS max_create_time
                FROM tb_schedu_task_his
                GROUP BY task_id, DATE_FORMAT(create_time, '%Y-%m-%d')
            ) latest
            ON b.task_id = latest.task_id
            AND b.create_time = latest.max_create_time
        where
            b.approval_status = 'approved'
            <if test="businessType != null  and businessType != ''"> and c.business_type = #{businessType}</if>
            <if test="startDate != null  and startDate != ''"> and b.create_time >= #{startDate}</if>
            <if test="endDate != null  and endDate != ''"> and b.create_time <![CDATA[<=]]>  #{endDate}</if>
            <if test="province != null  and province != ''"> and c.province_code = #{province}</if>
            <if test="city != null  and city != ''"> and c.city_code = #{city}</if>
            <if test="maintainUnitCode != null  and maintainUnitCode != ''"> and e.maintain_unit_code = #{maintainUnitCode}</if>
            <if test="siteId != null  and siteId != ''"> and c.site_id = #{siteId}</if>
        GROUP BY DATE_FORMAT(b.create_time, '%Y-%m-%d')
        ORDER BY DATE_FORMAT(b.create_time, '%Y-%m-%d')
    </select>
    
    <select id="selectScheduTaskHisById" parameterType="String" resultMap="ScheduTaskHisResult">
        <include refid="selectScheduTaskHisVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduTaskHis" parameterType="com.mes.smartdispath.domain.ScheduTaskHis">
        insert into tb_schedu_task_his
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="creatMethod != null">creat_method,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="dispatchedTime != null">dispatched_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="taskDesc != null">task_desc,</if>
            <if test="backTime != null">back_time,</if>
            <if test="createTime != null and createTime != ''">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="approvalStatus != null">approval_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="creatMethod != null">#{creatMethod},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="dispatchedTime != null">#{dispatchedTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="taskDesc != null">#{taskDesc},</if>
            <if test="backTime != null">#{backTime},</if>
            <if test="createTime != null and createTime != ''">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduTaskHis" parameterType="java.util.List">
        insert into tb_schedu_task_his (
            task_id,
            task_code,
            task_name,
            creat_method,
            task_status,
            dispatched_time,
            start_time,
            end_time,
            task_desc,
            back_time,
            create_time,
            create_by,
            update_time,
            update_by,
            is_deleted,
            tenant_id,
            approval_status
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.taskId},
                #{item.taskCode},
                #{item.taskName},
                #{item.creatMethod},
                #{item.taskStatus},
                #{item.dispatchedTime},
                #{item.startTime},
                #{item.endTime},
                #{item.taskDesc},
                #{item.backTime},
                now(),
                #{item.createBy},
                now(),
                #{item.updateBy},
                'N',
                #{item.tenantId},
                #{item.approvalStatus}
            )
        </foreach>
    </insert>

    <update id="updateScheduTaskHis" parameterType="com.mes.smartdispath.domain.ScheduTaskHis">
        update tb_schedu_task_his
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="creatMethod != null">creat_method = #{creatMethod},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="dispatchedTime != null">dispatched_time = #{dispatchedTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="taskDesc != null">task_desc = #{taskDesc},</if>
            <if test="backTime != null">back_time = #{backTime},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduTaskHis" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_task_his
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.taskId != null">
                    task_id = #{item.taskId},
                </if>
                <if test="item.taskCode != null and item.taskCode != ''">
                    task_code = #{item.taskCode},
                </if>
                <if test="item.taskName != null and item.taskName != ''">
                    task_name = #{item.taskName},
                </if>
                <if test="item.creatMethod != null">
                    creat_method = #{item.creatMethod},
                </if>
                <if test="item.taskStatus != null">
                    task_status = #{item.taskStatus},
                </if>
                <if test="item.dispatchedTime != null">
                    dispatched_time = #{item.dispatchedTime},
                </if>
                <if test="item.startTime != null">
                    start_time = #{item.startTime},
                </if>
                <if test="item.endTime != null">
                    end_time = #{item.endTime},
                </if>
                <if test="item.taskDesc != null">
                    task_desc = #{item.taskDesc},
                </if>
                <if test="item.backTime != null">
                    back_time = #{item.backTime},
                </if>
                <if test="item.createTime != null and item.createTime != ''">
                    create_time = #{item.createTime},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted},
                </if>
                <if test="item.tenantId != null">
                    tenant_id = #{item.tenantId},
                </if>
                <if test="item.approvalStatus != null">
                    approval_status = #{item.approvalStatus},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduTaskHisById" parameterType="String">
        delete from tb_schedu_task_his where id = #{id}
    </delete>

    <delete id="deleteScheduTaskHisByIds" parameterType="String">
        delete from tb_schedu_task_his where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>