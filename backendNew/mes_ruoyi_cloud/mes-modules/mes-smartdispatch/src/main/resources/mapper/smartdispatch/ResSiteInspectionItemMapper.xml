<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ResSiteInspectionItemMapper">

    <resultMap type="com.mes.smartdispath.domain.ResSiteInspectionItem" id="ResSiteInspectionItemResult">
            <result property="id" column="id"/>
            <result property="siteId" column="site_id"/>
            <result property="inspectionType" column="inspection_type"/>
            <result property="inspectionTime" column="inspection_time"/>
            <result property="parameters" column="parameters"/>
            <result property="riskLevel" column="risk_level"/>
            <result property="riskReason" column="risk_reason"/>
            <result property="remarks" column="remarks"/>
            <result property="createdBy" column="created_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updatedBy" column="updated_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectResSiteInspectionItemVo">
        select id, site_id, inspection_type, inspection_time, parameters, risk_level, risk_reason, remarks, created_by, create_time, updated_by, update_time
        from tb_res_site_inspection_item
    </sql>

    <select id="selectResSiteInspectionItemList" parameterType="com.mes.smartdispath.domain.ResSiteInspectionItem"
            resultMap="ResSiteInspectionItemResult">
        <include refid="selectResSiteInspectionItemVo"/>
        <where>
                        <if test="siteId != null  and siteId != ''">
                            and site_id = #{siteId}
                        </if>
                        <if test="inspectionType != null  and inspectionType != ''">
                            and inspection_type = #{inspectionType}
                        </if>
                        <if test="inspectionTime != null  and inspectionTime != ''">
                            and inspection_time = #{inspectionTime}
                        </if>
                        <if test="parameters != null  and parameters != ''">
                            and parameters = #{parameters}
                        </if>
                        <if test="riskLevel != null  and riskLevel != ''">
                            and risk_level = #{riskLevel}
                        </if>
                        <if test="riskReason != null  and riskReason != ''">
                            and risk_reason = #{riskReason}
                        </if>
                        <if test="remarks != null  and remarks != ''">
                            and remarks = #{remarks}
                        </if>
                        <if test="createdBy != null  and createdBy != ''">
                            and created_by = #{createdBy}
                        </if>
                        <if test="updatedBy != null  and updatedBy != ''">
                            and updated_by = #{updatedBy}
                        </if>
        </where>
    </select>

    <select id="selectInspectionItemBySiteId" parameterType="String"
            resultMap="ResSiteInspectionItemResult">
        <include refid="selectResSiteInspectionItemVo"/>
        where site_id = #{siteId}
    </select>

    <select id="selectResSiteInspectionItemById" parameterType="String"
            resultMap="ResSiteInspectionItemResult">
            <include refid="selectResSiteInspectionItemVo"/>
            where id = #{id}
    </select>

    <insert id="insertResSiteInspectionItem" parameterType="com.mes.smartdispath.domain.ResSiteInspectionItem">
        insert into tb_res_site_inspection_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,
                    </if>
                    <if test="siteId != null and siteId != ''">site_id,
                    </if>
                    <if test="inspectionType != null and inspectionType != ''">inspection_type,
                    </if>
                    <if test="inspectionTime != null and inspectionTime != ''">inspection_time,
                    </if>
                    <if test="parameters != null and parameters != ''">parameters,
                    </if>
                    <if test="riskLevel != null">risk_level,
                    </if>
                    <if test="riskReason != null">risk_reason,
                    </if>
                    <if test="remarks != null">remarks,
                    </if>
                    <if test="createdBy != null and createdBy != ''">created_by,
                    </if>
                    <if test="createTime != null and createTime != ''">create_time,
                    </if>
                    <if test="updatedBy != null and updatedBy != ''">updated_by,
                    </if>
                    <if test="updateTime != null and updateTime != ''">update_time,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="siteId != null and siteId != ''">#{siteId},
                    </if>
                    <if test="inspectionType != null and inspectionType != ''">#{inspectionType},
                    </if>
                    <if test="inspectionTime != null and inspectionTime != ''">#{inspectionTime},
                    </if>
                    <if test="parameters != null and parameters != ''">#{parameters},
                    </if>
                    <if test="riskLevel != null">#{riskLevel},
                    </if>
                    <if test="riskReason != null">#{riskReason},
                    </if>
                    <if test="remarks != null">#{remarks},
                    </if>
                    <if test="createdBy != null and createdBy != ''">#{createdBy},
                    </if>
                    <if test="createTime != null and createTime != ''">#{createTime},
                    </if>
                    <if test="updatedBy != null and updatedBy != ''">#{updatedBy},
                    </if>
                    <if test="updateTime != null and updateTime != ''">#{updateTime},
                    </if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertResSiteInspectionItem" parameterType="java.util.List">
        insert into tb_res_site_inspection_item (
                id,
                site_id,
                inspection_type,
                inspection_time,
                parameters,
                risk_level,
                risk_reason,
                remarks,
                created_by,
                create_time,
                updated_by,
                update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
                #{item.id},
                #{item.siteId},
                #{item.inspectionType},
                #{item.inspectionTime},
                #{item.parameters},
                #{item.riskLevel},
                #{item.riskReason},
                #{item.remarks},
                #{item.createdBy},
                #{item.createTime},
                #{item.updatedBy},
                #{item.updateTime}
        )
        </foreach>
    </insert>

    <update id="updateResSiteInspectionItem" parameterType="com.mes.smartdispath.domain.ResSiteInspectionItem">
        update tb_res_site_inspection_item
        <trim prefix="SET" suffixOverrides=",">
                    <if test="siteId != null and siteId != ''">site_id =
                        #{siteId},
                    </if>
                    <if test="inspectionType != null and inspectionType != ''">inspection_type =
                        #{inspectionType},
                    </if>
                    <if test="inspectionTime != null and inspectionTime != ''">inspection_time =
                        #{inspectionTime},
                    </if>
                    <if test="parameters != null and parameters != ''">parameters =
                        #{parameters},
                    </if>
                    <if test="riskLevel != null">risk_level =
                        #{riskLevel},
                    </if>
                    <if test="riskReason != null">risk_reason =
                        #{riskReason},
                    </if>
                    <if test="remarks != null">remarks =
                        #{remarks},
                    </if>
                    <if test="createdBy != null and createdBy != ''">created_by =
                        #{createdBy},
                    </if>
                    <if test="createTime != null and createTime != ''">create_time =
                        #{createTime},
                    </if>
                    <if test="updatedBy != null and updatedBy != ''">updated_by =
                        #{updatedBy},
                    </if>
                    <if test="updateTime != null and updateTime != ''">update_time =
                        #{updateTime},
                    </if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateResSiteInspectionItem" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_res_site_inspection_item
            <trim prefix="SET" suffixOverrides=",">
                        <if test="item.siteId != null and item.siteId != ''">
                            site_id = #{item.siteId},
                        </if>
                        <if test="item.inspectionType != null and item.inspectionType != ''">
                            inspection_type = #{item.inspectionType},
                        </if>
                        <if test="item.inspectionTime != null and item.inspectionTime != ''">
                            inspection_time = #{item.inspectionTime},
                        </if>
                        <if test="item.parameters != null and item.parameters != ''">
                            parameters = #{item.parameters},
                        </if>
                        <if test="item.riskLevel != null">
                            risk_level = #{item.riskLevel},
                        </if>
                        <if test="item.riskReason != null">
                            risk_reason = #{item.riskReason},
                        </if>
                        <if test="item.remarks != null">
                            remarks = #{item.remarks},
                        </if>
                        <if test="item.createdBy != null and item.createdBy != ''">
                            created_by = #{item.createdBy},
                        </if>
                        <if test="item.createTime != null and item.createTime != ''">
                            create_time = #{item.createTime},
                        </if>
                        <if test="item.updatedBy != null and item.updatedBy != ''">
                            updated_by = #{item.updatedBy},
                        </if>
                        <if test="item.updateTime != null and item.updateTime != ''">
                            update_time = #{item.updateTime},
                        </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteResSiteInspectionItemById" parameterType="String">
        delete
        from tb_res_site_inspection_item where id = #{id}
    </delete>

    <delete id="deleteResSiteInspectionItemByIds" parameterType="String">
        delete from tb_res_site_inspection_item where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>