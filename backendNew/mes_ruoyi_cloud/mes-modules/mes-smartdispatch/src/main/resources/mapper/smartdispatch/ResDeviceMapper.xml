<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ResDeviceMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ResDevice" id="ResDeviceResult">
        <result property="id"    column="id"    />
        <result property="bindProvinceId"    column="bind_province_id"    />
        <result property="bindCityId"    column="bind_city_id"    />
        <result property="operationUnitId"    column="operation_unit_id"    />
        <result property="deviceCode"    column="device_code"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceBrandId"    column="device_brand_id"    />
        <result property="deviceModelId"    column="device_model_id"    />
        <result property="deviceProtocolId"    column="device_protocol_id"    />
        <result property="deviceStatus"    column="device_status"    />
        <result property="isPrimaryDevice"    column="is_primary_device"    />
        <result property="monitoringElement"    column="monitoring_element"    />
        <result property="deviceTypeLevel1"    column="device_type_level1"    />
        <result property="deviceTypeLevel2"    column="device_type_level2"    />
        <result property="deviceTypeLevel3"    column="device_type_level3"    />
        <result property="isMeasuringInstrument"    column="is_measuring_instrument"    />
        <result property="deviceSn"    column="device_sn"    />
        <result property="source"    column="source"    />
        <result property="inventoryStatus"    column="inventory_status"    />
        <result property="warehouseId"    column="warehouse_id"    />
        <result property="deviceReagent"    column="device_reagent"    />
        <result property="detectionLimit"    column="detection_limit"    />
        <result property="analysisMethod"    column="analysis_method"    />
        <result property="testItems"    column="test_items"    />
        <result property="serviceYears"    column="service_years"    />
        <result property="spareDevicePeriod"    column="spare_device_period"    />
        <result property="spareAcceptanceType"    column="spare_acceptance_type"    />
        <result property="isHotSpare"    column="is_hot_spare"    />
        <result property="spareWarehouseName"    column="spare_warehouse_name"    />
        <result property="snPhotoUrl"    column="sn_photo_url"    />
        <result property="purchaseDate"    column="purchase_date"    />
        <result property="isBlacklist"    column="is_blacklist"    />
        <result property="firmwareVersion"    column="firmware_version"    />
        <result property="softwareVersion"    column="software_version"    />
        <result property="measurementPrinciple"    column="measurement_principle"    />
        <result property="heatingMethod"    column="heating_method"    />
        <result property="maxHeatingTemp"    column="max_heating_temp"    />
        <result property="targetHumidity"    column="target_humidity"    />
        <result property="lastInstallTime"    column="last_install_time"    />
        <result property="runningDays"    column="running_days"    />
        <result property="firstInstallTime"    column="first_install_time"    />
        <result property="currentProvinceId"    column="current_province_id"    />
        <result property="currentCityId"    column="current_city_id"    />
        <result property="currentSiteId"    column="current_site_id"    />
        <result property="bindingSiteId"    column="binding_site_id"    />
        <result property="communicationProtocol"    column="communication_protocol"    />
        <result property="serialPort"    column="serial_port"    />
        <result property="baudRate"    column="baud_rate"    />
        <result property="parityCheck"    column="parity_check"    />
        <result property="dataBits"    column="data_bits"    />
        <result property="stopBits"    column="stop_bits"    />
        <result property="targetIp"    column="target_ip"    />
        <result property="targetPort"    column="target_port"    />
        <result property="instrumentAddress"    column="instrument_address"    />
        <result property="isConnectedToSerialServer"    column="is_connected_to_serial_server"    />
        <result property="serialServerIp"    column="serial_server_ip"    />
        <result property="serialServerPort"    column="serial_server_port"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectResDeviceVo">
        select id, bind_province_id, bind_city_id, operation_unit_id, device_code, device_name, device_brand_id, device_model_id, device_protocol_id, device_status, is_primary_device, monitoring_element, device_type_level1, device_type_level2, device_type_level3, is_measuring_instrument, device_sn, source, inventory_status, warehouse_id, device_reagent, detection_limit, analysis_method, test_items, service_years, spare_device_period, spare_acceptance_type, is_hot_spare, spare_warehouse_name, sn_photo_url, purchase_date, is_blacklist, firmware_version, software_version, measurement_principle, heating_method, max_heating_temp, target_humidity, last_install_time, running_days, first_install_time, current_province_id, current_city_id, current_site_id, binding_site_id, communication_protocol, serial_port, baud_rate, parity_check, data_bits, stop_bits, target_ip, target_port, instrument_address, is_connected_to_serial_server, serial_server_ip, serial_server_port, created_by, create_time, updated_by, update_time from tb_res_device
    </sql>

    <select id="selectSiteLinkDevice" parameterType="com.mes.smartdispath.domain.dto.ResDeviceQueryDTO" resultMap="ResDeviceResult">
        SELECT
            t1.*
        FROM
            tb_res_device t1
        WHERE t1.current_site_id = #{siteId}
          AND t1.id not in
              (
                  SELECT devc_model
                  from tb_schedu_plan_info t2
                  where t2.status = 'A'
                    and t2.site_id = #{siteId}
                    <if test="null != curDevcModel and curDevcModel != ''">
                        and t2.devc_model != #{curDevcModel}
                    </if>
            )
    </select>

    <select id="selectResDeviceList" parameterType="com.mes.smartdispath.domain.ResDevice" resultMap="ResDeviceResult">
        <include refid="selectResDeviceVo"/>
        <where>  
            <if test="bindProvinceId != null  and bindProvinceId != ''"> and bind_province_id = #{bindProvinceId}</if>
            <if test="bindCityId != null  and bindCityId != ''"> and bind_city_id = #{bindCityId}</if>
            <if test="operationUnitId != null  and operationUnitId != ''"> and operation_unit_id = #{operationUnitId}</if>
            <if test="deviceCode != null  and deviceCode != ''"> and device_code = #{deviceCode}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceBrandId != null  and deviceBrandId != ''"> and device_brand_id = #{deviceBrandId}</if>
            <if test="deviceModelId != null  and deviceModelId != ''"> and device_model_id = #{deviceModelId}</if>
            <if test="deviceProtocolId != null  and deviceProtocolId != ''"> and device_protocol_id = #{deviceProtocolId}</if>
            <if test="deviceStatus != null  and deviceStatus != ''"> and device_status = #{deviceStatus}</if>
            <if test="isPrimaryDevice != null  and isPrimaryDevice != ''"> and is_primary_device = #{isPrimaryDevice}</if>
            <if test="monitoringElement != null  and monitoringElement != ''"> and monitoring_element = #{monitoringElement}</if>
            <if test="deviceTypeLevel1 != null  and deviceTypeLevel1 != ''"> and device_type_level1 = #{deviceTypeLevel1}</if>
            <if test="deviceTypeLevel2 != null  and deviceTypeLevel2 != ''"> and device_type_level2 = #{deviceTypeLevel2}</if>
            <if test="deviceTypeLevel3 != null  and deviceTypeLevel3 != ''"> and device_type_level3 = #{deviceTypeLevel3}</if>
            <if test="isMeasuringInstrument != null  and isMeasuringInstrument != ''"> and is_measuring_instrument = #{isMeasuringInstrument}</if>
            <if test="deviceSn != null  and deviceSn != ''"> and device_sn = #{deviceSn}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
            <if test="inventoryStatus != null  and inventoryStatus != ''"> and inventory_status = #{inventoryStatus}</if>
            <if test="warehouseId != null  and warehouseId != ''"> and warehouse_id = #{warehouseId}</if>
            <if test="deviceReagent != null  and deviceReagent != ''"> and device_reagent = #{deviceReagent}</if>
            <if test="detectionLimit != null  and detectionLimit != ''"> and detection_limit = #{detectionLimit}</if>
            <if test="analysisMethod != null  and analysisMethod != ''"> and analysis_method = #{analysisMethod}</if>
            <if test="testItems != null  and testItems != ''"> and test_items = #{testItems}</if>
            <if test="serviceYears != null  and serviceYears != ''"> and service_years = #{serviceYears}</if>
            <if test="spareDevicePeriod != null  and spareDevicePeriod != ''"> and spare_device_period = #{spareDevicePeriod}</if>
            <if test="spareAcceptanceType != null  and spareAcceptanceType != ''"> and spare_acceptance_type = #{spareAcceptanceType}</if>
            <if test="isHotSpare != null  and isHotSpare != ''"> and is_hot_spare = #{isHotSpare}</if>
            <if test="spareWarehouseName != null  and spareWarehouseName != ''"> and spare_warehouse_name like concat('%', #{spareWarehouseName}, '%')</if>
            <if test="snPhotoUrl != null  and snPhotoUrl != ''"> and sn_photo_url = #{snPhotoUrl}</if>
            <if test="purchaseDate != null  and purchaseDate != ''"> and purchase_date = #{purchaseDate}</if>
            <if test="isBlacklist != null  and isBlacklist != ''"> and is_blacklist = #{isBlacklist}</if>
            <if test="firmwareVersion != null  and firmwareVersion != ''"> and firmware_version = #{firmwareVersion}</if>
            <if test="softwareVersion != null  and softwareVersion != ''"> and software_version = #{softwareVersion}</if>
            <if test="measurementPrinciple != null  and measurementPrinciple != ''"> and measurement_principle = #{measurementPrinciple}</if>
            <if test="heatingMethod != null  and heatingMethod != ''"> and heating_method = #{heatingMethod}</if>
            <if test="maxHeatingTemp != null  and maxHeatingTemp != ''"> and max_heating_temp = #{maxHeatingTemp}</if>
            <if test="targetHumidity != null  and targetHumidity != ''"> and target_humidity = #{targetHumidity}</if>
            <if test="lastInstallTime != null  and lastInstallTime != ''"> and last_install_time = #{lastInstallTime}</if>
            <if test="runningDays != null  and runningDays != ''"> and running_days = #{runningDays}</if>
            <if test="firstInstallTime != null  and firstInstallTime != ''"> and first_install_time = #{firstInstallTime}</if>
            <if test="currentProvinceId != null  and currentProvinceId != ''"> and current_province_id = #{currentProvinceId}</if>
            <if test="currentCityId != null  and currentCityId != ''"> and current_city_id = #{currentCityId}</if>
            <if test="currentSiteId != null  and currentSiteId != ''"> and current_site_id = #{currentSiteId}</if>
            <if test="bindingSiteId != null  and bindingSiteId != ''"> and binding_site_id = #{bindingSiteId}</if>
            <if test="communicationProtocol != null  and communicationProtocol != ''"> and communication_protocol = #{communicationProtocol}</if>
            <if test="serialPort != null  and serialPort != ''"> and serial_port = #{serialPort}</if>
            <if test="baudRate != null  and baudRate != ''"> and baud_rate = #{baudRate}</if>
            <if test="parityCheck != null  and parityCheck != ''"> and parity_check = #{parityCheck}</if>
            <if test="dataBits != null  and dataBits != ''"> and data_bits = #{dataBits}</if>
            <if test="stopBits != null  and stopBits != ''"> and stop_bits = #{stopBits}</if>
            <if test="targetIp != null  and targetIp != ''"> and target_ip = #{targetIp}</if>
            <if test="targetPort != null  and targetPort != ''"> and target_port = #{targetPort}</if>
            <if test="instrumentAddress != null  and instrumentAddress != ''"> and instrument_address = #{instrumentAddress}</if>
            <if test="isConnectedToSerialServer != null  and isConnectedToSerialServer != ''"> and is_connected_to_serial_server = #{isConnectedToSerialServer}</if>
            <if test="serialServerIp != null  and serialServerIp != ''"> and serial_server_ip = #{serialServerIp}</if>
            <if test="serialServerPort != null  and serialServerPort != ''"> and serial_server_port = #{serialServerPort}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
        </where>
        ORDER BY device_status ASC
    </select>
    
    <select id="selectResDeviceById" parameterType="String" resultMap="ResDeviceResult">
        <include refid="selectResDeviceVo"/>
        where id = #{id}
    </select>

    <insert id="insertResDevice" parameterType="com.mes.smartdispath.domain.ResDevice">
        insert into tb_res_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bindProvinceId != null and bindProvinceId != ''">bind_province_id,</if>
            <if test="bindCityId != null">bind_city_id,</if>
            <if test="operationUnitId != null">operation_unit_id,</if>
            <if test="deviceCode != null and deviceCode != ''">device_code,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="deviceBrandId != null and deviceBrandId != ''">device_brand_id,</if>
            <if test="deviceModelId != null and deviceModelId != ''">device_model_id,</if>
            <if test="deviceProtocolId != null">device_protocol_id,</if>
            <if test="deviceStatus != null">device_status,</if>
            <if test="isPrimaryDevice != null">is_primary_device,</if>
            <if test="monitoringElement != null and monitoringElement != ''">monitoring_element,</if>
            <if test="deviceTypeLevel1 != null and deviceTypeLevel1 != ''">device_type_level1,</if>
            <if test="deviceTypeLevel2 != null">device_type_level2,</if>
            <if test="deviceTypeLevel3 != null">device_type_level3,</if>
            <if test="isMeasuringInstrument != null">is_measuring_instrument,</if>
            <if test="deviceSn != null">device_sn,</if>
            <if test="source != null">source,</if>
            <if test="inventoryStatus != null">inventory_status,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="deviceReagent != null">device_reagent,</if>
            <if test="detectionLimit != null">detection_limit,</if>
            <if test="analysisMethod != null">analysis_method,</if>
            <if test="testItems != null">test_items,</if>
            <if test="serviceYears != null">service_years,</if>
            <if test="spareDevicePeriod != null">spare_device_period,</if>
            <if test="spareAcceptanceType != null">spare_acceptance_type,</if>
            <if test="isHotSpare != null">is_hot_spare,</if>
            <if test="spareWarehouseName != null">spare_warehouse_name,</if>
            <if test="snPhotoUrl != null and snPhotoUrl != ''">sn_photo_url,</if>
            <if test="purchaseDate != null">purchase_date,</if>
            <if test="isBlacklist != null">is_blacklist,</if>
            <if test="firmwareVersion != null">firmware_version,</if>
            <if test="softwareVersion != null">software_version,</if>
            <if test="measurementPrinciple != null">measurement_principle,</if>
            <if test="heatingMethod != null">heating_method,</if>
            <if test="maxHeatingTemp != null">max_heating_temp,</if>
            <if test="targetHumidity != null">target_humidity,</if>
            <if test="lastInstallTime != null">last_install_time,</if>
            <if test="runningDays != null">running_days,</if>
            <if test="firstInstallTime != null">first_install_time,</if>
            <if test="currentProvinceId != null">current_province_id,</if>
            <if test="currentCityId != null">current_city_id,</if>
            <if test="currentSiteId != null">current_site_id,</if>
            <if test="bindingSiteId != null">binding_site_id,</if>
            <if test="communicationProtocol != null">communication_protocol,</if>
            <if test="serialPort != null">serial_port,</if>
            <if test="baudRate != null">baud_rate,</if>
            <if test="parityCheck != null">parity_check,</if>
            <if test="dataBits != null">data_bits,</if>
            <if test="stopBits != null">stop_bits,</if>
            <if test="targetIp != null">target_ip,</if>
            <if test="targetPort != null">target_port,</if>
            <if test="instrumentAddress != null">instrument_address,</if>
            <if test="isConnectedToSerialServer != null">is_connected_to_serial_server,</if>
            <if test="serialServerIp != null">serial_server_ip,</if>
            <if test="serialServerPort != null">serial_server_port,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bindProvinceId != null and bindProvinceId != ''">#{bindProvinceId},</if>
            <if test="bindCityId != null">#{bindCityId},</if>
            <if test="operationUnitId != null">#{operationUnitId},</if>
            <if test="deviceCode != null and deviceCode != ''">#{deviceCode},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="deviceBrandId != null and deviceBrandId != ''">#{deviceBrandId},</if>
            <if test="deviceModelId != null and deviceModelId != ''">#{deviceModelId},</if>
            <if test="deviceProtocolId != null">#{deviceProtocolId},</if>
            <if test="deviceStatus != null">#{deviceStatus},</if>
            <if test="isPrimaryDevice != null">#{isPrimaryDevice},</if>
            <if test="monitoringElement != null and monitoringElement != ''">#{monitoringElement},</if>
            <if test="deviceTypeLevel1 != null and deviceTypeLevel1 != ''">#{deviceTypeLevel1},</if>
            <if test="deviceTypeLevel2 != null">#{deviceTypeLevel2},</if>
            <if test="deviceTypeLevel3 != null">#{deviceTypeLevel3},</if>
            <if test="isMeasuringInstrument != null">#{isMeasuringInstrument},</if>
            <if test="deviceSn != null">#{deviceSn},</if>
            <if test="source != null">#{source},</if>
            <if test="inventoryStatus != null">#{inventoryStatus},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="deviceReagent != null">#{deviceReagent},</if>
            <if test="detectionLimit != null">#{detectionLimit},</if>
            <if test="analysisMethod != null">#{analysisMethod},</if>
            <if test="testItems != null">#{testItems},</if>
            <if test="serviceYears != null">#{serviceYears},</if>
            <if test="spareDevicePeriod != null">#{spareDevicePeriod},</if>
            <if test="spareAcceptanceType != null">#{spareAcceptanceType},</if>
            <if test="isHotSpare != null">#{isHotSpare},</if>
            <if test="spareWarehouseName != null">#{spareWarehouseName},</if>
            <if test="snPhotoUrl != null and snPhotoUrl != ''">#{snPhotoUrl},</if>
            <if test="purchaseDate != null">#{purchaseDate},</if>
            <if test="isBlacklist != null">#{isBlacklist},</if>
            <if test="firmwareVersion != null">#{firmwareVersion},</if>
            <if test="softwareVersion != null">#{softwareVersion},</if>
            <if test="measurementPrinciple != null">#{measurementPrinciple},</if>
            <if test="heatingMethod != null">#{heatingMethod},</if>
            <if test="maxHeatingTemp != null">#{maxHeatingTemp},</if>
            <if test="targetHumidity != null">#{targetHumidity},</if>
            <if test="lastInstallTime != null">#{lastInstallTime},</if>
            <if test="runningDays != null">#{runningDays},</if>
            <if test="firstInstallTime != null">#{firstInstallTime},</if>
            <if test="currentProvinceId != null">#{currentProvinceId},</if>
            <if test="currentCityId != null">#{currentCityId},</if>
            <if test="currentSiteId != null">#{currentSiteId},</if>
            <if test="bindingSiteId != null">#{bindingSiteId},</if>
            <if test="communicationProtocol != null">#{communicationProtocol},</if>
            <if test="serialPort != null">#{serialPort},</if>
            <if test="baudRate != null">#{baudRate},</if>
            <if test="parityCheck != null">#{parityCheck},</if>
            <if test="dataBits != null">#{dataBits},</if>
            <if test="stopBits != null">#{stopBits},</if>
            <if test="targetIp != null">#{targetIp},</if>
            <if test="targetPort != null">#{targetPort},</if>
            <if test="instrumentAddress != null">#{instrumentAddress},</if>
            <if test="isConnectedToSerialServer != null">#{isConnectedToSerialServer},</if>
            <if test="serialServerIp != null">#{serialServerIp},</if>
            <if test="serialServerPort != null">#{serialServerPort},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateResDevice" parameterType="com.mes.smartdispath.domain.ResDevice">
        update tb_res_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="bindProvinceId != null and bindProvinceId != ''">bind_province_id = #{bindProvinceId},</if>
            <if test="bindCityId != null">bind_city_id = #{bindCityId},</if>
            <if test="operationUnitId != null">operation_unit_id = #{operationUnitId},</if>
            <if test="deviceCode != null and deviceCode != ''">device_code = #{deviceCode},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="deviceBrandId != null and deviceBrandId != ''">device_brand_id = #{deviceBrandId},</if>
            <if test="deviceModelId != null and deviceModelId != ''">device_model_id = #{deviceModelId},</if>
            <if test="deviceProtocolId != null">device_protocol_id = #{deviceProtocolId},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
            <if test="isPrimaryDevice != null">is_primary_device = #{isPrimaryDevice},</if>
            <if test="monitoringElement != null and monitoringElement != ''">monitoring_element = #{monitoringElement},</if>
            <if test="deviceTypeLevel1 != null and deviceTypeLevel1 != ''">device_type_level1 = #{deviceTypeLevel1},</if>
            <if test="deviceTypeLevel2 != null">device_type_level2 = #{deviceTypeLevel2},</if>
            <if test="deviceTypeLevel3 != null">device_type_level3 = #{deviceTypeLevel3},</if>
            <if test="isMeasuringInstrument != null">is_measuring_instrument = #{isMeasuringInstrument},</if>
            <if test="deviceSn != null">device_sn = #{deviceSn},</if>
            <if test="source != null">source = #{source},</if>
            <if test="inventoryStatus != null">inventory_status = #{inventoryStatus},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="deviceReagent != null">device_reagent = #{deviceReagent},</if>
            <if test="detectionLimit != null">detection_limit = #{detectionLimit},</if>
            <if test="analysisMethod != null">analysis_method = #{analysisMethod},</if>
            <if test="testItems != null">test_items = #{testItems},</if>
            <if test="serviceYears != null">service_years = #{serviceYears},</if>
            <if test="spareDevicePeriod != null">spare_device_period = #{spareDevicePeriod},</if>
            <if test="spareAcceptanceType != null">spare_acceptance_type = #{spareAcceptanceType},</if>
            <if test="isHotSpare != null">is_hot_spare = #{isHotSpare},</if>
            <if test="spareWarehouseName != null">spare_warehouse_name = #{spareWarehouseName},</if>
            <if test="snPhotoUrl != null and snPhotoUrl != ''">sn_photo_url = #{snPhotoUrl},</if>
            <if test="purchaseDate != null">purchase_date = #{purchaseDate},</if>
            <if test="isBlacklist != null">is_blacklist = #{isBlacklist},</if>
            <if test="firmwareVersion != null">firmware_version = #{firmwareVersion},</if>
            <if test="softwareVersion != null">software_version = #{softwareVersion},</if>
            <if test="measurementPrinciple != null">measurement_principle = #{measurementPrinciple},</if>
            <if test="heatingMethod != null">heating_method = #{heatingMethod},</if>
            <if test="maxHeatingTemp != null">max_heating_temp = #{maxHeatingTemp},</if>
            <if test="targetHumidity != null">target_humidity = #{targetHumidity},</if>
            <if test="lastInstallTime != null">last_install_time = #{lastInstallTime},</if>
            <if test="runningDays != null">running_days = #{runningDays},</if>
            <if test="firstInstallTime != null">first_install_time = #{firstInstallTime},</if>
            <if test="currentProvinceId != null">current_province_id = #{currentProvinceId},</if>
            <if test="currentCityId != null">current_city_id = #{currentCityId},</if>
            <if test="currentSiteId != null">current_site_id = #{currentSiteId},</if>
            <if test="bindingSiteId != null">binding_site_id = #{bindingSiteId},</if>
            <if test="communicationProtocol != null">communication_protocol = #{communicationProtocol},</if>
            <if test="serialPort != null">serial_port = #{serialPort},</if>
            <if test="baudRate != null">baud_rate = #{baudRate},</if>
            <if test="parityCheck != null">parity_check = #{parityCheck},</if>
            <if test="dataBits != null">data_bits = #{dataBits},</if>
            <if test="stopBits != null">stop_bits = #{stopBits},</if>
            <if test="targetIp != null">target_ip = #{targetIp},</if>
            <if test="targetPort != null">target_port = #{targetPort},</if>
            <if test="instrumentAddress != null">instrument_address = #{instrumentAddress},</if>
            <if test="isConnectedToSerialServer != null">is_connected_to_serial_server = #{isConnectedToSerialServer},</if>
            <if test="serialServerIp != null">serial_server_ip = #{serialServerIp},</if>
            <if test="serialServerPort != null">serial_server_port = #{serialServerPort},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResDeviceById" parameterType="String">
        delete from tb_res_device where id = #{id}
    </delete>

    <delete id="deleteResDeviceByIds" parameterType="String">
        delete from tb_res_device where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>