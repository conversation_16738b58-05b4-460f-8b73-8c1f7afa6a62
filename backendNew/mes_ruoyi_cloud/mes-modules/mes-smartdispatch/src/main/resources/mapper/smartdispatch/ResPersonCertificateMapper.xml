<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ResPersonCertificateMapper">

    <resultMap type="com.mes.smartdispath.domain.ResPersonCertificate" id="ResPersonCertificateResult">
        <result property="certificateId" column="certificate_id"/>
        <result property="idCard" column="id_card"/>
        <result property="certificateName" column="certificate_name"/>
        <result property="certificateNumber" column="certificate_number"/>
        <result property="issuingAuthority" column="issuing_authority"/>
        <result property="expirationDate" column="expiration_date"/>
        <result property="certificateScanUrl" column="certificate_scan_url"/>
        <result property="certificateStatus" column="certificate_status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectResPersonCertificateVo">
        select certificate_id,
               id_card,
               certificate_name,
               certificate_number,
               issuing_authority,
               expiration_date,
               certificate_scan_url,
               certificate_status,
               created_by,
               create_time,
               updated_by,
               update_time
        from tb_res_person_certificate
    </sql>

    <select id="selectResPersonCertificateList" parameterType="com.mes.smartdispath.domain.ResPersonCertificate"
            resultMap="ResPersonCertificateResult">
        <include refid="selectResPersonCertificateVo"/>
        <where>
            <if test="idCard != null  and idCard != ''">
                and id_card = #{idCard}
            </if>
            <if test="certificateName != null  and certificateName != ''">
                and certificate_name like concat('%', #{certificateName}, '%')
            </if>
            <if test="certificateNumber != null  and certificateNumber != ''">
                and certificate_number = #{certificateNumber}
            </if>
            <if test="issuingAuthority != null  and issuingAuthority != ''">
                and issuing_authority = #{issuingAuthority}
            </if>
            <if test="expirationDate != null  and expirationDate != ''">
                and expiration_date = #{expirationDate}
            </if>
            <if test="certificateScanUrl != null  and certificateScanUrl != ''">
                and certificate_scan_url = #{certificateScanUrl}
            </if>
            <if test="certificateStatus != null  and certificateStatus != ''">
                and certificate_status = #{certificateStatus}
            </if>
            <if test="createdBy != null  and createdBy != ''">
                and created_by = #{createdBy}
            </if>
            <if test="updatedBy != null  and updatedBy != ''">
                and updated_by = #{updatedBy}
            </if>
        </where>
    </select>

    <select id="selectPersonCertificateListByIdCards" resultMap="ResPersonCertificateResult">
        SELECT
            id_card,
            COALESCE(LISTAGG(DISTINCT certificate_name, ',') WITHIN GROUP (ORDER BY certificate_name), '') AS certificate_name
        FROM tb_res_person_certificate
        WHERE certificate_status = '1'
          and id_card in
            <foreach collection="idCardArr" item="idCard" open="(" separator="," close=")">
                #{idCard}
            </foreach>
        GROUP BY id_card
    </select>

    <select id="selectResPersonCertificateByCertificateId" parameterType="String"
            resultMap="ResPersonCertificateResult">
        <include refid="selectResPersonCertificateVo"/>
        where certificate_id = #{certificateId}
    </select>

    <insert id="insertResPersonCertificate" parameterType="com.mes.smartdispath.domain.ResPersonCertificate">
        insert into tb_res_person_certificate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="certificateId != null">certificate_id,
            </if>
            <if test="idCard != null and idCard != ''">id_card,
            </if>
            <if test="certificateName != null and certificateName != ''">certificate_name,
            </if>
            <if test="certificateNumber != null">certificate_number,
            </if>
            <if test="issuingAuthority != null">issuing_authority,
            </if>
            <if test="expirationDate != null">expiration_date,
            </if>
            <if test="certificateScanUrl != null">certificate_scan_url,
            </if>
            <if test="certificateStatus != null">certificate_status,
            </if>
            <if test="createdBy != null">created_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updatedBy != null">updated_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="certificateId != null">#{certificateId},
            </if>
            <if test="idCard != null and idCard != ''">#{idCard},
            </if>
            <if test="certificateName != null and certificateName != ''">#{certificateName},
            </if>
            <if test="certificateNumber != null">#{certificateNumber},
            </if>
            <if test="issuingAuthority != null">#{issuingAuthority},
            </if>
            <if test="expirationDate != null">#{expirationDate},
            </if>
            <if test="certificateScanUrl != null">#{certificateScanUrl},
            </if>
            <if test="certificateStatus != null">#{certificateStatus},
            </if>
            <if test="createdBy != null">#{createdBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updatedBy != null">#{updatedBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertResPersonCertificate" parameterType="java.util.List">
        insert into tb_res_person_certificate (
        certificate_id,
        id_card,
        certificate_name,
        certificate_number,
        issuing_authority,
        expiration_date,
        certificate_scan_url,
        certificate_status,
        created_by,
        create_time,
        updated_by,
        update_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.certificateId},
            #{item.idCard},
            #{item.certificateName},
            #{item.certificateNumber},
            #{item.issuingAuthority},
            #{item.expirationDate},
            #{item.certificateScanUrl},
            #{item.certificateStatus},
            #{item.createdBy},
            #{item.createTime},
            #{item.updatedBy},
            #{item.updateTime}
            )
        </foreach>
    </insert>

    <update id="updateResPersonCertificate" parameterType="com.mes.smartdispath.domain.ResPersonCertificate">
        update tb_res_person_certificate
        <trim prefix="SET" suffixOverrides=",">
            <if test="idCard != null and idCard != ''">id_card =
                #{idCard},
            </if>
            <if test="certificateName != null and certificateName != ''">certificate_name =
                #{certificateName},
            </if>
            <if test="certificateNumber != null">certificate_number =
                #{certificateNumber},
            </if>
            <if test="issuingAuthority != null">issuing_authority =
                #{issuingAuthority},
            </if>
            <if test="expirationDate != null">expiration_date =
                #{expirationDate},
            </if>
            <if test="certificateScanUrl != null">certificate_scan_url =
                #{certificateScanUrl},
            </if>
            <if test="certificateStatus != null">certificate_status =
                #{certificateStatus},
            </if>
            <if test="createdBy != null">created_by =
                #{createdBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updatedBy != null">updated_by =
                #{updatedBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
        </trim>
        where certificate_id = #{certificateId}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateResPersonCertificate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_res_person_certificate
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.idCard != null and item.idCard != ''">
                    id_card = #{item.idCard},
                </if>
                <if test="item.certificateName != null and item.certificateName != ''">
                    certificate_name = #{item.certificateName},
                </if>
                <if test="item.certificateNumber != null">
                    certificate_number = #{item.certificateNumber},
                </if>
                <if test="item.issuingAuthority != null">
                    issuing_authority = #{item.issuingAuthority},
                </if>
                <if test="item.expirationDate != null">
                    expiration_date = #{item.expirationDate},
                </if>
                <if test="item.certificateScanUrl != null">
                    certificate_scan_url = #{item.certificateScanUrl},
                </if>
                <if test="item.certificateStatus != null">
                    certificate_status = #{item.certificateStatus},
                </if>
                <if test="item.createdBy != null">
                    created_by = #{item.createdBy},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime},
                </if>
                <if test="item.updatedBy != null">
                    updated_by = #{item.updatedBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
            </trim>
            where certificate_id = #{item.certificateId}
        </foreach>
    </update>

    <delete id="deleteResPersonCertificateByCertificateId" parameterType="String">
        delete
        from tb_res_person_certificate
        where certificate_id = #{certificateId}
    </delete>

    <delete id="deleteResPersonCertificateByCertificateIds" parameterType="String">
        delete from tb_res_person_certificate where certificate_id in
        <foreach item="certificateId" collection="array" open="(" separator="," close=")">
            #{certificateId}
        </foreach>
    </delete>
</mapper>