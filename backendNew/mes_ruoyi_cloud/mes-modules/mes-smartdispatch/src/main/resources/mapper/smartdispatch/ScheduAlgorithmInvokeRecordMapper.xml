<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduAlgorithmInvokeRecordMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord" id="ScheduAlgorithmInvokeRecordResult">
        <result property="id"    column="id"    />
        <result property="algorithmId"    column="algorithm_id"    />
        <result property="algorithmCode"    column="algorithm_code"    />
        <result property="algorithmName"    column="algorithm_name"    />
        <result property="invokeTime"    column="invoke_time"    />
        <result property="duration"    column="duration"    />
        <result property="planName"    column="plan_name"    />
        <result property="planCode"    column="plan_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="taskCode"    column="task_code"    />
        <result property="status"    column="status"    />
        <result property="invokeDate"    column="invoke_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <resultMap id="ScheduAlgorithmInvokeRecordVOesult" type="com.mes.smartdispath.domain.vo.ScheduAlgorithmInvokeRecordVO" extends="ScheduAlgorithmInvokeRecordResult">
        <result property="avgDuration"    column="avg_duration"    />
        <result property="planCount"    column="plan_count"    />
        <result property="taskCount"    column="task_count"    />
    </resultMap>

    <sql id="selectScheduAlgorithmInvokeRecordVo">
        select id, algorithm_id, algorithm_code, algorithm_name, invoke_time, duration, plan_name, plan_code, task_name, task_code, status, invoke_date, create_by, create_time, update_by, update_time, is_deleted, tenant_id from tb_schedu_algorithm_invoke_record
    </sql>

    <select id="selectCollectScheduAlgorithmInvokeRecordList" parameterType="com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord" resultMap="ScheduAlgorithmInvokeRecordVOesult">
        SELECT
            a.algorithm_id,
            a.algorithm_code,
            a.algorithm_name,
            avg(a.duration) AS avg_duration,
            a.invoke_date,
            COUNT(a.plan_code) AS plan_count,
            COUNT(a.task_code) AS task_count,
            MAX(a.status) AS status,
            MAX(a.invoke_time) AS invoke_time
        FROM tb_schedu_algorithm_invoke_record a
        WHERE a.is_deleted = 'N'
            <if test="null != algorithmCode and algorithmCode != ''">
                and a.algorithm_code = #{algorithmCode}
            </if>
            <if test="null != algorithmName and algorithmName != ''">
                and a.algorithm_name like concat('%', #{algorithmName}, '%')
            </if>
            <if test="null != invokeDate and invokeDate != ''">
                and a.invoke_date = #{invokeDate}
            </if>
        GROUP BY a.invoke_date, a.algorithm_id, a.algorithm_code, a.algorithm_name, a.task_code, a.task_name
        ORDER BY a.invoke_date, a.algorithm_id, a.task_code
    </select>

    <select id="selectScheduAlgorithmInvokeRecordList" parameterType="com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord" resultMap="ScheduAlgorithmInvokeRecordResult">
        <include refid="selectScheduAlgorithmInvokeRecordVo"/>
        <where>  
            <if test="algorithmId != null  and algorithmId != ''"> and algorithm_id = #{algorithmId}</if>
            <if test="algorithmCode != null  and algorithmCode != ''"> and algorithm_code = #{algorithmCode}</if>
            <if test="algorithmName != null  and algorithmName != ''"> and algorithm_name like concat('%', #{algorithmName}, '%')</if>
            <if test="invokeTime != null  and invokeTime != ''"> and invoke_time = #{invokeTime}</if>
            <if test="duration != null  and duration != ''"> and duration = #{duration}</if>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="planCode != null  and planCode != ''"> and plan_code = #{planCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="invokeDate != null  and invokeDate != ''"> and invoke_date = #{invokeDate}</if>
            <if test="isDeleted != null  and isDeleted != ''"> and is_deleted = #{isDeleted}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
        </where>
        ORDER BY invoke_time desc
    </select>
    
    <select id="selectScheduAlgorithmInvokeRecordById" parameterType="String" resultMap="ScheduAlgorithmInvokeRecordResult">
        <include refid="selectScheduAlgorithmInvokeRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduAlgorithmInvokeRecord" parameterType="com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord">
        insert into tb_schedu_algorithm_invoke_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="algorithmId != null">algorithm_id,</if>
            <if test="algorithmCode != null and algorithmCode != ''">algorithm_code,</if>
            <if test="algorithmName != null and algorithmName != ''">algorithm_name,</if>
            <if test="invokeTime != null and invokeTime != ''">invoke_time,</if>
            <if test="duration != null and duration != ''">duration,</if>
            <if test="planName != null">plan_name,</if>
            <if test="planCode != null">plan_code,</if>
            <if test="taskName != null">task_name,</if>
            <if test="taskCode != null">task_code,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="invokeDate != null and invokeDate != ''">invoke_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null and createTime != ''">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="tenantId != null">tenant_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="algorithmId != null">#{algorithmId},</if>
            <if test="algorithmCode != null and algorithmCode != ''">#{algorithmCode},</if>
            <if test="algorithmName != null and algorithmName != ''">#{algorithmName},</if>
            <if test="invokeTime != null and invokeTime != ''">#{invokeTime},</if>
            <if test="duration != null and duration != ''">#{duration},</if>
            <if test="planName != null">#{planName},</if>
            <if test="planCode != null">#{planCode},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="taskCode != null">#{taskCode},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="invokeDate != null and invokeDate != ''">#{invokeDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null and createTime != ''">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="tenantId != null">#{tenantId},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduAlgorithmInvokeRecord" parameterType="java.util.List">
        insert into tb_schedu_algorithm_invoke_record (
            algorithm_id,
            algorithm_code,
            algorithm_name,
            invoke_time,
            duration,
            plan_name,
            plan_code,
            task_name,
            task_code,
            status,
            invoke_date,
            create_by,
            create_time,
            update_by,
            update_time,
            is_deleted,
            tenant_id
        ) values
        <foreach collection="list" item="item" separator=",">
        (
            #{item.algorithmId},
            #{item.algorithmCode},
            #{item.algorithmName},
            #{item.invokeTime},
            #{item.duration},
            #{item.planName},
            #{item.planCode},
            #{item.taskName},
            #{item.taskCode},
            #{item.status},
            #{item.invokeDate},
            #{item.createBy},
            #{item.createTime},
            #{item.updateBy},
            #{item.updateTime},
            'N',
            #{item.tenantId}
        )
        </foreach>
    </insert>

    <update id="updateScheduAlgorithmInvokeRecord" parameterType="com.mes.smartdispath.domain.ScheduAlgorithmInvokeRecord">
        update tb_schedu_algorithm_invoke_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="algorithmId != null">algorithm_id = #{algorithmId},</if>
            <if test="algorithmCode != null and algorithmCode != ''">algorithm_code = #{algorithmCode},</if>
            <if test="algorithmName != null and algorithmName != ''">algorithm_name = #{algorithmName},</if>
            <if test="invokeTime != null and invokeTime != ''">invoke_time = #{invokeTime},</if>
            <if test="duration != null and duration != ''">duration = #{duration},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="planCode != null">plan_code = #{planCode},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="taskCode != null">task_code = #{taskCode},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="invokeDate != null and invokeDate != ''">invoke_date = #{invokeDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduAlgorithmInvokeRecord" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_algorithm_invoke_record
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.algorithmId != null">
                    algorithm_id = #{item.algorithmId},
                </if>
                <if test="item.algorithmCode != null and item.algorithmCode != ''">
                    algorithm_code = #{item.algorithmCode},
                </if>
                <if test="item.algorithmName != null and item.algorithmName != ''">
                    algorithm_name = #{item.algorithmName},
                </if>
                <if test="item.invokeTime != null and item.invokeTime != ''">
                    invoke_time = #{item.invokeTime},
                </if>
                <if test="item.duration != null and item.duration != ''">
                    duration = #{item.duration},
                </if>
                <if test="item.planName != null">
                    plan_name = #{item.planName},
                </if>
                <if test="item.planCode != null">
                    plan_code = #{item.planCode},
                </if>
                <if test="item.taskName != null">
                    task_name = #{item.taskName},
                </if>
                <if test="item.taskCode != null">
                    task_code = #{item.taskCode},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status},
                </if>
                <if test="item.invokeDate != null and item.invokeDate != ''">
                    invoke_date = #{item.invokeDate},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.createTime != null and item.createTime != ''">
                    create_time = #{item.createTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted},
                </if>
                <if test="item.tenantId != null">
                    tenant_id = #{item.tenantId},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduAlgorithmInvokeRecordById" parameterType="String">
        delete from tb_schedu_algorithm_invoke_record where id = #{id}
    </delete>

    <delete id="deleteScheduAlgorithmInvokeRecordByIds" parameterType="String">
        delete from tb_schedu_algorithm_invoke_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>