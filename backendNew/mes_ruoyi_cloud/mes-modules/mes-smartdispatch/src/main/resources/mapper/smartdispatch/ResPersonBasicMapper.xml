<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ResPersonBasicMapper">

    <resultMap type="com.mes.smartdispath.domain.ResPersonBasic" id="ResPersonBasicResult">
            <result property="personId" column="person_id"/>
            <result property="userId" column="user_id"/>
            <result property="monitoringElement" column="monitoring_element"/>
            <result property="personName" column="person_name"/>
            <result property="sex" column="sex"/>
            <result property="idCard" column="id_card"/>
            <result property="phoneNumber" column="phone_number"/>
            <result property="personType" column="person_type"/>
            <result property="deptId" column="dept_id"/>
            <result property="status" column="status"/>
            <result property="hasDrivingLicense" column="has_driving_license"/>
            <result property="abilityLevel" column="ability_level"/>
            <result property="dataSource" column="data_source"/>
            <result property="tenantId" column="tenant_id"/>
            <result property="createdBy" column="created_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updatedBy" column="updated_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="ResPersonBasicVOResult" type="com.mes.smartdispath.domain.vo.ResPersonBasicVO" extends="ResPersonBasicResult">
        <result property="deptName" column="dept_name"/>
        <result property="statusName" column="status_name"/>
    </resultMap>

    <resultMap id="MaintainPersonStaticVO" type="com.mes.smartdispath.domain.vo.MaintainPersonCountVO">
        <result property="totalCount" column="total_count"/>
        <result property="onDutyCount" column="on_duty_count"/>
        <result property="leaveCount" column="leave_count"/>
    </resultMap>

    <sql id="selectResPersonBasicVo">
        select person_id, user_id, person_name, sex, id_card, phone_number, person_type, dept_id, status, has_driving_license, ability_level, data_source, tenant_id, created_by, create_time, updated_by, update_time
        from tb_res_person_basic
    </sql>

    <select id="selectMaintainPersonStatic" resultMap="MaintainPersonStaticVO">
        select count(*) as total_count,
               sum(case when status = #{onDutyStatus} then 1 else 0 end) as on_duty_count,
               sum(case when status = #{leaveStatus} then 1 else 0 end) as leave_count
        from tb_res_person_basic
        where person_type = #{personType}
        and status in (#{onDutyStatus}, #{leaveStatus})
        <if test="null != businessType and businessType != ''">
            and monitoring_element = #{businessType}
        </if>
    </select>

    <select id="selectMaintenancePersonCount" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultType="java.lang.Integer">
        select count(*) from tb_res_person_basic
        where dept_id in
        (
        select distinct operation_unit from tb_res_site
            <where>
                <if test="province != null  and province != ''"> and province = #{province}</if>
                <if test="city != null  and city != ''"> and city = #{city}</if>
                <if test="businessType != null  and businessType != ''"> and monitoring_element = #{businessType}</if>
                <if test="maintainUnitCode != null  and maintainUnitCode != ''"> and operation_unit = #{maintainUnitCode}</if>
            </where>
        )
        <if test="null != maintainPersonType">
            and person_type = #{maintainPersonType}
        </if>
    </select>

    <select id="selectResPersonBasicList" parameterType="com.mes.smartdispath.domain.dto.ResPersonBasicQueryDTO"
            resultMap="ResPersonBasicVOResult">
        select
            t1.person_id, t1.user_id, t1.person_name, t1.sex, t1.id_card, t1.phone_number,
            t1.person_type, t1.dept_id, t1.status, t1.has_driving_license, t1.ability_level, t1.data_source,
            t1.tenant_id, t1.created_by, t1.create_time, t1.updated_by, t1.update_time,
            t2.dept_name, t3.dict_value as status_name
        from tb_res_person_basic t1
        left join tb_res_dept t2 on t1.dept_id = t2.dept_id
        left join tb_sys_dict t3 on t3.class_code = 'person_status' AND t3.state = 'A' AND t1.status = t3.dict_code
        <where>
            <if test="userId != null ">
                and t1.user_id = #{userId}
            </if>
            <if test="monitoringElement != null ">
                and t1.monitoring_element = #{monitoringElement}
            </if>
            <if test="personName != null  and personName != ''">
                and t1.person_name like concat('%', #{personName}, '%')
            </if>
            <if test="sex != null  and sex != ''">
                and t1.sex = #{sex}
            </if>
            <if test="idCard != null  and idCard != ''">
                and t1.id_card = #{idCard}
            </if>
            <if test="phoneNumber != null  and phoneNumber != ''">
                and t1.phone_number = #{phoneNumber}
            </if>
            <if test="personType != null  and personType != ''">
                and t1.person_type = #{personType}
            </if>
            <if test="deptId != null  and deptId != '' and null == deptIdArr">
                and t1.dept_id = #{deptId}
            </if>
            <if test="deptIdArr != null  and deptIdArr != ''">
                and t1.dept_id in
                    <foreach collection="deptIdArr" item="item" separator=",">
                        #{item}
                    </foreach>
            </if>
            <if test="status != null  and status != '' and null == statusArr">
                and t1.status = #{status}
            </if>
            <if test="statusArr != null  and statusArr != ''">
                and t1.status in
                    <foreach collection="statusArr" item="item" separator=",">
                        #{item}
                    </foreach>
            </if>
            <if test="hasDrivingLicense != null  and hasDrivingLicense != ''">
                and t1.has_driving_license = #{hasDrivingLicense}
            </if>
            <if test="abilityLevel != null  and abilityLevel != ''">
                and t1.ability_level = #{abilityLevel}
            </if>
            <if test="dataSource != null  and dataSource != ''">
                and t1.data_source = #{dataSource}
            </if>
            <if test="tenantId != null  and tenantId != ''">
                and t1.tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <select id="selectResPersonBasicByPersonId" parameterType="String"
            resultMap="ResPersonBasicResult">
            <include refid="selectResPersonBasicVo"/>
            where person_id = #{personId}
    </select>

    <insert id="insertResPersonBasic" parameterType="com.mes.smartdispath.domain.ResPersonBasic">
        insert into tb_res_person_basic
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="personId != null">person_id,
                    </if>
                    <if test="userId != null">user_id,
                    </if>
                    <if test="monitoringElement != null">monitoring_element,
                    </if>
                    <if test="personName != null and personName != ''">person_name,
                    </if>
                    <if test="sex != null">sex,
                    </if>
                    <if test="idCard != null">id_card,
                    </if>
                    <if test="phoneNumber != null">phone_number,
                    </if>
                    <if test="personType != null">person_type,
                    </if>
                    <if test="deptId != null">dept_id,
                    </if>
                    <if test="status != null and status != ''">status,
                    </if>
                    <if test="hasDrivingLicense != null">has_driving_license,
                    </if>
                    <if test="abilityLevel != null">ability_level,
                    </if>
                    <if test="dataSource != null">data_source,
                    </if>
                    <if test="tenantId != null and tenantId != ''">tenant_id,
                    </if>
                    <if test="createdBy != null">created_by,
                    </if>
                    <if test="createTime != null">create_time,
                    </if>
                    <if test="updatedBy != null">updated_by,
                    </if>
                    <if test="updateTime != null">update_time,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="personId != null">#{personId},
                    </if>
                    <if test="userId != null">#{userId},
                    </if>
                    <if test="monitoringElement != null">#{monitoringElement},
                    </if>
                    <if test="personName != null and personName != ''">#{personName},
                    </if>
                    <if test="sex != null">#{sex},
                    </if>
                    <if test="idCard != null">#{idCard},
                    </if>
                    <if test="phoneNumber != null">#{phoneNumber},
                    </if>
                    <if test="personType != null">#{personType},
                    </if>
                    <if test="deptId != null">#{deptId},
                    </if>
                    <if test="status != null and status != ''">#{status},
                    </if>
                    <if test="hasDrivingLicense != null">#{hasDrivingLicense},
                    </if>
                    <if test="abilityLevel != null">#{abilityLevel},
                    </if>
                    <if test="dataSource != null">#{dataSource},
                    </if>
                    <if test="tenantId != null and tenantId != ''">#{tenantId},
                    </if>
                    <if test="createdBy != null">#{createdBy},
                    </if>
                    <if test="createTime != null">#{createTime},
                    </if>
                    <if test="updatedBy != null">#{updatedBy},
                    </if>
                    <if test="updateTime != null">#{updateTime},
                    </if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertResPersonBasic" parameterType="java.util.List">
        insert into tb_res_person_basic (
                person_id,
                user_id,
                monitoring_element,
                person_name,
                sex,
                id_card,
                phone_number,
                person_type,
                dept_id,
                status,
                has_driving_license,
                ability_level,
                data_source,
                tenant_id,
                created_by,
                create_time,
                updated_by,
                update_time
        ) values
        <foreach collection="list" item="item" separator=",">
        (
                #{item.personId},
                #{item.userId},
                #{item.monitoringElement},
                #{item.personName},
                #{item.sex},
                #{item.idCard},
                #{item.phoneNumber},
                #{item.personType},
                #{item.deptId},
                #{item.status},
                #{item.hasDrivingLicense},
                #{item.abilityLevel},
                #{item.dataSource},
                #{item.tenantId},
                #{item.createdBy},
                #{item.createTime},
                #{item.updatedBy},
                #{item.updateTime}
        )
        </foreach>
    </insert>

    <update id="updateResPersonBasic" parameterType="com.mes.smartdispath.domain.ResPersonBasic">
        update tb_res_person_basic
        <trim prefix="SET" suffixOverrides=",">
                    <if test="userId != null">user_id =
                        #{userId},
                    </if>
                    <if test="monitoringElement != null">monitoring_element =
                        #{monitoringElement},
                    </if>
                    <if test="personName != null and personName != ''">person_name =
                        #{personName},
                    </if>
                    <if test="sex != null">sex =
                        #{sex},
                    </if>
                    <if test="idCard != null">id_card =
                        #{idCard},
                    </if>
                    <if test="phoneNumber != null">phone_number =
                        #{phoneNumber},
                    </if>
                    <if test="personType != null">person_type =
                        #{personType},
                    </if>
                    <if test="deptId != null">dept_id =
                        #{deptId},
                    </if>
                    <if test="status != null and status != ''">status =
                        #{status},
                    </if>
                    <if test="hasDrivingLicense != null">has_driving_license =
                        #{hasDrivingLicense},
                    </if>
                    <if test="abilityLevel != null">ability_level =
                        #{abilityLevel},
                    </if>
                    <if test="dataSource != null">data_source =
                        #{dataSource},
                    </if>
                    <if test="tenantId != null and tenantId != ''">tenant_id =
                        #{tenantId},
                    </if>
                    <if test="createdBy != null">created_by =
                        #{createdBy},
                    </if>
                    <if test="createTime != null">create_time =
                        #{createTime},
                    </if>
                    <if test="updatedBy != null">updated_by =
                        #{updatedBy},
                    </if>
                    <if test="updateTime != null">update_time =
                        #{updateTime},
                    </if>
        </trim>
        where person_id = #{personId}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateResPersonBasic" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_res_person_basic
            <trim prefix="SET" suffixOverrides=",">
                        <if test="item.userId != null">
                            user_id = #{item.userId},
                        </if>
                        <if test="monitoringElement != null">monitoring_element =
                            #{monitoringElement},
                        </if>
                        <if test="item.personName != null and item.personName != ''">
                            person_name = #{item.personName},
                        </if>
                        <if test="item.sex != null">
                            sex = #{item.sex},
                        </if>
                        <if test="item.idCard != null">
                            id_card = #{item.idCard},
                        </if>
                        <if test="item.phoneNumber != null">
                            phone_number = #{item.phoneNumber},
                        </if>
                        <if test="item.personType != null">
                            person_type = #{item.personType},
                        </if>
                        <if test="item.deptId != null">
                            dept_id = #{item.deptId},
                        </if>
                        <if test="item.status != null and item.status != ''">
                            status = #{item.status},
                        </if>
                        <if test="item.hasDrivingLicense != null">
                            has_driving_license = #{item.hasDrivingLicense},
                        </if>
                        <if test="item.abilityLevel != null">
                            ability_level = #{item.abilityLevel},
                        </if>
                        <if test="item.dataSource != null">
                            data_source = #{item.dataSource},
                        </if>
                        <if test="item.tenantId != null and item.tenantId != ''">
                            tenant_id = #{item.tenantId},
                        </if>
                        <if test="item.createdBy != null">
                            created_by = #{item.createdBy},
                        </if>
                        <if test="item.createTime != null">
                            create_time = #{item.createTime},
                        </if>
                        <if test="item.updatedBy != null">
                            updated_by = #{item.updatedBy},
                        </if>
                        <if test="item.updateTime != null">
                            update_time = #{item.updateTime},
                        </if>
            </trim>
            where person_id = #{item.personId}
        </foreach>
    </update>

    <delete id="deleteResPersonBasicByPersonId" parameterType="String">
        delete
        from tb_res_person_basic where person_id = #{personId}
    </delete>

    <delete id="deleteResPersonBasicByPersonIds" parameterType="String">
        delete from tb_res_person_basic where person_id in
        <foreach item="personId" collection="array" open="(" separator="," close=")">
            #{personId}
        </foreach>
    </delete>
</mapper>