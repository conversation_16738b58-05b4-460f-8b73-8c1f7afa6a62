<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduTaskInfoMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduTaskInfo" id="ScheduTaskInfoResult">
        <result property="id"    column="id"    />
        <result property="businessType"    column="business_type"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="creatMethod"    column="creat_method"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="dispatchedTime"    column="dispatched_time"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="taskDesc"    column="task_desc"    />
        <result property="backTime"    column="back_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="approvalStatus"    column="approval_status"    />
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.ScheduTaskInfoVO" id="ScheduTaskInfoVOResult" extends="ScheduTaskInfoResult">
        <result property="businessTypeName"    column="business_type_name"    />
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.TaskStaticVO" id="TaskStaticResult">
        <result property="taskCount"    column="task_count"    />
        <result property="completeTaskCount"    column="complete_task_count"    />
        <result property="unCompleteTaskCount"    column="un_complete_task_count"    />
        <result property="inProgressTaskCount"    column="in_progress_task_count"    />
        <result property="notStartedTaskCount"    column="not_started_task_count"    />
        <result property="averageTime"    column="average_time"    />
    </resultMap>

    <resultMap id="SiteTaskListVOResultMap" type="com.mes.smartdispath.domain.vo.SiteTaskListVO">
        <result property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="activityName" column="activity_name"/>
        <result property="dispatchedTime" column="dispatched_time"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="executorName" column="executor_name"/>
        <result property="maintainUnitName" column="maintain_unit_name"/>
        <result property="siteName" column="site_name"/>
    </resultMap>

    <resultMap id="TaskCountStaticResult" type="com.mes.smartdispath.domain.vo.TaskCountStaticVO">
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="taskCount" column="task_count"/>
    </resultMap>
    
    <resultMap id="ChartBasicVOResult" type="com.mes.smartdispath.domain.vo.ChartBasicVO">
        <result property="dt" column="dt"/>
        <result property="value" column="value"/>
    </resultMap>

    <sql id="selectScheduTaskInfoVo">
        select id, business_type, task_code, task_name, creat_method, task_status, dispatched_time, start_time, end_time, task_desc, back_time, create_time, create_by, update_time, update_by, is_deleted, tenant_id, approval_status from tb_schedu_task_info
    </sql>

    <select id="selectMonthPersonAvgPlanCount" resultMap="ChartBasicVOResult">
        WITH MonthlyStats AS (
            SELECT
                TO_CHAR(t.start_time, 'MM') AS month_key,
                TO_CHAR(t.start_time, 'YYYY"年"MM"月"') AS month_name,
                COUNT(DISTINCT r.id) AS total_plans,  -- 当月计划总数
                COUNT(DISTINCT e.executor_name) AS total_executors,  -- 当月执行人数
                EXTRACT(DAY FROM LAST_DAY(t.start_time)) AS days_in_month  -- 当月天数
            FROM CNEMC_ENVIRONMENT."tb_schedu_task_info" t
                     JOIN CNEMC_ENVIRONMENT."tb_schedu_task_plan_rel" r
                          ON t.id = r.task_id
                              AND r.status = 'A'  -- 有效关系
                     JOIN CNEMC_ENVIRONMENT."tb_schedu_task_extend" e
                          ON t.id = e.task_id
                              AND e.is_deleted = 'N'  -- 有效扩展
                              AND e.executor_name IS NOT NULL  -- 有执行人
            WHERE 1=1
              AND t.is_deleted = 'N'  -- 未删除任务
              AND EXTRACT(YEAR FROM t.start_time) = EXTRACT(YEAR FROM SYSDATE)  -- 当年
              AND t.approval_status = 'approved'  -- 已批准任务
            GROUP BY
                TO_CHAR(t.start_time, 'MM'),
                TO_CHAR(t.start_time, 'YYYY"年"MM"月"'),
                EXTRACT(DAY FROM LAST_DAY(t.start_time))
        )
        SELECT
            month_key as dt,
            ROUND(
                    total_plans / NULLIF(total_executors * days_in_month, 0),
                    1
            ) AS value -- 单人平均每天计划数 = 计划总数 / (执行人数 × 当月天数)
        FROM MonthlyStats
        ORDER BY month_key
    </select>
    
    <select id="selectMonthPerTaskAvgPlanCount" resultMap="ChartBasicVOResult">
        WITH MonthlyStats AS (
            SELECT
                TO_CHAR(t.start_time, 'MM') AS month_num,
                TO_CHAR(t.start_time, 'YYYY"年"MM"月"') AS month_name,
                COUNT(DISTINCT t.id) AS task_count,
                COUNT(r.id) AS plan_count
            FROM CNEMC_ENVIRONMENT."tb_schedu_task_info" t
                     JOIN CNEMC_ENVIRONMENT."tb_schedu_task_plan_rel" r
                          ON t.id = r.task_id
                              AND r.status = 'A'  -- 只关联有效关系
            WHERE 1=1
              AND t.is_deleted = 'N'  -- 未删除的任务
              AND t.task_status = '4'  -- 已完成的任务状态
              AND EXTRACT(YEAR FROM t.start_time) = EXTRACT(YEAR FROM SYSDATE)  -- 当年任务
              AND t.approval_status = 'approved'  -- 已批准的任务
            GROUP BY
                TO_CHAR(t.start_time, 'MM'),
                TO_CHAR(t.start_time, 'YYYY"年"MM"月"')
        )
        SELECT
            month_num as dt,
            ROUND(plan_count / task_count, 1) AS value  -- 平均每个任务的计划数
        FROM MonthlyStats
        ORDER BY month_num
    </select>

    <select id="selectRegionTaskCountStatic" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultMap="TaskCountStaticResult">
        SELECT
            p.province_code as code,
            p.province_name as name,
            COUNT(DISTINCT t.id) as task_count
        FROM tb_schedu_task_info t
            JOIN tb_schedu_task_plan_rel r ON t.id = r.task_id AND r.status = 'A'
            JOIN tb_schedu_plan_info p ON r.plan_id = p.id AND p.status = 'A'
        WHERE t.is_deleted = 'N'
            <if test="businessType != null  and businessType != ''">and t.business_type = #{businessType}</if>
            <if test="startTime != null  and startTime != ''">and t.start_time >= #{startTime}</if>
            <if test="endTime != null  and endTime != ''">and t.start_time <![CDATA[<=]]> #{endTime}</if>
        GROUP BY
            p.province_code,
            p.province_name
        ORDER BY task_count desc
    </select>

    <select id="selectMaintainUnitTaskCountStatic" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultMap="TaskCountStaticResult">
        SELECT
            COALESCE(e.maintain_unit_code, '-1') AS code,
            COALESCE(e.maintain_unit_name, '未分配运维单位') AS name,
            COUNT(DISTINCT t.id) AS task_count
            FROM tb_schedu_task_info t
            LEFT JOIN tb_schedu_task_extend e ON t.id = e.task_id AND e.is_deleted = 'N'
            WHERE t.is_deleted = 'N'
                <if test="businessType != null  and businessType != ''">and t.business_type = #{businessType}</if>
                <if test="startTime != null  and startTime != ''">and t.start_time >= #{startTime}</if>
                <if test="endTime != null  and endTime != ''">and t.start_time <![CDATA[<=]]> #{endTime}</if>
            GROUP BY
                e.maintain_unit_code,
                e.maintain_unit_name
            ORDER BY task_count DESC
    </select>

    <select id="selectPeriodTaskStatic" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultMap="TaskStaticResult">
        select COUNT(DISTINCT a.id) as task_count,
               COUNT(DISTINCT CASE WHEN a.task_status = 4 THEN a.id END) AS complete_task_count,
               COUNT(DISTINCT CASE WHEN a.task_status = 3 THEN a.id END) AS in_progress_task_count,
               COUNT(DISTINCT CASE WHEN a.task_status IN (1, 2) THEN a.id END) AS not_started_task_count
        from tb_schedu_task_info a
                 LEFT JOIN tb_schedu_task_plan_rel b ON a.id = b.task_id
                 LEFT JOIN tb_schedu_plan_info c ON b.plan_id = c.id
        <where>
            <if test="siteId != null  and siteId != ''"> and c.site_id = #{siteId}</if>
            <if test="endDate != null  and endDate != ''"> and a.start_time <![CDATA[<=]]> #{endDate}</if>
            <if test="startDate != null  and startDate != ''"> and a.start_time >= #{startDate}</if>
        </where>
    </select>

    <select id="selectCollectTaskList" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultMap="SiteTaskListVOResultMap">
        SELECT
            b.id as task_id,
            b.task_name,
            b.dispatched_time,
            b.start_time,
            b.end_time,
            COALESCE(LISTAGG(DISTINCT d.activity_type_name, '、') WITHIN GROUP (ORDER BY d.activity_type_name), '') AS activity_name,
            COALESCE(LISTAGG(DISTINCT e.executor_name, '、') WITHIN GROUP (ORDER BY e.executor_name), '') AS executor_name,
            COALESCE(LISTAGG(DISTINCT e.maintain_unit_name, '、') WITHIN GROUP (ORDER BY e.maintain_unit_name), '') AS maintain_unit_name,
            COALESCE(LISTAGG(DISTINCT c.site_name, '、') WITHIN GROUP (ORDER BY c.site_name), '') AS site_name
        FROM
            tb_schedu_task_info b
                LEFT JOIN tb_schedu_task_plan_rel a ON b.id = a.task_id
                LEFT JOIN tb_schedu_plan_info c ON a.plan_id = c.id
                LEFT JOIN tb_schedu_monitor_activity_info d ON c.activity_type = d.activity_type_code
                LEFT JOIN tb_schedu_task_extend e ON b.id = e.task_id
        <where>
            <if test="siteId != null  and siteId != ''"> and c.site_id = #{siteId}</if>
            <if test="null != taskStausArr">
                AND b.task_status IN
                <foreach collection="taskStausArr" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="endDate != null  and endDate != ''"> and b.start_time <![CDATA[<=]]> #{endDate}</if>
            <if test="startDate != null  and startDate != ''"> and b.start_time >= #{startDate}</if>
        </where>
        GROUP BY
            b.id,
            b.task_name,
            b.dispatched_time,
            b.start_time,
            b.end_time
        ORDER BY b.start_time DESC
    </select>

    <select id="selectMasterStationTaskStatic" parameterType="com.mes.smartdispath.domain.dto.TaskStaticQueryDTO" resultMap="TaskStaticResult">
        select COUNT(*) as task_count,
            COUNT(CASE WHEN status = 4 THEN 1 END) AS complete_task_count,
            COUNT(CASE WHEN status IN (1, 2, 3) THEN 1 END) AS un_complete_task_count,
            AVG(diff_time) as average_time
        from (
            select distinct a.id, a.task_status as status, TIMESTAMPDIFF(SECOND, a.start_time, a.end_time) AS diff_time
               from tb_schedu_task_info a
                        left join tb_schedu_task_plan_rel b on a.id = b.task_id
                        left join tb_schedu_plan_info c on b.plan_id = c.id
                        left join tb_schedu_task_extend d on a.id = d.task_id
               <where>
                   <if test="businessType != null  and businessType != ''"> and a.business_type = #{businessType}</if>
                   <if test="startDate != null  and startDate != ''"> and a.start_time >= #{startDate}</if>
                   <if test="endDate != null  and endDate != ''"> and a.start_time <![CDATA[<=]]>  #{endDate}</if>
                   <if test="province != null  and province != ''"> and c.province_code = #{province}</if>
                   <if test="city != null  and city != ''"> and c.city_code = #{city}</if>
                   <if test="maintainUnitCode != null  and maintainUnitCode != ''"> and d.maintain_unit_code = #{maintainUnitCode}</if>
               </where>
       )
    </select>

    <select id="selectScheduTaskInfoList" parameterType="com.mes.smartdispath.domain.dto.ScheduTaskInfoQueryDTO" resultMap="ScheduTaskInfoVOResult">
        select
            t1.id, t1.business_type, t1.task_code, t1.task_name, t1.creat_method, t1.task_status, t1.dispatched_time, t1.start_time,
            t1.end_time, t1.task_desc, t1.back_time, t1.create_time, t1.create_by, t1.update_time, t1.update_by, t1.is_deleted, t1.tenant_id, t1.approval_status,
            t2.dict_value as business_type_name
        from tb_schedu_task_info t1
        left join tb_sys_dict t2 on t1.business_type = t2.dict_code and t2.class_code = #{businessTypeDictClassCode} and t2.state = 'A'
        where
            t1.is_deleted = 'N'
            <if test="null != idArr">
                and t1.id in
                    <foreach collection="idArr" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="businessType != null  and businessType != ''"> and t1.business_type = #{businessType}</if>
            <if test="taskCode != null  and taskCode != ''"> and t1.task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and t1.task_name like concat('%', #{taskName}, '%')</if>
            <if test="creatMethod != null  and creatMethod != ''"> and t1.creat_method = #{creatMethod}</if>
            <if test="taskStatus != null  and taskStatus != '' and null == taskStatusArr"> and t1.task_status = #{taskStatus}</if>
            <if test="taskStatusArr != null  and taskStatusArr != ''">
                and t1.task_status in
                    <foreach collection="taskStatusArr" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="dispatchedTime != null  and dispatchedTime != ''"> and t1.dispatched_time = #{dispatchedTime}</if>
            <if test="endDispatchedTime != null  and endDispatchedTime != ''"> and t1.dispatched_time <![CDATA[<=]]> #{endDispatchedTime}</if>
            <if test="startDispatchedTime != null  and startDispatchedTime != ''"> and t1.dispatched_time >= #{startDispatchedTime}</if>
            <if test="startTime != null  and startTime != ''"> and t1.start_time = #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and t1.end_time = #{endTime}</if>
            <if test="taskDesc != null  and taskDesc != ''"> and t1.task_desc = #{taskDesc}</if>
            <if test="backTime != null  and backTime != ''"> and t1.back_time = #{backTime}</if>
            <if test="tenantId != null  and tenantId != ''"> and t1.tenant_id = #{tenantId}</if>
            <if test="approvalStatus != null  and approvalStatus != '' and null == approvalStatusArr"> and t1.approval_status = #{approvalStatus}</if>
            <if test="approvalStatusArr != null  and approvalStatusArr != ''">
                and t1.approval_status in
                    <foreach collection="approvalStatusArr" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="startTaskTime != null  and startTaskTime != ''"> and t1.start_time >= #{startTaskTime}</if>
            <if test="endTaskTime != null  and endTaskTime != ''"> and t1.start_time <![CDATA[<=]]> #{endTaskTime}</if>
    </select>
    
    <select id="selectScheduTaskInfoById" parameterType="String" resultMap="ScheduTaskInfoVOResult">
        select
            t1.id, t1.business_type, t1.task_code, t1.task_name, t1.creat_method, t1.task_status, t1.dispatched_time, t1.start_time,
            t1.end_time, t1.task_desc, t1.back_time, t1.create_time, t1.create_by, t1.update_time, t1.update_by, t1.is_deleted, t1.tenant_id, t1.approval_status,
            t2.dict_value as business_type_name
        from tb_schedu_task_info t1
        where t1.id = #{id}
    </select>

    <select id="selectScheduTaskInfoByTaskCode" parameterType="String" resultMap="ScheduTaskInfoVOResult">
        select
            t1.id, t1.business_type, t1.task_code, t1.task_name, t1.creat_method, t1.task_status, t1.dispatched_time, t1.start_time,
            t1.end_time, t1.task_desc, t1.back_time, t1.create_time, t1.create_by, t1.update_time, t1.update_by, t1.is_deleted, t1.tenant_id, t1.approval_status,
            t2.dict_value as business_type_name
        from tb_schedu_task_info t1
        where t1.id = #{id}
    </select>

    <insert id="insertScheduTaskInfo" parameterType="com.mes.smartdispath.domain.ScheduTaskInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_schedu_task_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="creatMethod != null">creat_method,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="dispatchedTime != null">dispatched_time,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="taskDesc != null">task_desc,</if>
            <if test="backTime != null">back_time,</if>
            <if test="createTime != null and createTime != ''">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="approvalStatus != null">approval_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="creatMethod != null">#{creatMethod},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="dispatchedTime != null">#{dispatchedTime},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="taskDesc != null">#{taskDesc},</if>
            <if test="backTime != null">#{backTime},</if>
            <if test="createTime != null and createTime != ''">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="tenantId != null">#{tenantId},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduTaskInfo" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into tb_schedu_task_info (
            business_type,
            task_code,
            task_name,
            creat_method,
            task_status,
            dispatched_time,
            start_time,
            end_time,
            task_desc,
            back_time,
            create_time,
            create_by,
            update_time,
            update_by,
            is_deleted,
            tenant_id,
            approval_status
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.businessType},
                #{item.taskCode},
                #{item.taskName},
                #{item.creatMethod},
                #{item.taskStatus},
                #{item.dispatchedTime},
                #{item.startTime},
                #{item.endTime},
                #{item.taskDesc},
                #{item.backTime},
                now(),
                #{item.createBy},
                now(),
                #{item.updateBy},
                'N',
                #{item.tenantId},
                #{item.approvalStatus}
            )
        </foreach>
    </insert>

    <update id="updateScheduTaskInfo" parameterType="com.mes.smartdispath.domain.ScheduTaskInfo">
        update tb_schedu_task_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="creatMethod != null">creat_method = #{creatMethod},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="dispatchedTime != null">dispatched_time = #{dispatchedTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="taskDesc != null">task_desc = #{taskDesc},</if>
            <if test="backTime != null">back_time = #{backTime},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateScheduTaskInfoByTaskCode" parameterType="com.mes.smartdispath.domain.ScheduTaskInfo">
        update tb_schedu_task_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="creatMethod != null">creat_method = #{creatMethod},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="dispatchedTime != null">dispatched_time = #{dispatchedTime},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="taskDesc != null">task_desc = #{taskDesc},</if>
            <if test="backTime != null">back_time = #{backTime},</if>
            <if test="createTime != null and createTime != ''">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            update_time = now(),
        </trim>
        where task_code = #{taskCode}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduTaskInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_task_info
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.businessType != null">
                    business_type = #{item.businessType},
                </if>
                <if test="item.taskCode != null and item.taskCode != ''">
                    task_code = #{item.taskCode},
                </if>
                <if test="item.taskName != null and item.taskName != ''">
                    task_name = #{item.taskName},
                </if>
                <if test="item.creatMethod != null">
                    creat_method = #{item.creatMethod},
                </if>
                <if test="item.taskStatus != null">
                    task_status = #{item.taskStatus},
                </if>
                <if test="item.dispatchedTime != null">
                    dispatched_time = #{item.dispatchedTime},
                </if>
                <if test="item.startTime != null">
                    start_time = #{item.startTime},
                </if>
                <if test="item.endTime != null">
                    end_time = #{item.endTime},
                </if>
                <if test="item.taskDesc != null">
                    task_desc = #{item.taskDesc},
                </if>
                <if test="item.backTime != null">
                    back_time = #{item.backTime},
                </if>
                <if test="item.createTime != null and item.createTime != ''">
                    create_time = #{item.createTime},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted},
                </if>
                <if test="item.tenantId != null">
                    tenant_id = #{item.tenantId},
                </if>
                <if test="item.approvalStatus != null">
                    approval_status = #{item.approvalStatus},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduTaskInfoByTaskCode" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_task_info
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.businessType != null">
                    business_type = #{item.businessType},
                </if>
                <if test="item.taskName != null and item.taskName != ''">
                    task_name = #{item.taskName},
                </if>
                <if test="item.creatMethod != null">
                    creat_method = #{item.creatMethod},
                </if>
                <if test="item.taskStatus != null">
                    task_status = #{item.taskStatus},
                </if>
                <if test="item.dispatchedTime != null">
                    dispatched_time = #{item.dispatchedTime},
                </if>
                <if test="item.startTime != null">
                    start_time = #{item.startTime},
                </if>
                <if test="item.endTime != null">
                    end_time = #{item.endTime},
                </if>
                <if test="item.taskDesc != null">
                    task_desc = #{item.taskDesc},
                </if>
                <if test="item.backTime != null">
                    back_time = #{item.backTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted},
                </if>
                <if test="item.tenantId != null">
                    tenant_id = #{item.tenantId},
                </if>
                <if test="item.approvalStatus != null">
                    approval_status = #{item.approvalStatus},
                </if>
                update_time = now(),
            </trim>
            where task_code = #{item.taskCode}
        </foreach>
    </update>

    <delete id="deleteScheduTaskInfoById" parameterType="String">
        delete from tb_schedu_task_info where id = #{id}
    </delete>

    <delete id="deleteScheduTaskInfoByIds" parameterType="String">
        delete from tb_schedu_task_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>