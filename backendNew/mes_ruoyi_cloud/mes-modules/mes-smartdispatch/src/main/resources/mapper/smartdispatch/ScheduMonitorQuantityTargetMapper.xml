<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduMonitorQuantityTargetMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduMonitorQuantityTarget" id="ScheduMonitorQuantityTargetResult">
        <result property="id"    column="id"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityCode"    column="city_code"    />
        <result property="cityName"    column="city_name"    />
        <result property="siteName"    column="site_name"    />
        <result property="siteId"    column="site_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="siteType"    column="site_type"    />
        <result property="activityType"    column="activity_type"    />
        <result property="activitySubtype"    column="activity_subtype"    />
        <result property="targetValue"    column="target_value"    />
        <result property="statYear"    column="stat_year"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <resultMap id="ScheduMonitorQuantityTargetVOResult" type="com.mes.smartdispath.domain.vo.ScheduMonitorQuantityTargetVO" extends="ScheduMonitorQuantityTargetResult">
        <result property="siteTypeName"    column="site_type_name"    />
        <result property="hasAutomaticStation"    column="has_automatic_station"    />
        <result property="businessTypeName"    column="business_type_name"    />
        <result property="activityTypeName"    column="activity_type_name"    />
        <result property="activitySubtypeName"    column="activity_subtype_name"    />
    </resultMap>


    <resultMap id="QuantityTargetYearCollectVOResult" type="com.mes.smartdispath.domain.vo.QuantityTargetYearCollectVO">
        <result property="year"    column="year"    />
        <result property="weeklyTarget"    column="weekly_target"    />
        <result property="monthlyTarget"    column="monthly_target"    />
    </resultMap>

    <sql id="selectScheduMonitorQuantityTargetVo">
        select id, province_code, province_name, city_code, city_name, site_name, site_id, business_type, site_type, activity_type, activity_subtype, target_value, stat_year, create_by, create_time, update_by, update_time, status, tenant_id from tb_schedu_monitor_quantity_target
    </sql>

    <select id="selectBizCollectQuantityTarget" resultMap="ScheduMonitorQuantityTargetResult">
        select business_type,
               sum(target_value) as target_value
        from tb_schedu_monitor_quantity_target
        where status = 'A'
            and stat_year = #{statYear}
        group by business_type
    </select>

    <select id="selectQuantityTargetYearCollect" resultMap="QuantityTargetYearCollectVOResult">
        SELECT
            stat_year AS year,
            SUM(CASE WHEN activity_type=#{monthlyActivityType} THEN target_value ELSE 0 END) AS monthly_target,
            SUM(CASE WHEN activity_type=#{weeklyActivityType} THEN target_value ELSE 0 END) AS weekly_target
        FROM tb_schedu_monitor_quantity_target
        WHERE "status" = 'A'
          AND stat_year = #{curMonthYear}
          AND business_type = #{businessType}
          AND activity_type IN (#{monthlyActivityType}, #{weeklyActivityType})
        GROUP BY stat_year
        UNION  ALL
        SELECT
            stat_year AS year,
            SUM(CASE WHEN activity_type=#{monthlyActivityType} THEN target_value ELSE 0 END) AS monthly_target,
            SUM(CASE WHEN activity_type=#{weeklyActivityType} THEN target_value ELSE 0 END) AS weekly_target
        FROM tb_schedu_monitor_quantity_target
        WHERE "status" = 'A'
          AND stat_year = #{lastMonthYear}
          AND business_type = #{businessType}
          AND activity_type IN (#{monthlyActivityType}, #{weeklyActivityType})
        GROUP BY stat_year
    </select>

    <select id="selectScheduMonitorQuantityTargetCount" parameterType="com.mes.smartdispath.domain.dto.ScheduMonitorQuantityTargetQueryDTO" resultType="java.lang.Integer">
        SELECT count(*) from tb_schedu_monitor_quantity_target
        WHERE status = 'A'
            <if test="siteId != null  and siteId != ''"> and site_id = #{siteId}</if>
            <if test="siteIdArr != null  and siteIdArr != ''">
                and site_id in
                <foreach item="item" collection="siteIdArr" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
            <if test="activityType != null  and activityType != ''"> and activity_type = #{activityType}</if>
            <if test="activitySubtype != null  and activitySubtype != ''"> and activity_subtype = #{activitySubtype}</if>
            <if test="statYear != null  and statYear != ''"> and stat_year = #{statYear}</if>
    </select>

    <select id="selectScheduMonitorQuantityTargetList" parameterType="com.mes.smartdispath.domain.dto.ScheduMonitorQuantityTargetQueryDTO" resultMap="ScheduMonitorQuantityTargetVOResult">
        select
            t1.id, t1.province_code, t1.province_name, t1.city_code, t1.city_name,
            t1.site_name, t1.site_id, t1.business_type, t1.site_type, t1.activity_type,
            t1.activity_subtype, t1.target_value, t1.stat_year, t1.create_by, t1.create_time,
            t1.update_by, t1.update_time, t1.status, t1.tenant_id,
            (SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{siteTypeDictClassCode} AND b.state = 'A' AND b.dict_code = t1.site_type AND b.parent_dict_code = t1.business_type) AS site_type_name,
            (SELECT b.dict_value FROM tb_sys_dict b WHERE b.class_code = #{businessTypeDictClassCode} AND b.state = 'A' AND b.dict_code = t1.business_type) AS business_type_name,
            (SELECT b.activity_type_name FROM tb_schedu_monitor_activity_info b WHERE b.activity_type_code = t1.activity_type LIMIT 1) AS activity_type_name,
            (SELECT b.activity_subtype_name FROM tb_schedu_monitor_activity_info b WHERE b.activity_subtype_code = t1.activity_subtype LIMIT 1)AS activity_subtype_name,
            t2.has_automatic_station
        from tb_schedu_monitor_quantity_target t1
        left join tb_res_site t2 on t1.site_id = t2.id
        where t1.status = 'A'
            <if test="siteIdArr != null  and siteIdArr != ''">
                and t1.site_id in
                    <foreach item="item" collection="siteIdArr" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="siteId != null  and siteId != '' and null == siteIdArr"> and t1.site_id = #{siteId}</if>
            <if test="provinceCode != null  and provinceCode != ''"> and t1.province_code = #{provinceCode}</if>
            <if test="provinceName != null  and provinceName != ''"> and t1.province_name like concat('%', #{provinceName}, '%')</if>
            <if test="cityCode != null  and cityCode != '' and null == cityCodeArr"> and t1.city_code = #{cityCode}</if>
            <if test="cityCodeArr != null  and cityCodeArr != ''">
                and t1.city_code in
                    <foreach item="item" collection="cityCodeArr" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="cityName != null  and cityName != ''"> and t1.city_name like concat('%', #{cityName}, '%')</if>
            <if test="siteName != null  and siteName != ''"> and t1.site_name like concat('%', #{siteName}, '%')</if>
            <if test="businessType != null  and businessType != ''"> and t1.business_type = #{businessType}</if>
            <if test="siteType != null  and siteType != ''"> and t1.site_type = #{siteType}</if>
            <if test="activityType != null  and activityType != ''"> and t1.activity_type = #{activityType}</if>
            <if test="activitySubtype != null  and activitySubtype != ''"> and t1.activity_subtype = #{activitySubtype}</if>
            <if test="targetValue != null  and targetValue != ''"> and t1.target_value = #{targetValue}</if>
            <if test="statYear != null  and statYear != ''"> and t1.stat_year = #{statYear}</if>
            <if test="status != null  and status != ''"> and t1.status = #{status}</if>
            <if test="tenantId != null  and tenantId != ''"> and t1.tenant_id = #{tenantId}</if>
            <if test="packageId != null  and packageId != '' and null == packageIdArr"> and t2.package_id = #{packageId}</if>
            <if test="packageIdArr != null and packageIdArr != ''">
                and t2.package_id in
                    <foreach item="item" collection="packageIdArr" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="hasAutomaticStation != null  and hasAutomaticStation != ''"> and t2.has_automatic_station = #{hasAutomaticStation}</if>
    </select>

    <select id="selectNationwideCollectScheduMonitorQuantityTargetList" parameterType="com.mes.smartdispath.domain.dto.ScheduMonitorQuantityTargetQueryDTO" resultMap="ScheduMonitorQuantityTargetVOResult">
        select
            t1.business_type, t1.site_type, t1.activity_type,t2.has_automatic_station,
            t1.activity_subtype, t1.stat_year,
            t3.dict_value AS site_type_name,
            t4.dict_value AS business_type_name,
            t5.activity_type_name AS activity_type_name,
            t5.activity_subtype_name AS activity_subtype_name,
            sum(t1.target_value) as target_value
        from tb_schedu_monitor_quantity_target t1
            left join tb_res_site t2 on t1.site_id = t2.id
            left join tb_sys_dict t3 on t3.class_code = #{siteTypeDictClassCode} AND t3.state = 'A' AND t3.dict_code = t1.site_type AND t3.parent_dict_code = t1.business_type
            left join tb_sys_dict t4 on t4.class_code = #{businessTypeDictClassCode} AND t4.state = 'A' AND t4.dict_code = t1.business_type
            left join tb_schedu_monitor_activity_info t5 on t5.activity_type_code = t1.activity_type and t5.activity_subtype_code = t1.activity_subtype
        where t1.status = 'A'
        <if test="businessType != null  and businessType != ''"> and t1.business_type = #{businessType}</if>
        <if test="siteType != null  and siteType != ''"> and t1.site_type = #{siteType}</if>
        <if test="activityType != null  and activityType != ''"> and t1.activity_type = #{activityType}</if>
        <if test="activitySubtype != null  and activitySubtype != ''"> and t1.activity_subtype = #{activitySubtype}</if>
        <if test="statYear != null  and statYear != ''"> and t1.stat_year = #{statYear}</if>
        <if test="tenantId != null  and tenantId != ''"> and t1.tenant_id = #{tenantId}</if>
        <if test="packageId != null  and packageId != '' and null == packageIdArr"> and t2.package_id = #{packageId}</if>
        <if test="packageIdArr != null and packageIdArr != ''">
            and t2.package_id in
            <foreach item="item" collection="packageIdArr" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="hasAutomaticStation != null  and hasAutomaticStation != ''"> and t2.has_automatic_station = #{hasAutomaticStation}</if>
        group by
        t1.business_type, t1.site_type, t1.activity_type,t2.has_automatic_station,
        t1.activity_subtype, t1.stat_year,
        t3.dict_value,
        t4.dict_value,
        t5.activity_type_name,
        t5.activity_subtype_name
        order by t1.stat_year desc
    </select>

    <select id="selectProvinceCollectScheduMonitorQuantityTargetList" parameterType="com.mes.smartdispath.domain.dto.ScheduMonitorQuantityTargetQueryDTO" resultMap="ScheduMonitorQuantityTargetVOResult">
        select
        t1.province_code, t1.province_name,
        t1.business_type, t1.site_type, t1.activity_type,t2.has_automatic_station,
        t1.activity_subtype, t1.stat_year,
        t3.dict_value AS site_type_name,
        t4.dict_value AS business_type_name,
        t5.activity_type_name AS activity_type_name,
        t5.activity_subtype_name AS activity_subtype_name,
        sum(t1.target_value) as target_value
        from tb_schedu_monitor_quantity_target t1
            left join tb_res_site t2 on t1.site_id = t2.id
            left join tb_sys_dict t3 on t3.class_code = #{siteTypeDictClassCode} AND t3.state = 'A' AND t3.dict_code = t1.site_type AND t3.parent_dict_code = t1.business_type
            left join tb_sys_dict t4 on t4.class_code = #{businessTypeDictClassCode} AND t4.state = 'A' AND t4.dict_code = t1.business_type
            left join tb_schedu_monitor_activity_info t5 on t5.activity_type_code = t1.activity_type and t5.activity_subtype_code = t1.activity_subtype
        where t1.status = 'A'
        <if test="provinceCode != null  and provinceCode != '' and null == provinceCodeArr"> and t1.province_code = #{provinceCode}</if>
        <if test="provinceCodeArr != null  and provinceCodeArr != ''">
            and t1.province_code in
                <foreach item="item" collection="provinceCodeArr" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
        <if test="cityCode != null  and cityCode != '' and null == cityCodeArr"> and t1.city_code = #{cityCode}</if>
        <if test="cityCodeArr != null  and cityCodeArr != ''">
            and t1.city_code in
            <foreach item="item" collection="cityCodeArr" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="businessType != null  and businessType != ''"> and t1.business_type = #{businessType}</if>
        <if test="siteType != null  and siteType != ''"> and t1.site_type = #{siteType}</if>
        <if test="activityType != null  and activityType != ''"> and t1.activity_type = #{activityType}</if>
        <if test="activitySubtype != null  and activitySubtype != ''"> and t1.activity_subtype = #{activitySubtype}</if>
        <if test="statYear != null  and statYear != ''"> and t1.stat_year = #{statYear}</if>
        <if test="tenantId != null  and tenantId != ''"> and t1.tenant_id = #{tenantId}</if>
        <if test="packageId != null  and packageId != '' and null == packageIdArr"> and t2.package_id = #{packageId}</if>
        <if test="packageIdArr != null and packageIdArr != ''">
            and t2.package_id in
            <foreach item="item" collection="packageIdArr" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="hasAutomaticStation != null  and hasAutomaticStation != ''"> and t2.has_automatic_station = #{hasAutomaticStation}</if>
        group by
        t1.province_code, t1.province_name,
        t1.business_type, t1.site_type, t1.activity_type,t2.has_automatic_station,
        t1.activity_subtype, t1.stat_year,
        t3.dict_value,
        t4.dict_value,
        t5.activity_type_name,
        t5.activity_subtype_name
        order by t1.stat_year desc, t1.province_code asc
    </select>
    
    <select id="selectScheduMonitorQuantityTargetById" parameterType="String" resultMap="ScheduMonitorQuantityTargetResult">
        <include refid="selectScheduMonitorQuantityTargetVo"/>
        where id = #{id}
    </select>

    <insert id="batchInsertScheduMonitorQuantityTarget" parameterType="java.util.List">
        INSERT INTO tb_schedu_monitor_quantity_target (
            province_code,
            province_name,
            city_code,
            city_name,
            site_name,
            site_id,
            site_type,
            business_type,
            activity_type,
            activity_subtype,
            target_value,
            stat_year,
            status,
            tenant_id,
            create_by,
            create_time,
            update_by,
            update_time
        ) VALUES
        <foreach collection="targetList" item="item" separator=",">
            (
                #{item.provinceCode},
                #{item.provinceName},
                #{item.cityCode},
                #{item.cityName},
                #{item.siteName},
                #{item.siteId},
                #{item.siteType},
                #{item.businessType},
                #{item.activityType},
                #{item.activitySubtype},
                #{item.targetValue},
                #{item.statYear},
                'A',
                #{item.tenantId},
                #{item.createBy},
                now(),
                #{item.updateBy},
                now()
            )
        </foreach>
    </insert>

    <insert id="insertScheduMonitorQuantityTarget" parameterType="com.mes.smartdispath.domain.ScheduMonitorQuantityTarget">
        insert into tb_schedu_monitor_quantity_target
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="provinceCode != null and provinceCode != ''">province_code,</if>
            <if test="provinceName != null and provinceName != ''">province_name,</if>
            <if test="cityCode != null and cityCode != ''">city_code,</if>
            <if test="cityName != null and cityName != ''">city_name,</if>
            <if test="siteName != null and siteName != ''">site_name,</if>
            <if test="siteId != null and siteId != ''">site_id,</if>
            <if test="businessType != null and businessType != ''">business_type,</if>
            <if test="siteType != null and siteType != ''">site_type,</if>
            <if test="activityType != null and activityType != ''">activity_type,</if>
            <if test="activitySubtype != null and activitySubtype != ''">activity_subtype,</if>
            <if test="targetValue != null and targetValue != ''">target_value,</if>
            <if test="statYear != null">stat_year,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="tenantId != null">tenant_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="provinceCode != null and provinceCode != ''">#{provinceCode},</if>
            <if test="provinceName != null and provinceName != ''">#{provinceName},</if>
            <if test="cityCode != null and cityCode != ''">#{cityCode},</if>
            <if test="cityName != null and cityName != ''">#{cityName},</if>
            <if test="siteName != null and siteName != ''">#{siteName},</if>
            <if test="siteId != null and siteId != ''">#{siteId},</if>
            <if test="businessType != null and businessType != ''">#{businessType},</if>
            <if test="siteType != null and siteType != ''">#{siteType},</if>
            <if test="activityType != null and activityType != ''">#{activityType},</if>
            <if test="activitySubtype != null and activitySubtype != ''">#{activitySubtype},</if>
            <if test="targetValue != null and targetValue != ''">#{targetValue},</if>
            <if test="statYear != null">#{statYear},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="tenantId != null">#{tenantId},</if>
         </trim>
    </insert>

    <update id="updateScheduMonitorQuantityTarget" parameterType="com.mes.smartdispath.domain.ScheduMonitorQuantityTarget">
        update tb_schedu_monitor_quantity_target
        <trim prefix="SET" suffixOverrides=",">
            <if test="provinceCode != null and provinceCode != ''">province_code = #{provinceCode},</if>
            <if test="provinceName != null and provinceName != ''">province_name = #{provinceName},</if>
            <if test="cityCode != null and cityCode != ''">city_code = #{cityCode},</if>
            <if test="cityName != null and cityName != ''">city_name = #{cityName},</if>
            <if test="siteName != null and siteName != ''">site_name = #{siteName},</if>
            <if test="siteId != null and siteId != ''">site_id = #{siteId},</if>
            <if test="businessType != null and businessType != ''">business_type = #{businessType},</if>
            <if test="siteType != null and siteType != ''">site_type = #{siteType},</if>
            <if test="activityType != null and activityType != ''">activity_type = #{activityType},</if>
            <if test="activitySubtype != null and activitySubtype != ''">activity_subtype = #{activitySubtype},</if>
            <if test="targetValue != null and targetValue != ''">target_value = #{targetValue},</if>
            <if test="statYear != null">stat_year = #{statYear},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = now(),</if>
            <if test="status != null">status = #{status},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScheduMonitorQuantityTargetById" parameterType="String">
        delete from tb_schedu_monitor_quantity_target where id = #{id}
    </delete>

    <delete id="deleteScheduMonitorQuantityTargetByIds" parameterType="String">
        delete from tb_schedu_monitor_quantity_target where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>