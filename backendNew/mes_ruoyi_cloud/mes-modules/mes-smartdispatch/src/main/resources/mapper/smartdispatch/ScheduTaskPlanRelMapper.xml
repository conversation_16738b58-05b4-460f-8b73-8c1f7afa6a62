<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduTaskPlanRelMapper">

    <resultMap type="com.mes.smartdispath.domain.ScheduTaskPlanRel" id="ScheduTaskPlanRelResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="planId"    column="plan_id"    />
        <result property="planCode"    column="plan_code"    />
        <result property="planName"    column="plan_name"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <resultMap type="com.mes.smartdispath.domain.vo.SiteTaskStaticVO" id="SiteTaskStaticVOResult">
        <result property="annualTaskCount"    column="annual_task_count"    />
        <result property="monthlyTaskCount"    column="monthly_task_count"    />
        <result property="dailyTaskCount"    column="daily_task_count"    />
        <result property="inProgressTaskCount"    column="in_progress_task_count"    />
        <result property="notStartedTaskCount"    column="not_started_task_count"    />
    </resultMap>

    <sql id="selectScheduTaskPlanRelVo">
        select id, task_id, task_code, task_name, plan_id, plan_code, plan_name, status, create_time, create_by, update_time, update_by from tb_schedu_task_plan_rel
    </sql>

    <select id="selectScheduTaskPlanRelList" parameterType="com.mes.smartdispath.domain.ScheduTaskPlanRel" resultMap="ScheduTaskPlanRelResult">
        <include refid="selectScheduTaskPlanRelVo"/>
        <where>
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="planId != null  and planId != ''"> and plan_id = #{planId}</if>
            <if test="planCode != null  and planCode != ''"> and plan_code = #{planCode}</if>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectScheduTaskPlanRelById" parameterType="String" resultMap="ScheduTaskPlanRelResult">
        <include refid="selectScheduTaskPlanRelVo"/>
        where id = #{id}
    </select>

    <select id="selectTaskStaticBySiteId" parameterType="String" resultMap="SiteTaskStaticVOResult">
        SELECT
            COUNT(DISTINCT CASE WHEN YEAR(b.start_time) = YEAR(CURRENT_DATE) THEN b.id END) AS annual_task_count,
            COUNT(DISTINCT CASE WHEN YEAR(b.start_time) = YEAR(CURRENT_DATE) AND MONTH(b.start_time) = MONTH(CURRENT_DATE) THEN b.id END) AS monthly_task_count,
            COUNT(DISTINCT CASE WHEN TO_CHAR(b.start_time, 'YYYY-MM-DD') = TO_CHAR(CURRENT_DATE, 'YYYY-MM-DD') THEN b.id END) AS daily_task_count,
            COUNT(DISTINCT CASE WHEN b.task_status = 3 THEN b.id END) AS in_progress_task_count,
            COUNT(DISTINCT CASE WHEN b.task_status IN (1, 2) THEN b.id END) AS not_started_task_count
        FROM
            tb_schedu_task_plan_rel a
        JOIN
            tb_schedu_task_info b ON a.task_id = b.id
        JOIN
            tb_schedu_plan_info c ON a.plan_id = c.id
        WHERE
            c.site_id = #{siteId}
            AND b.start_time <![CDATA[<=]]> TO_DATE(TO_CHAR(CURRENT_DATE, 'YYYY') || '-12-31', 'YYYY-MM-DD')
            AND b.start_time >= TO_DATE(TO_CHAR(CURRENT_DATE, 'YYYY') || '-01-01', 'YYYY-MM-DD')
    </select>

    <insert id="insertScheduTaskPlanRel" parameterType="com.mes.smartdispath.domain.ScheduTaskPlanRel">
        insert into tb_schedu_task_plan_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null">task_name,</if>
            <if test="planId != null">plan_id,</if>
            <if test="planCode != null and planCode != ''">plan_code,</if>
            <if test="planName != null">plan_name,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="planId != null">#{planId},</if>
            <if test="planCode != null and planCode != ''">#{planCode},</if>
            <if test="planName != null">#{planName},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduTaskPlanRel" parameterType="java.util.List">
        insert into tb_schedu_task_plan_rel (
            task_id,
            task_code,
            task_name,
            plan_id,
            plan_code,
            plan_name,
            status,
            create_time,
            create_by,
            update_time,
            update_by
        ) values
        <foreach collection="list" item="item" separator=",">
        (
            #{item.taskId},
            #{item.taskCode},
            #{item.taskName},
            #{item.planId},
            #{item.planCode},
            #{item.planName},
            'A',
            now(),
            #{item.createBy},
            now(),
            #{item.updateBy}
        )
        </foreach>
    </insert>

    <update id="updateScheduTaskPlanRel" parameterType="com.mes.smartdispath.domain.ScheduTaskPlanRel">
        update tb_schedu_task_plan_rel
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="planCode != null and planCode != ''">plan_code = #{planCode},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduTaskPlanRel" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_task_plan_rel
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.taskId != null">
                    task_id = #{item.taskId},
                </if>
                <if test="item.taskCode != null and item.taskCode != ''">
                    task_code = #{item.taskCode},
                </if>
                <if test="item.taskName != null">
                    task_name = #{item.taskName},
                </if>
                <if test="item.planId != null">
                    plan_id = #{item.planId},
                </if>
                <if test="item.planCode != null and item.planCode != ''">
                    plan_code = #{item.planCode},
                </if>
                <if test="item.planName != null">
                    plan_name = #{item.planName},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime},
                </if>
                <if test="item.createBy != null">
                    create_by = #{item.createBy},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.updateBy != null">
                    update_by = #{item.updateBy},
                </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduTaskPlanRelById" parameterType="String">
        delete from tb_schedu_task_plan_rel where id = #{id}
    </delete>

    <delete id="deleteScheduTaskPlanRelByIds" parameterType="String">
        delete from tb_schedu_task_plan_rel where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>