<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduAlgorithmInfoMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduAlgorithmInfo" id="ScheduAlgorithmInfoResult">
        <result property="id"    column="id"    />
        <result property="algorithmCode"    column="algorithm_code"    />
        <result property="algorithmName"    column="algorithm_name"    />
        <result property="algCronRule"    column="alg_cron_rule"    />
        <result property="algDesc"    column="alg_desc"    />
        <result property="algStatus"    column="alg_status"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <sql id="selectScheduAlgorithmInfoVo">
        select id, algorithm_code, algorithm_name, alg_cron_rule, alg_desc, alg_status, is_delete, create_by, create_time, tenant_id from tb_schedu_algorithm_info
    </sql>

    <select id="selectScheduAlgorithmInfoList" parameterType="com.mes.smartdispath.domain.ScheduAlgorithmInfo" resultMap="ScheduAlgorithmInfoResult">
        <include refid="selectScheduAlgorithmInfoVo"/>
        where
            is_delete = 0
            <if test="algorithmCode != null  and algorithmCode != ''"> and algorithm_code like concat('%', #{algorithmCode}, '%')</if>
            <if test="algorithmName != null  and algorithmName != ''"> and algorithm_name like concat('%', #{algorithmName}, '%')</if>
            <if test="algCronRule != null  and algCronRule != ''"> and alg_cron_rule = #{algCronRule}</if>
            <if test="algDesc != null  and algDesc != ''"> and alg_desc = #{algDesc}</if>
            <if test="algStatus != null  and algStatus != ''"> and alg_status = #{algStatus}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
    </select>
    
    <select id="selectScheduAlgorithmInfoById" parameterType="String" resultMap="ScheduAlgorithmInfoResult">
        <include refid="selectScheduAlgorithmInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduAlgorithmInfo" parameterType="com.mes.smartdispath.domain.ScheduAlgorithmInfo">
        insert into tb_schedu_algorithm_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="algorithmCode != null">algorithm_code,</if>
            <if test="algorithmName != null">algorithm_name,</if>
            <if test="algCronRule != null">alg_cron_rule,</if>
            <if test="algDesc != null">alg_desc,</if>
            <if test="algStatus != null">alg_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="tenantId != null">tenant_id,</if>
            create_time,
            is_delete,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="algorithmCode != null">#{algorithmCode},</if>
            <if test="algorithmName != null">#{algorithmName},</if>
            <if test="algCronRule != null">#{algCronRule},</if>
            <if test="algDesc != null">#{algDesc},</if>
            <if test="algStatus != null">#{algStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="tenantId != null">#{tenantId},</if>
            now(),
            0,
         </trim>
    </insert>

    <update id="updateScheduAlgorithmInfo" parameterType="com.mes.smartdispath.domain.ScheduAlgorithmInfo">
        update tb_schedu_algorithm_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="algorithmCode != null">algorithm_code = #{algorithmCode},</if>
            <if test="algorithmName != null">algorithm_name = #{algorithmName},</if>
            <if test="algCronRule != null">alg_cron_rule = #{algCronRule},</if>
            <if test="algDesc != null">alg_desc = #{algDesc},</if>
            <if test="algStatus != null">alg_status = #{algStatus},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScheduAlgorithmInfoById" parameterType="String">
        delete from tb_schedu_algorithm_info where id = #{id}
    </delete>

    <delete id="deleteScheduAlgorithmInfoByIds" parameterType="String">
        delete from tb_schedu_algorithm_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>