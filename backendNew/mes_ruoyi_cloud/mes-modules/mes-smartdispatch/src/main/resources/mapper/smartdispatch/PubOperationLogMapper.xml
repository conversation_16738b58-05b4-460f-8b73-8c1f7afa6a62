<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.PubOperationLogMapper">
    
    <resultMap type="com.mes.smartdispath.domain.PubOperationLog" id="PubOperationLogResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="ip"    column="ip"    />
        <result property="type"    column="type"    />
        <result property="module"    column="module"    />
        <result property="content"    column="content"    />
        <result property="state"    column="state"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectPubOperationLogVo">
        select id, name, ip, type, module, content, state, create_time, create_by, update_time, update_by from tb_pub_operation_log
    </sql>

    <select id="selectPubOperationLogList" parameterType="com.mes.smartdispath.domain.PubOperationLog" resultMap="PubOperationLogResult">
        <include refid="selectPubOperationLogVo"/>
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="module != null  and module != ''"> and module = #{module}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
        </where>
    </select>
    
    <select id="selectPubOperationLogById" parameterType="String" resultMap="PubOperationLogResult">
        <include refid="selectPubOperationLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertPubOperationLog" parameterType="com.mes.smartdispath.domain.PubOperationLog">
        insert into tb_pub_operation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="ip != null">ip,</if>
            <if test="type != null">type,</if>
            <if test="module != null">module,</if>
            <if test="content != null">content,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="ip != null">#{ip},</if>
            <if test="type != null">#{type},</if>
            <if test="module != null">#{module},</if>
            <if test="content != null">#{content},</if>
            <if test="state != null">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updatePubOperationLog" parameterType="com.mes.smartdispath.domain.PubOperationLog">
        update tb_pub_operation_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="type != null">type = #{type},</if>
            <if test="module != null">module = #{module},</if>
            <if test="content != null">content = #{content},</if>
            <if test="state != null">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePubOperationLogById" parameterType="String">
        delete from tb_pub_operation_log where id = #{id}
    </delete>

    <delete id="deletePubOperationLogByIds" parameterType="String">
        delete from tb_pub_operation_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>