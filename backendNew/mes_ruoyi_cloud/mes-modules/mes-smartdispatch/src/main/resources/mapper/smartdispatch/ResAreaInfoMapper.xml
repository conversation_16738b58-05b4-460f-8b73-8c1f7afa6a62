<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ResAreaInfoMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ResAreaInfo" id="ResAreaInfoResult">
        <result property="id"    column="id"    />
        <result property="areaCode"    column="area_code"    />
        <result property="areaName"    column="area_name"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="areaType"    column="area_type"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectResAreaInfoVo">
        select id, area_code, area_name, parent_code, area_type, status, create_time, update_time from tb_res_area_info
    </sql>

    <select id="selectProvinceCityRegionList" parameterType="com.mes.smartdispath.domain.ResAreaInfo" resultMap="ResAreaInfoResult">
        <include refid="selectResAreaInfoVo"/>
        where area_type <![CDATA[<]]> 3
    </select>

    <select id="selectResAreaInfoList" parameterType="com.mes.smartdispath.domain.ResAreaInfo" resultMap="ResAreaInfoResult">
        <include refid="selectResAreaInfoVo"/>
        <where>  
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="parentCode != null  and parentCode != ''"> and parent_code = #{parentCode}</if>
            <if test="areaType != null  and areaType != ''"> and area_type = #{areaType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectResAreaInfoById" parameterType="String" resultMap="ResAreaInfoResult">
        <include refid="selectResAreaInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertResAreaInfo" parameterType="com.mes.smartdispath.domain.ResAreaInfo">
        insert into tb_res_area_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="areaCode != null and areaCode != ''">area_code,</if>
            <if test="areaName != null and areaName != ''">area_name,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="areaType != null and areaType != ''">area_type,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="areaCode != null and areaCode != ''">#{areaCode},</if>
            <if test="areaName != null and areaName != ''">#{areaName},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="areaType != null and areaType != ''">#{areaType},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateResAreaInfo" parameterType="com.mes.smartdispath.domain.ResAreaInfo">
        update tb_res_area_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="areaCode != null and areaCode != ''">area_code = #{areaCode},</if>
            <if test="areaName != null and areaName != ''">area_name = #{areaName},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="areaType != null and areaType != ''">area_type = #{areaType},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteResAreaInfoById" parameterType="String">
        delete from tb_res_area_info where id = #{id}
    </delete>

    <delete id="deleteResAreaInfoByIds" parameterType="String">
        delete from tb_res_area_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>