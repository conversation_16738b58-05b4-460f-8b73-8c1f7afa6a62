<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduLabTaskRecordMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduLabTaskRecord" id="ScheduLabTaskRecordResult">
        <result property="id"    column="id"    />
        <result property="laboratoryName"    column="laboratory_name"    />
        <result property="laboratoryCode"    column="laboratory_code"    />
        <result property="executorId"    column="executor_id"    />
        <result property="executorName"    column="executor_name"    />
        <result property="phone"    column="phone"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskCode"    column="task_code"    />
        <result property="taskName"    column="task_name"    />
        <result property="planCode"    column="plan_code"    />
        <result property="planName"    column="plan_name"    />
        <result property="collectPara"    column="collect_para"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="sendStatus"    column="send_status"    />
    </resultMap>

    <sql id="selectScheduLabTaskRecordVo">
        select id, laboratory_name, laboratory_code, executor_id, executor_name, phone, task_id, task_code, task_name, plan_code, plan_name, collect_para, create_time, update_time, send_status from tb_schedu_lab_task_record
    </sql>

    <select id="selectScheduLabTaskRecordList" parameterType="com.mes.smartdispath.domain.ScheduLabTaskRecord" resultMap="ScheduLabTaskRecordResult">
        <include refid="selectScheduLabTaskRecordVo"/>
        <where>  
            <if test="laboratoryName != null  and laboratoryName != ''"> and laboratory_name like concat('%', #{laboratoryName}, '%')</if>
            <if test="laboratoryCode != null  and laboratoryCode != ''"> and laboratory_code = #{laboratoryCode}</if>
            <if test="executorId != null  and executorId != ''"> and executor_id = #{executorId}</if>
            <if test="executorName != null  and executorName != ''"> and executor_name like concat('%', #{executorName}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="taskCode != null  and taskCode != ''"> and task_code = #{taskCode}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="planCode != null  and planCode != ''"> and plan_code = #{planCode}</if>
            <if test="planName != null  and planName != ''"> and plan_name like concat('%', #{planName}, '%')</if>
            <if test="collectPara != null  and collectPara != ''"> and collect_para = #{collectPara}</if>
            <if test="sendStatus != null  and sendStatus != ''"> and send_status = #{sendStatus}</if>
        </where>
    </select>
    
    <select id="selectScheduLabTaskRecordById" parameterType="String" resultMap="ScheduLabTaskRecordResult">
        <include refid="selectScheduLabTaskRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduLabTaskRecord" parameterType="com.mes.smartdispath.domain.ScheduLabTaskRecord">
        insert into tb_schedu_lab_task_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="laboratoryName != null and laboratoryName != ''">laboratory_name,</if>
            <if test="laboratoryCode != null and laboratoryCode != ''">laboratory_code,</if>
            <if test="executorId != null and executorId != ''">executor_id,</if>
            <if test="executorName != null and executorName != ''">executor_name,</if>
            <if test="phone != null">phone,</if>
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="taskCode != null and taskCode != ''">task_code,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="planCode != null">plan_code,</if>
            <if test="planName != null">plan_name,</if>
            <if test="collectPara != null and collectPara != ''">collect_para,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sendStatus != null">send_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="laboratoryName != null and laboratoryName != ''">#{laboratoryName},</if>
            <if test="laboratoryCode != null and laboratoryCode != ''">#{laboratoryCode},</if>
            <if test="executorId != null and executorId != ''">#{executorId},</if>
            <if test="executorName != null and executorName != ''">#{executorName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="taskCode != null and taskCode != ''">#{taskCode},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="planCode != null">#{planCode},</if>
            <if test="planName != null">#{planName},</if>
            <if test="collectPara != null and collectPara != ''">#{collectPara},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sendStatus != null">#{sendStatus},</if>
         </trim>
    </insert>

    <update id="updateScheduLabTaskRecord" parameterType="com.mes.smartdispath.domain.ScheduLabTaskRecord">
        update tb_schedu_lab_task_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="laboratoryName != null and laboratoryName != ''">laboratory_name = #{laboratoryName},</if>
            <if test="laboratoryCode != null and laboratoryCode != ''">laboratory_code = #{laboratoryCode},</if>
            <if test="executorId != null and executorId != ''">executor_id = #{executorId},</if>
            <if test="executorName != null and executorName != ''">executor_name = #{executorName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="taskCode != null and taskCode != ''">task_code = #{taskCode},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="planCode != null">plan_code = #{planCode},</if>
            <if test="planName != null">plan_name = #{planName},</if>
            <if test="collectPara != null and collectPara != ''">collect_para = #{collectPara},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sendStatus != null">send_status = #{sendStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScheduLabTaskRecordById" parameterType="String">
        delete from tb_schedu_lab_task_record where id = #{id}
    </delete>

    <delete id="deleteScheduLabTaskRecordByIds" parameterType="String">
        delete from tb_schedu_lab_task_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>