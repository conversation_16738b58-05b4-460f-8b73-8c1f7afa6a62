<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduTaskAvgDurationMapper">
    
    <resultMap type="com.mes.smartdispath.domain.ScheduTaskAvgDuration" id="ScheduTaskAvgDurationResult">
        <result property="id"    column="id"    />
        <result property="siteId"    column="site_id"    />
        <result property="siteName"    column="site_name"    />
        <result property="activityTypeCode"    column="activity_type_code"    />
        <result property="activityTypeName"    column="activity_type_name"    />
        <result property="avgDuration"    column="avg_duration"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectScheduTaskAvgDurationVo">
        select id, site_id, site_name, activity_type_code, activity_type_name, avg_duration, create_time, update_time from tb_schedu_task_avg_duration
    </sql>

    <select id="selectScheduTaskAvgDurationList" parameterType="com.mes.smartdispath.domain.ScheduTaskAvgDuration" resultMap="ScheduTaskAvgDurationResult">
        <include refid="selectScheduTaskAvgDurationVo"/>
        <where>  
            <if test="siteId != null  and siteId != ''"> and site_id = #{siteId}</if>
            <if test="siteName != null  and siteName != ''"> and site_name like concat('%', #{siteName}, '%')</if>
            <if test="activityTypeCode != null  and activityTypeCode != ''"> and activity_type_code = #{activityTypeCode}</if>
            <if test="activityTypeName != null  and activityTypeName != ''"> and activity_type_name like concat('%', #{activityTypeName}, '%')</if>
            <if test="avgDuration != null  and avgDuration != ''"> and avg_duration = #{avgDuration}</if>
        </where>
    </select>
    
    <select id="selectScheduTaskAvgDurationById" parameterType="String" resultMap="ScheduTaskAvgDurationResult">
        <include refid="selectScheduTaskAvgDurationVo"/>
        where id = #{id}
    </select>

    <insert id="insertScheduTaskAvgDuration" parameterType="com.mes.smartdispath.domain.ScheduTaskAvgDuration">
        insert into tb_schedu_task_avg_duration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="siteId != null">site_id,</if>
            <if test="siteName != null">site_name,</if>
            <if test="activityTypeCode != null">activity_type_code,</if>
            <if test="activityTypeName != null">activity_type_name,</if>
            <if test="avgDuration != null">avg_duration,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="siteId != null">#{siteId},</if>
            <if test="siteName != null">#{siteName},</if>
            <if test="activityTypeCode != null">#{activityTypeCode},</if>
            <if test="activityTypeName != null">#{activityTypeName},</if>
            <if test="avgDuration != null">#{avgDuration},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateScheduTaskAvgDuration" parameterType="com.mes.smartdispath.domain.ScheduTaskAvgDuration">
        update tb_schedu_task_avg_duration
        <trim prefix="SET" suffixOverrides=",">
            <if test="siteId != null">site_id = #{siteId},</if>
            <if test="siteName != null">site_name = #{siteName},</if>
            <if test="activityTypeCode != null">activity_type_code = #{activityTypeCode},</if>
            <if test="activityTypeName != null">activity_type_name = #{activityTypeName},</if>
            <if test="avgDuration != null">avg_duration = #{avgDuration},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteScheduTaskAvgDurationById" parameterType="String">
        delete from tb_schedu_task_avg_duration where id = #{id}
    </delete>

    <delete id="deleteScheduTaskAvgDurationByIds" parameterType="String">
        delete from tb_schedu_task_avg_duration where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>