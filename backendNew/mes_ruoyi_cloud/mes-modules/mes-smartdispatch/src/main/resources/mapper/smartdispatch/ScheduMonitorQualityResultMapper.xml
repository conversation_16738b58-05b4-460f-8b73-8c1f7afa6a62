<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mes.smartdispath.mapper.ScheduMonitorQualityResultMapper">

    <resultMap type="com.mes.smartdispath.domain.ScheduMonitorQualityResult" id="ScheduMonitorQualityResultResult">
            <result property="id" column="id"/>
            <result property="provinceCode" column="province_code"/>
            <result property="cityCode" column="city_code"/>
            <result property="siteId" column="site_id"/>
            <result property="siteCode" column="site_code"/>
            <result property="businessType" column="business_type"/>
            <result property="monitIndex" column="monit_index"/>
            <result property="accuracy" column="accuracy"/>
            <result property="precision" column="precision"/>
            <result property="effectivenessRate" column="effectiveness_rate"/>
            <result property="captureRate" column="capture_rate"/>
            <result property="quactrlPassRate" column="quactrl_pass_rate"/>
            <result property="tp" column="tp"/>
            <result property="dt" column="dt"/>
            <result property="statYear" column="stat_year"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="status" column="status"/>
            <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectScheduMonitorQualityResultVo">
        select id, province_code, city_code, site_id, site_code, business_type, monit_index, accuracy, precision, effectiveness_rate, capture_rate, quactrl_pass_rate, tp, dt, stat_year, create_by, create_time, update_by, update_time, status, tenant_id
        from tb_schedu_monitor_quality_result
    </sql>

    <select id="selectScheduMonitorQualityResultList" parameterType="com.mes.smartdispath.domain.ScheduMonitorQualityResult"
            resultMap="ScheduMonitorQualityResultResult">
        <include refid="selectScheduMonitorQualityResultVo"/>
        <where>
                        <if test="provinceCode != null  and provinceCode != ''">
                            and province_code = #{provinceCode}
                        </if>
                        <if test="cityCode != null  and cityCode != ''">
                            and city_code = #{cityCode}
                        </if>
                        <if test="siteId != null  and siteId != ''">
                            and site_id = #{siteId}
                        </if>
                        <if test="siteCode != null  and siteCode != ''">
                            and site_code = #{siteCode}
                        </if>
                        <if test="businessType != null  and businessType != ''">
                            and business_type = #{businessType}
                        </if>
                        <if test="monitIndex != null  and monitIndex != ''">
                            and monit_index = #{monitIndex}
                        </if>
                        <if test="accuracy != null  and accuracy != ''">
                            and accuracy = #{accuracy}
                        </if>
                        <if test="precision != null  and precision != ''">
                            and precision = #{precision}
                        </if>
                        <if test="effectivenessRate != null  and effectivenessRate != ''">
                            and effectiveness_rate = #{effectivenessRate}
                        </if>
                        <if test="captureRate != null  and captureRate != ''">
                            and capture_rate = #{captureRate}
                        </if>
                        <if test="quactrlPassRate != null  and quactrlPassRate != ''">
                            and quactrl_pass_rate = #{quactrlPassRate}
                        </if>
                        <if test="tp != null  and tp != ''">
                            and tp = #{tp}
                        </if>
                        <if test="dt != null  and dt != ''">
                            and dt = #{dt}
                        </if>
                        <if test="statYear != null  and statYear != ''">
                            and stat_year = #{statYear}
                        </if>
                        <if test="status != null  and status != ''">
                            and status = #{status}
                        </if>
                        <if test="tenantId != null  and tenantId != ''">
                            and tenant_id = #{tenantId}
                        </if>
        </where>
    </select>

    <select id="selectCollectMonitorQualityResultList" parameterType="com.mes.smartdispath.domain.dto.MonitorQualityResultQueryDTO"
            resultMap="ScheduMonitorQualityResultResult">
        SELECT
            province_code,
            city_code,
            site_id,
            stat_year,
            business_type,
            monit_index,
            ROUND(AVG(accuracy),2) AS accuracy,
            ROUND(AVG(precision),2) AS precision,
            ROUND(AVG(effectiveness_rate),2) AS effectiveness_rate,
            ROUND(AVG(capture_rate),2) AS capture_rate,
            ROUND(AVG(quactrl_pass_rate),2) AS quactrl_pass_rate
        FROM tb_schedu_monitor_quality_result
        <where>
            <if test="statYearArr != null  and statYearArr != ''">
                and stat_year in
                    <foreach collection="statYearArr" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="provinceCodeArr != null  and provinceCodeArr != ''">
                and province_code in
                <foreach collection="provinceCodeArr" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cityCodeArr != null  and cityCodeArr != ''">
                and city_code in
                <foreach collection="cityCodeArr" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="siteIdArr != null  and siteIdArr != ''">
                and site_id in
                <foreach collection="siteIdArr" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="monitIndexArr != null  and monitIndexArr != ''">
                and monit_index in
                <foreach collection="monitIndexArr" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
            province_code,
            city_code,
            site_id,
            stat_year,
            business_type,
            monit_index
    </select>

    <select id="selectStatYearCollectMonitorQualityResultList" parameterType="com.mes.smartdispath.domain.dto.MonitorQualityResultQueryDTO"
            resultMap="ScheduMonitorQualityResultResult">
        select
            t1.business_type, t1.monit_index,
            t1.stat_year,
            ROUND(AVG(t1.accuracy),2) as accuracy,
            ROUND(AVG(t1.precision),2) as precision,
            ROUND(AVG(t1.effectiveness_rate),2) as effectiveness_rate,
            ROUND(AVG(t1.capture_rate),2) as capture_rate,
            ROUND(AVG(t1.quactrl_pass_rate),2) as quactrl_pass_rate
        from tb_schedu_monitor_quality_result t1
            left join tb_res_site t2 on t1.site_id = t2.id
        where
            t1.status = 'A'
            and t1.business_type = #{businessType}
            and t1.stat_year = #{statYear}
            <if test="provinceCodeArr != null  and provinceCodeArr != ''">
                and t1.province_code in
                <foreach item="item" collection="provinceCodeArr" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="maintainUnitCodeArr != null  and maintainUnitCodeArr != ''">
                and t2.operation_unit in
                <foreach item="item" collection="maintainUnitCodeArr" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        group by t1.business_type, t1.monit_index, t1.stat_year
    </select>

    <select id="selectScheduMonitorQualityResultById" parameterType="String"
            resultMap="ScheduMonitorQualityResultResult">
            <include refid="selectScheduMonitorQualityResultVo"/>
            where id = #{id}
    </select>

    <insert id="insertScheduMonitorQualityResult" parameterType="com.mes.smartdispath.domain.ScheduMonitorQualityResult">
        insert into tb_schedu_monitor_quality_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,
                    </if>
                    <if test="provinceCode != null and provinceCode != ''">province_code,
                    </if>
                    <if test="cityCode != null and cityCode != ''">city_code,
                    </if>
                    <if test="siteId != null and siteId != ''">site_id,
                    </if>
                    <if test="siteCode != null and siteCode != ''">site_code,
                    </if>
                    <if test="businessType != null and businessType != ''">business_type,
                    </if>
                    <if test="monitIndex != null and monitIndex != ''">monit_index,
                    </if>
                    <if test="accuracy != null">accuracy,
                    </if>
                    <if test="precision != null">precision,
                    </if>
                    <if test="effectivenessRate != null">effectiveness_rate,
                    </if>
                    <if test="captureRate != null">capture_rate,
                    </if>
                    <if test="quactrlPassRate != null">quactrl_pass_rate,
                    </if>
                    <if test="tp != null">tp,
                    </if>
                    <if test="dt != null">dt,
                    </if>
                    <if test="statYear != null">stat_year,
                    </if>
                    <if test="createBy != null">create_by,
                    </if>
                    <if test="createTime != null">create_time,
                    </if>
                    <if test="updateBy != null">update_by,
                    </if>
                    <if test="updateTime != null">update_time,
                    </if>
                    <if test="status != null">status,
                    </if>
                    <if test="tenantId != null">tenant_id,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="provinceCode != null and provinceCode != ''">#{provinceCode},
                    </if>
                    <if test="cityCode != null and cityCode != ''">#{cityCode},
                    </if>
                    <if test="siteId != null and siteId != ''">#{siteId},
                    </if>
                    <if test="siteCode != null and siteCode != ''">#{siteCode},
                    </if>
                    <if test="businessType != null and businessType != ''">#{businessType},
                    </if>
                    <if test="monitIndex != null and monitIndex != ''">#{monitIndex},
                    </if>
                    <if test="accuracy != null">#{accuracy},
                    </if>
                    <if test="precision != null">#{precision},
                    </if>
                    <if test="effectivenessRate != null">#{effectivenessRate},
                    </if>
                    <if test="captureRate != null">#{captureRate},
                    </if>
                    <if test="quactrlPassRate != null">#{quactrlPassRate},
                    </if>
                    <if test="tp != null">#{tp},
                    </if>
                    <if test="dt != null">#{dt},
                    </if>
                    <if test="statYear != null">#{statYear},
                    </if>
                    <if test="createBy != null">#{createBy},
                    </if>
                    <if test="createTime != null">#{createTime},
                    </if>
                    <if test="updateBy != null">#{updateBy},
                    </if>
                    <if test="updateTime != null">#{updateTime},
                    </if>
                    <if test="status != null">#{status},
                    </if>
                    <if test="tenantId != null">#{tenantId},
                    </if>
        </trim>
    </insert>

    <!-- 批量插入模板 -->
    <insert id="batchInsertScheduMonitorQualityResult" parameterType="java.util.List">
        insert into tb_schedu_monitor_quality_result (
                id,
                province_code,
                city_code,
                site_id,
                site_code,
                business_type,
                monit_index,
                accuracy,
                precision,
                effectiveness_rate,
                capture_rate,
                quactrl_pass_rate,
                tp,
                dt,
                stat_year,
                create_by,
                create_time,
                update_by,
                update_time,
                status,
                tenant_id
        ) values
        <foreach collection="list" item="item" separator=",">
        (
                #{item.id},
                #{item.provinceCode},
                #{item.cityCode},
                #{item.siteId},
                #{item.siteCode},
                #{item.businessType},
                #{item.monitIndex},
                #{item.accuracy},
                #{item.precision},
                #{item.effectivenessRate},
                #{item.captureRate},
                #{item.quactrlPassRate},
                #{item.tp},
                #{item.dt},
                #{item.statYear},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.status},
                #{item.tenantId}
        )
        </foreach>
    </insert>

    <update id="updateScheduMonitorQualityResult" parameterType="com.mes.smartdispath.domain.ScheduMonitorQualityResult">
        update tb_schedu_monitor_quality_result
        <trim prefix="SET" suffixOverrides=",">
                    <if test="provinceCode != null and provinceCode != ''">province_code =
                        #{provinceCode},
                    </if>
                    <if test="cityCode != null and cityCode != ''">city_code =
                        #{cityCode},
                    </if>
                    <if test="siteId != null and siteId != ''">site_id =
                        #{siteId},
                    </if>
                    <if test="siteCode != null and siteCode != ''">site_code =
                        #{siteCode},
                    </if>
                    <if test="businessType != null and businessType != ''">business_type =
                        #{businessType},
                    </if>
                    <if test="monitIndex != null and monitIndex != ''">monit_index =
                        #{monitIndex},
                    </if>
                    <if test="accuracy != null">accuracy =
                        #{accuracy},
                    </if>
                    <if test="precision != null">precision =
                        #{precision},
                    </if>
                    <if test="effectivenessRate != null">effectiveness_rate =
                        #{effectivenessRate},
                    </if>
                    <if test="captureRate != null">capture_rate =
                        #{captureRate},
                    </if>
                    <if test="quactrlPassRate != null">quactrl_pass_rate =
                        #{quactrlPassRate},
                    </if>
                    <if test="tp != null">tp =
                        #{tp},
                    </if>
                    <if test="dt != null">dt =
                        #{dt},
                    </if>
                    <if test="statYear != null">stat_year =
                        #{statYear},
                    </if>
                    <if test="createBy != null">create_by =
                        #{createBy},
                    </if>
                    <if test="createTime != null">create_time =
                        #{createTime},
                    </if>
                    <if test="updateBy != null">update_by =
                        #{updateBy},
                    </if>
                    <if test="updateTime != null">update_time =
                        #{updateTime},
                    </if>
                    <if test="status != null">status =
                        #{status},
                    </if>
                    <if test="tenantId != null">tenant_id =
                        #{tenantId},
                    </if>
        </trim>
        where id = #{id}
    </update>

    <!-- 批量更新模板（保持原样即可） -->
    <update id="batchUpdateScheduMonitorQualityResult" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tb_schedu_monitor_quality_result
            <trim prefix="SET" suffixOverrides=",">
                        <if test="item.provinceCode != null and item.provinceCode != ''">
                            province_code = #{item.provinceCode},
                        </if>
                        <if test="item.cityCode != null and item.cityCode != ''">
                            city_code = #{item.cityCode},
                        </if>
                        <if test="item.siteId != null and item.siteId != ''">
                            site_id = #{item.siteId},
                        </if>
                        <if test="item.siteCode != null and item.siteCode != ''">
                            site_code = #{item.siteCode},
                        </if>
                        <if test="item.businessType != null and item.businessType != ''">
                            business_type = #{item.businessType},
                        </if>
                        <if test="item.monitIndex != null and item.monitIndex != ''">
                            monit_index = #{item.monitIndex},
                        </if>
                        <if test="item.accuracy != null">
                            accuracy = #{item.accuracy},
                        </if>
                        <if test="item.precision != null">
                            precision = #{item.precision},
                        </if>
                        <if test="item.effectivenessRate != null">
                            effectiveness_rate = #{item.effectivenessRate},
                        </if>
                        <if test="item.captureRate != null">
                            capture_rate = #{item.captureRate},
                        </if>
                        <if test="item.quactrlPassRate != null">
                            quactrl_pass_rate = #{item.quactrlPassRate},
                        </if>
                        <if test="item.tp != null">
                            tp = #{item.tp},
                        </if>
                        <if test="item.dt != null">
                            dt = #{item.dt},
                        </if>
                        <if test="item.statYear != null">
                            stat_year = #{item.statYear},
                        </if>
                        <if test="item.createBy != null">
                            create_by = #{item.createBy},
                        </if>
                        <if test="item.createTime != null">
                            create_time = #{item.createTime},
                        </if>
                        <if test="item.updateBy != null">
                            update_by = #{item.updateBy},
                        </if>
                        <if test="item.updateTime != null">
                            update_time = #{item.updateTime},
                        </if>
                        <if test="item.status != null">
                            status = #{item.status},
                        </if>
                        <if test="item.tenantId != null">
                            tenant_id = #{item.tenantId},
                        </if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteScheduMonitorQualityResultById" parameterType="String">
        delete
        from tb_schedu_monitor_quality_result where id = #{id}
    </delete>

    <delete id="deleteScheduMonitorQualityResultByIds" parameterType="String">
        delete from tb_schedu_monitor_quality_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>